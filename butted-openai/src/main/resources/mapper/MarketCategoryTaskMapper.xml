<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.butted.openai.mapper.MarketCategoryTaskMapper">
    <select id="findPage" resultType="tech.tiangong.butted.openai.vo.resp.MarketCategoryTaskVo"
            parameterType="tech.tiangong.butted.openai.vo.query.MarketCategoryTaskQuery">
        SELECT *
        FROM market_category_task t
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="query.orderBys != null and query.orderBys.size() > 0">
            ORDER BY
            <foreach collection="query.orderBys" separator="," item="order">
                ${order.sortBy}
            </foreach>
        </if>
    </select>

    <select id="listByQuery" resultType="tech.tiangong.butted.openai.vo.resp.MarketCategoryTaskVo"
            parameterType="tech.tiangong.butted.openai.vo.query.MarketCategoryTaskQuery">
        SELECT *
        FROM market_category_task t
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="query.orderBys != null and query.orderBys.size() > 0">
            ORDER BY
            <foreach collection="query.orderBys" separator="," item="order">
                ${order.sortBy}
            </foreach>
        </if>
    </select>


    <sql id="queryCondition">
        <if test="query.deleted!=null">
            AND t.deleted = #{query.deleted}
        </if>
        <if test="query.taskId!=null">
            AND t.task_id = #{query.taskId}
        </if>
        <if test="query.taskStatus!=null">
            AND t.task_status = #{query.taskStatus}
        </if>
        <if test="query.taskStatusList!=null and query.taskStatusList.size()>0">
            AND t.task_status IN
            <foreach collection="query.taskStatusList" open="(" separator="," close=")" item="taskStatus">
                #{taskStatus}
            </foreach>
        </if>
        <if test="query.taskProgress!=null">
            AND t.task_progress = #{query.taskProgress}
        </if>
        <if test="query.pushStatus!=null">
            AND t.push_status = #{query.pushStatus}
        </if>
        <if test="query.pushTimes!=null">
            AND t.push_times = #{query.pushTimes}
        </if>
        <if test="query.pullTimes!=null">
            AND t.pull_times = #{query.pullTimes}
        </if>
        <if test="query.tenantId!=null">
            AND t.tenant_id = #{query.tenantId}
        </if>
        <if test="query.creatorId!=null">
            AND t.creator_id = #{query.creatorId}
        </if>
        <if test="query.creatorName!=null and query.creatorName!=''">
            AND t.creator_name Like CONCAT('%',#{query.creatorName},'%')
        </if>
        <if test="query.afterCreatedTime!=null">
            AND t.created_time &gt; #{query.afterCreatedTime}
        </if>
    </sql>
</mapper>

