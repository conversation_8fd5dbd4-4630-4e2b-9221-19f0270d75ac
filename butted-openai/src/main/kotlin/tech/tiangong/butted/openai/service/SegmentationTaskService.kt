package tech.tiangong.butted.openai.service

import tech.tiangong.butted.openai.vo.req.SegmentationTaskReq
import tech.tiangong.butted.openai.vo.resp.SegmentationTaskVo

/**
 * 佐糖图片识别分割服务接口
 *
 * <AUTHOR>
 */
interface SegmentationTaskService {

    /**
     * 创建
     * @param req SegmentationTaskReq
     */
    fun create(req: SegmentationTaskReq): Long
    fun detail(taskId: Long): SegmentationTaskVo

}
