package tech.tiangong.butted.openai.vo.resp

import java.io.Serial
import java.io.Serializable

/**
 * 虚拟换装生成图Vo
 *
 * <AUTHOR>
 */
data class DressUpOutputVo(
    /**
     * 输出ID
     */
    var outputId: Long? = null,

    /**
     * 任务ID
     */
    var taskId: Long? = null,

    /**
     * 生成图
     */
    var resImg: String? = null,

    /**
     * 序号
     */
    var serialNum: Int? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
