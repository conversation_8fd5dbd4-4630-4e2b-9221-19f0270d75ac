ALTER TABLE `shift_face_task`
    MODIFY COLUMN `third_task_id` varchar(80) NULL DEFAULT NULL COMMENT '第三方任务ID' AFTER `task_id`;

ALTER TABLE `shift_model_task`
    MODIFY COLUMN `third_task_id` varchar(80) NULL DEFAULT NULL COMMENT '第三方任务ID' AFTER `task_id`;

ALTER TABLE `shift_scene_task`
    MODIFY COLUMN `third_task_id` varchar(80) NULL DEFAULT NULL COMMENT '第三方任务ID' AFTER `task_id`;

ALTER TABLE `partition_task`
    MODIFY COLUMN `third_task_id` varchar(80) NULL DEFAULT NULL COMMENT '第三方任务ID' AFTER `task_id`;

ALTER TABLE `task_log`
    MODIFY COLUMN `task_id` varchar(80) NULL DEFAULT NULL COMMENT '任务ID' AFTER `request_id`;

ALTER TABLE `shift_scene_task`
    ADD COLUMN `bg_output_size` varchar(32) NULL COMMENT '背景图片输出尺寸：\r\n1比1：传one_to_one\r\n原尺寸：传original' AFTER `bg_encode`;

ALTER TABLE `try_on_task`
    MODIFY COLUMN `scene_picture_output_size` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '场景图片输出尺寸：\r\n1比1：传one_to_one\r\n原尺寸：传original' AFTER `scene_picture_path`;

ALTER TABLE `shift_face_task`
    ADD COLUMN `face_encode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参考人脸编码（各算法平台自定义）' AFTER `face_imgs`;


ALTER TABLE `smart_design_task`
    DROP COLUMN `red_book_style`;

ALTER TABLE `smart_identify_task`
    DROP COLUMN `task_mini_status`,
    DROP COLUMN `identify_type`;

ALTER TABLE `task_log`
    ADD INDEX `idx_request_id`(`request_id`);


ALTER TABLE `design_material_task`
    MODIFY COLUMN `model_img_info` json NULL COMMENT '模特图（作废）' AFTER `bg_img_info`,
    ADD COLUMN `model_id` bigint NULL DEFAULT NULL COMMENT '模特（面容）ID（模特素材ID）' AFTER `model_img_info`,
    ADD COLUMN `model_code` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模特编码（字典编码）' AFTER `model_id`,
    ADD COLUMN `model_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模特名称' AFTER `model_code`,
    ADD COLUMN `model_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模特（面容）图片URL' AFTER `model_name`,
    ADD COLUMN `model_caption` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模特（面容）图片描述' AFTER `model_url`;


UPDATE `try_on_task` SET `mode_code` = 'chaoji', `mode_name` = '潮际' WHERE `mode_code` = '10';
UPDATE `try_on_task` SET `mode_code` = 'chaoji', `mode_name` = '潮际' WHERE `algorithms` = 'cj';


CREATE TABLE `cropping_task` (
    `task_id` bigint unsigned NOT NULL COMMENT '任务ID',
    `bus_id` bigint DEFAULT NULL COMMENT '业务主键ID',
    `bus_code` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务编号',
    `task_status` int NOT NULL DEFAULT '0' COMMENT '任务状态：0-排队中；10-生成中；20-已中止；30-已完成；50-失败；60-超时失败；',
    `task_progress` int DEFAULT '0' COMMENT '任务进度',
    `rank_position` int DEFAULT NULL COMMENT '排队位置',
    `input_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '输入图片URL',
    `size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裁剪后的图像大小，默认：1400x1400 （宽×高）',
    `gen_count` int NOT NULL COMMENT '生成数量count',
    `res_imgs` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成图（多张逗号,隔开）',
    `res_origin_imgs` json DEFAULT NULL COMMENT '原始生成图json数组',
    `push_status` tinyint unsigned DEFAULT '0' COMMENT '推送状态：0-未推送；1-已推送；2-推送失败',
    `push_time` datetime DEFAULT NULL COMMENT '推送时间',
    `push_times` int DEFAULT '0' COMMENT '推送次数',
    `pull_time` datetime DEFAULT NULL COMMENT '拉取时间',
    `pull_times` int DEFAULT '0' COMMENT '拉取次数',
    `sync_status` tinyint DEFAULT '1' COMMENT '同步给业务状态：0-未同步；1-已同步',
    `sync_time` datetime DEFAULT NULL COMMENT '同步时间',
    `sync_times` int DEFAULT '0' COMMENT '同步次数',
    `sync_fail_times` int DEFAULT '0' COMMENT '同步失败次数',
    `ai_start_time` datetime DEFAULT NULL COMMENT 'AI开始处理时间',
    `ai_end_time` datetime DEFAULT NULL COMMENT 'AI结束处理时间',
    `message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '信息备注',
    `algorithms` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '算法：cj-潮际；jv-杉缔',
    `task_attribute` tinyint DEFAULT NULL COMMENT '任务属性：0-单任务，1-批量任务',
    `callback` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '回调URL',
    `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
    `creator_id` bigint unsigned DEFAULT NULL COMMENT '创建人ID',
    `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人姓名',
    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
    `reviser_id` bigint unsigned DEFAULT NULL COMMENT '更新人ID',
    `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人姓名',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`task_id`) USING BTREE,
    UNIQUE KEY `uniq_bus_id` (`bus_id`),
    KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='裁剪任务';