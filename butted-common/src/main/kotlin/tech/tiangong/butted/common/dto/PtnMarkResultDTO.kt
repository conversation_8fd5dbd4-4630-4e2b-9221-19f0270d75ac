package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable

/**
 * ptn_mark结果DTO
 *
 * <AUTHOR>
 * @date       ：2025/4/23 16:51
 * @version    :1.0
 */
class PtnMarkResultDTO(
    @JsonProperty(value = "status")
    var status: String? = null,
    @JsonProperty(value = "error_msg")
    var errorMsg: String? = null,
    @JsonProperty(value = "inference_result")
    val inferenceResult: PtnMarkInferenceResultDTO? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1078170999693424655L
    }
}