package tech.tiangong.butted.common.enums

/**
 * 智能AI任务模型枚举类（FG_modelVersion）
 */
enum class SmartDesignModeEnum(
    val code: String,
    val aiCode: Int,
    val desc: String,
) {
    FG2_0("FG2_0", 0, "FG2.0"),
    FG2_0_RED_BOOK("FG2_0_RED_BOOK", 1, "FG2.0小红书"),
    FG2_0_RED_BOOK2("FG2_0_RED_BOOK2", 2, "FG2.0小红书2"),
    FG2_0_SEA("FG2_0_SEA", 3, "FG2.0 东南亚模型"),
    ;


    companion object {
        fun isRedBook(code: String?): <PERSON><PERSON><PERSON> {
            return FG2_0_RED_BOOK.code == code
        }

        fun isRedBook2(code: String?): <PERSON><PERSON><PERSON> {
            return FG2_0_RED_BOOK2.code == code
        }
    }
}