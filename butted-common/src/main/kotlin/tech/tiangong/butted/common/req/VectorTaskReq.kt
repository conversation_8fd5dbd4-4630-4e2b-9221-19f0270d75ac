package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import java.io.Serial

/**
 * 图片向量任务Req
 *
 * <AUTHOR>
 */
data class VectorTaskReq(

    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,

    /**
     * 业务编号
     */
    var busCode: String? = null,

    /**
     * 输入图片URL
     */
    @field:NotBlank(message = "输入图片URL不能空")
    var inputImage: String? = null,

    /**
     * 是否异步获取结果，异步为true,同步为false，默认异步
     */
    var async: Boolean? = true,

    /**
     * 回调URL,异步时必填
     */
    //@field:NotBlank(message = "回调URL不能空")
    var callback: String? = null,


    ) : BaseTenantUserReq() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
