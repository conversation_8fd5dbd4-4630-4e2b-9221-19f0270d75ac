package tech.tiangong.butted.common.vo.category

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.common.vo.label.MarketPredLabelsVo

/**
 * 营销品类识别任务Vo
 *
 * <AUTHOR>
 */
data class MarketCategoryTaskVo(

    /**
     * 识别标签结果
     */
    var predLabelsInfo: MarketPredLabelsVo?=null


    ) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
