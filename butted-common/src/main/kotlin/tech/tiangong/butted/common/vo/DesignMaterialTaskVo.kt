package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.DurationBaseTaskVo
import tech.tiangong.butted.common.vo.base.RefImgVo
import java.io.Serial

/**
 * AI素材任务Vo
 *
 * <AUTHOR>
 */
data class DesignMaterialTaskVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,

    /**
     * 业务编号
     */
    var busCode: String? = null,

    /**
     * Clip任务ID
     */
    var clipTaskId: Long? = null,

    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: Int? = null,

    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 品类编号
     */
    var categoryCode: String? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,
    /**
     * 背景（场景）图
     */
    var bgImgObj: RefImgVo? = null,
    /**
     * 模特图
     */
    var modelImgObj: RefImgVo? = null,
    /**
     * 模特（面容）ID（模特素材ID）
     */
    var modelId: Long? = null,

    /**
     * 模特编码（字典编码）
     */
    var modelCode: String? = null,

    /**
     * 模特（面容）名称
     */
    var modelName: String? = null,

    /**
     * 模特（面容）图片URL
     */
    var modelUrl: String? = null,

    /**
     * 模特（面容）图片描述
     */
    var modelCaption: String? = null,

    /**
     * 是否生成多姿势，1-是，0-否
     */
    var multiPose: Int? = null,

    /**
     * 角度：正面-FRONT,背面-BACK
     */
    var viewpoint: String? = null,

    /**
     * 随机种子，固定的话每次生成图都一样
     */
    var seed: Long? = null,

    /**
     * 生成图尺寸，宽高，默认1024x1024
     */
    var size: String? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,

    /**
     * 生成描述词
     */
    var prompts: String? = null,

    /**
     * 模型版本
     */
    var modelVersion: String? = null,
//    /**
//     * 款式，面料，花型，风格等标签
//     */
//    var labelList: List<PredLabelVo>? = null,
    /**
     * 提交的clip标签
     */
    var submitClipLabelList: List<PredLabelVo>? = null,
    /**
     * 生成图列表
     */
    var resImgList: List<ResGroupRepairImgVo>? = null,

    ) : DurationBaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
