package tech.tiangong.butted.common.req

import java.io.Serializable
import jakarta.validation.constraints.*

/**
 * 面料重新推荐请求
 *
 * <AUTHOR>
 */
data class ReRecommendFabricReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 智能开款ID
     */
    @field:NotNull(message = "智能开款ID不能空")
    var smartDevelopId: Long? = null,

    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    var callback: String? = null,


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
