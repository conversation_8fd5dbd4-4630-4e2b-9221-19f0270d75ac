package tech.tiangong.butted.common.dto

/**
 * 矩形坐标Dto
 */
data class RectangleCoordinateDto(
    /**
     * X坐标起点
     */
    var xmin: Int,
    /**
     * Y坐标起点
     */
    var ymin: Int,
    /**
     * X坐标终点
     */
    var xmax: Int,
    /**
     * Y坐标终点
     */
    var ymax: Int,

    ) {
    companion object {
        private const val serialVersionUID = 1L
    }

    fun toRegion(): List<Int> {
        return mutableListOf<Int>(xmin, ymin, xmax, ymax)
    }
}

