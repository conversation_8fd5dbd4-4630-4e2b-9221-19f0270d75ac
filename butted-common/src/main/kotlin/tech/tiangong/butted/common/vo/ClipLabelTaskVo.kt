package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.LabelBaseTaskVo

/**
 * 提取标签任务Vo
 *
 * <AUTHOR>
 */
data class ClipLabelTaskVo(
    /**
     * 父任务ID
     */
    var parentTaskId: Long? = null,
    /**
     * 识别品类名称
     */
    var category: String? = null,
    /**
     * 识别品类编号
     */
    var categoryCode: String? = null,
    /**
     * 扩展处理:1-风格,2-花型识别,3-多姿势,4-面料识别及推荐,5-花型提取,6-场景,7-模特,8-Try换装
     */
    var extendActions: String? = null,
    /**
     * 图片类型：0-非真人模特图、1-真人模特图
     */
    var inputImageType: Int? = null,
    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: Int? = null,
    /**
     * 有效平铺：0-否；1-是
     */
    var usefulFlat: Int? = null,

    /**
     * 预测的标签字符串（前端不可用）
     */
    var predLabels: String? = null,

    ) : LabelBaseTaskVo() {

    companion object {
        private const val serialVersionUID = 1L
    }
}
