package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable

/**
 * ptn_mark结果DTO
 *
 * <AUTHOR>
 * @date       ：2025/4/23 16:35
 * @version    :1.0
 */
class PtnMarkInferenceResultDTO(

    /**
     * 描述
     */
    @JsonProperty(value = "dec")
    var dec: List<String> = listOf(),

    /**
     * 风格
     */
    @JsonProperty(value = "style")
    var style: List<String> = listOf(),

    /**
     * 主题
     */
    @JsonProperty(value = "topic")
    var topic: List<String> = listOf(),

    /**
     * 文字内容
     */
    @JsonProperty(value = "content")
    var content: List<String> = listOf(),

    /**
     * 图案元素
     */
    @JsonProperty(value = "element")
    var element: List<String> = listOf(),

    /**
     * 绘图手法
     */
    @JsonProperty(value = "draw_tech")
    var drawTech: List<String> = listOf(),
    /**
     * 展示类型
     */
    @JsonProperty(value = "show_type")
    var showType: List<String> = listOf(),
    /**
     * 印花类型
     */
    @JsonProperty(value = "print_type")
    var printType: List<String> = listOf(),
    /**
     * 工艺
     */
    @JsonProperty(value = "technology")
    var technology: List<String> = listOf(),
    /**
     * 侵权标识
     */
    @JsonProperty(value = "infringement")
    var infringement: List<String> = listOf(),
    /**
     * 品牌标识
     */
    @JsonProperty(value = "brand_identity")
    var brandIdentity: List<String> = listOf(),
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1078170999693424655L
    }
}