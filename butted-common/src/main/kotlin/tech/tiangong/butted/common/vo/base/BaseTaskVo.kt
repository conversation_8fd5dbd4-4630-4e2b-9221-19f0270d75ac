package tech.tiangong.butted.common.vo.base

import com.fasterxml.jackson.annotation.JsonFormat
import java.io.Serializable
import java.time.LocalDateTime

/**
 * 任务BaseVO
 */
open class BaseTaskVo(
    /**
     * 任务ID
     */
    var taskId: Long? = null,
    /**
     * 任务状态：0-排队中；10-生成中；20-已中止；30-已完成；50-失败；60-超时失败；
     * @see com.tiangong.butted.common.enums.TaskStatusEnum
     */
    var taskStatus: Int? = null,
    /**
     * 任务进度0-100
     */
    var taskProgress: Int? = null,
    /**
     * 排队位置
     */
    var rankPosition: Int? = null,
    /**
     * 消息备注
     */
    var message: String? = null,
    /**
     * 处理失败模型
     */
    var failTaskMode: String? = null,

    /**
     * AI开始处理时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var aiStartTime: LocalDateTime? = null,
    /**
     * AI结束处理时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var aiEndTime: LocalDateTime? = null,
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var createdTime: LocalDateTime? = null,
    /**
     * 推送时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var pushTime: LocalDateTime? = null,


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
