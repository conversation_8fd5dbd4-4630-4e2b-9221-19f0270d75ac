package tech.tiangong.butted.common.vo

import java.io.Serializable
import java.math.BigDecimal

/**
 * 推荐中台面料
 */
data class FabricRecommendSkuVo(
    /**
     * 家族代表名称
     */
    var familyName: String? = null,
    /**
     * 家族代表分数
     */
    var familyScore: BigDecimal? = null,
    /**
     * 家族代表面料类目
     */
    var familyFabricCategory: String? = null,
    /**
     * 中台主商品ID
     */
    var sourceCommodityId: Long? = null,
    /**
     * 商品ID
     */
    val commodityId: Long? = null,
    /**
     * 商品编码
     */
    val commodityCode: String? = null,
    /**
     * 商品名称
     */
    val commodityName: String? = null,
    /**
     * 商品图片
     */
    val commodityPicture: String? = null,
    /**
     * 纹理色块图
     */
    var colorPicture: String? = null,
    /**
     * SKU-ID
     */
    val skuId: Long? = null,
    /**
     * SKU-编码
     */
    val skuCode: String? = null,
    /**
     * 色号
     */
    val colorCode: String? = null,
    /**
     * SKU色块图RGB值
     */
    val rgb: String? = null,

    ) : Serializable {
    companion object {
        const val serialVersionUID = 1L
    }

}