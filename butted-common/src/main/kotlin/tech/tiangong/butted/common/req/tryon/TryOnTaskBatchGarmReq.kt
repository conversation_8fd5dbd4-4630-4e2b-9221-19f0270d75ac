package tech.tiangong.butted.common.req.tryon

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 虚拟换装批量任务服装图Req
 *
 * <AUTHOR>
 */
data class TryOnTaskBatchGarmReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 服装图
     */
    @field:NotBlank(message = "服装图不能空")
    var garmImgUrl: String? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
