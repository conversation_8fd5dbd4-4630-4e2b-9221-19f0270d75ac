package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 *  mllm_chat任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class MllmChatTaskVo(
    /**
     * 提示词
     */
    var prompt: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 文本回答
     */
    var responseText: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -1720887894583414217L
    }


}