package tech.tiangong.butted.common.req.base;

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.io.Serial
import java.io.Serializable

/**
 * FM场景图编码Req
 *
 * <AUTHOR>
 */
data class ModelMaterialEncodeReq(
    /**
     * 模型编码（字典编码）
     */
    @field:NotBlank(message = "模型编码不能为空")
    var modeCode: String? = null,
    /**
     * 模型名称（字典名称）
     */
    @field:NotBlank(message = "模型名称不能为空")
    var modeName: String? = null,
    /**
     * 场景图编码
     */
    @field:NotBlank(message = "场景图编码不能为空")
    @field:Size(max = 255, message = "场景图编码不能超过255个字符")
    var encode: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
