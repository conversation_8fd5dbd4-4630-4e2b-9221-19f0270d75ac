package tech.tiangong.butted.common.req

import tech.tiangong.butted.common.dto.CoordinateDto
import java.io.Serial
import java.io.Serializable
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 用户自定义参数区域
 *
 * <AUTHOR>
 * @date       ：2025/1/6 18:05
 * @version    :1.0
 */
class CustomRegionReq(
    @field:NotNull(message = "中心坐标不能为空")
    val center: CoordinateDto,
    @field:Valid
    @field:NotEmpty(message = "坐标不能为空")
    val coordinates: List<CoordinateDto>
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -3018243412355604390L
    }
}