package tech.tiangong.butted.common.vo.base

import team.aikero.blade.core.toolkit.isNotBlank
import java.io.Serializable

/**
 * 参考图
 */
class RefImgVo(
    /**
     * 图片url
     */
    var url: String? = null,
    /**
     * 图片文本描述
     */
    var captions: String? = null,
) : Serializable {

    fun valid(): Boolean {
        return url.isNotBlank()
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}