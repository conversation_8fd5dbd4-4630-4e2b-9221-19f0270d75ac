package tech.tiangong.butted.common.req;

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.BizSourceEnum
import java.io.Serializable

/**
 * 图片描述说明任务Req
 *
 * <AUTHOR>
 */
data class PictureCaptionSourceReq(
    /**
     * 图片来源
     */
    @field:NotNull(message = "图片来源不能为空")
    var source: BizSourceEnum? = null,
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 输入图片
     */
    @field:NotBlank(message = "输入图片不能为空")
    var inputImg: String? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) : Serializable {
    companion object {
        @java.io.Serial
        private const val serialVersionUID: Long = 1L
    }
}
