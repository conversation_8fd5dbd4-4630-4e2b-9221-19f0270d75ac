package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 特征点信息
 *```json
 * {
 *          "CoordPhy" : "NzZcitnsAGJERf7zdq3SqIbaJOAxAfRiZmrfnS2T1wvLxace3d3dhg/SjJyYbPwmiwZdEd8KYCSuoGcEMzrQaAX1o0rsZwS+N4a/hgAiYPjG7pxI5g0AXlHD7zGrHUxCoH9H3uHFzw33cfdOXRXVDqigdq44QIORbxpaa7gC1er1iyi/gYjB24FPF+abqa/cL5ebct9b5l3zi+N/mIaDRQs0dmBF4Sz+Cd4+eyjkR428H0zrkFLFJcplWa3eoF7fZpuizO831U488u6PG5bWVnl7fN9VGPTrOUjwpP2JfwDonK2cvYdgRTjSlWczRWznRvEppL09gM1gqrdKagyhDw==",
 *          "CoordPix" : {
 *             "X" : 922,
 *             "Y" : 515
 *          },
 *          "name" : "Point_5"
 *       }
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 16:16
 * @version    :1.0
 */
class FeaturePointDto(
    @field:NotEmpty(message = "特征点名称名称不能为空") val name: String,
    @JsonProperty(value = "CoordPix")
    @field:NotNull(message = "特征点像素坐标不能为空") val coordPix: CoordinateIntDto,
//    @JsonProperty(value = "CoordPhy")
//    @field:NotNull(message = "特征点物理坐标不能为空") val coordPhy:
    @JsonProperty(value = "CoordPhy")
    @field:NotNull(message = "特征点物理坐标不能为空") val coordPhy: String,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 4486944267017237720L
    }
}