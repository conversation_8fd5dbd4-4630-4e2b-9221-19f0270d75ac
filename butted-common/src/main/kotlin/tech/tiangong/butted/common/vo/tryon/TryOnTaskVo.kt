package tech.tiangong.butted.common.vo.tryon

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import jakarta.validation.constraints.NotBlank
import tech.tiangong.butted.common.vo.PredLabelVo
import tech.tiangong.butted.common.vo.ResShiftImgVo

/**
 * 虚拟换装任务Vo
 *
 * <AUTHOR>
 */
data class TryOnTaskVo(
    /**
     * clip任务ID
     */
    var clipTaskId: Long? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 品类名
     */
    var category: String? = null,

    /**
     * 上装图
     */
    var upperClothImg: String? = null,
    /**
     * 上装Mask图
     */
    var upperClothMaskImg: String? = null,

    /**
     * 下装图
     */
    var lowerClothImg: String? = null,
    /**
     * 下装Mask图
     */
    var lowerClothMaskImg: String? = null,

    /**
     * 全身（连体）图
     */
    var overallClothImg: String? = null,
    /**
     * 全身（连体）Mask图
     */
    var overallClothMaskImg: String? = null,

    /**
     * 模特参考图
     */
    @field:NotBlank(message = "模特参考图不能空")
    var humanImgUrl: String? = null,

    /**
     * 模特参考图重绘区域
     */
    //@field:NotBlank(message = "模特参考图重绘区域不能空")
    var humanMaskImgUrl: String? = null,


    /**
     * 换脸：0-否；1-是
     */
    var shiftFace: Int? = null,
    /**
     * 换脸模特头像图列表
     */
    var shiftFaceImgList: List<String>? = null,
    /**
     * 生成图列表
     */
    var resImgList: List<ResShiftImgVo>? = null,
    /**
     * 预测的标签列表
     */
    var predLabelList: List<PredLabelVo>? = null,

    ) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID = 1L
    }
}
