package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.DurationBaseTaskVo

/**
 * 花型提取VO
 */
data class FlowerPatternExtractTaskVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 提取区域：1-上半身；2-下半身；3-全身
     */
    var extractRegion: Int? = null,

    /**
     * 提取花型图片结果图列表
     */
    var resImgList: List<String>? = null,

//    /**
//     * 从衣服切出来的原始patch(跟resImgList:一一对应)（作废）
//     */
//    var oriPatchList: List<String>? = null,

    ) : DurationBaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
