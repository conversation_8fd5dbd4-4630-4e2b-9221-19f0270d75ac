package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 预测标签VO
 */
data class PredLabelVo(
    /**
     * 中文标签
     */
    var cn: LabelValueVo? = null,
    /**
     * 英文标签
     */
    var en: LabelValueVo? = null,
    /**
     * coloro的编码，非颜色标签不存在此字段；多个颜色用逗号分隔
     */
    var coloroCodes: String? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }

    fun valid(): Boolean {
        return cn?.valid() == true || en?.valid() == true
    }

    fun validIncludeCode(): Boolean {
        return cn?.validIncludeCode() == true || en?.validIncludeCode() == true
    }

}