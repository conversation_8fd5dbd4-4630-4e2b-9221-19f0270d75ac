package tech.tiangong.butted.common.req

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 灵感设计Req
 */
data class InspirationDesignReq(
    /**
     * 智能识别ID
     */
    @field:NotNull(message = "智能识别ID不能空")
    var smartIdentifyId: Long? = null,
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,

    /**
     * 参考图url
     */
    @field:NotBlank(message = "参考图url不能为空")
    var refImgUrl: String? = null,
    /**
     * 品类名称
     */
    @field:NotBlank(message = "品类名称不能为空")
    var category: String? = null,
    /**
     * 是否生成多姿势，1-是，0-否
     */
    @field:NotNull(message = "是否生成多姿势不能为空")
    var multiPose: Int? = null,
    /**
     * 背景增强：0-否；1-是
     */
    @field: NotNull(message = "背景增强不能为空")
    var filterBack: Int? = null,
    /**
     * 脸部修复：0-否；1-是
     */
    @field: NotNull(message = "脸部修复不能为空")
    var faceRepair: Int? = null,
    /**
     * 背景图列表
     */
    var bgImgInfoList: List<RefImgReq>? = null,
    /**
     * 模特图列表
     */
    var modelImgInfoList: List<RefImgReq>? = null,
    /**
     * 生成图片数量
     */
    @field:NotNull(message = "生成图片数量不能为空")
    @field:Min(value = 1, message = "生成图片数量不能小于1")
    @field:Max(value = 10, message = "生成图片数量不能大于10")
    var genCount: Int? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) {
    companion object {
        private const val serialVersionUID = 1L
    }

}
