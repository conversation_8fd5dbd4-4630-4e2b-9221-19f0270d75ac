package tech.tiangong.butted.common.req;

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.BizSourceEnum
import java.io.Serial
import java.io.Serializable

/**
 * 图片描述说明任务Req
 *
 * <AUTHOR>
 */
data class PictureCaptionSourceBatchReq(
    /**
     * 图片来源
     */
    @field:NotNull(message = "图片来源不能为空")
    var source: BizSourceEnum? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,
    /**
     * 输入图片列表
     */
    @field:Valid
    @field:NotEmpty(message = "输入图片不能为空")
    var pictureList: List<PictureCaptionBatchItemReq>? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}

