package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * CAD信息Mapping
 *```json
 * {
 *    "FeaturePoint" : [
 *       {
 *          "CoordPhy" : "NzZcitnsAGJERf7zdq3SqIbaJOAxAfRiZmrfnS2T1wvLxace3d3dhg/SjJyYbPwmiwZdEd8KYCSuoGcEMzrQaAX1o0rsZwS+N4a/hgAiYPjG7pxI5g0AXlHD7zGrHUxCoH9H3uHFzw33cfdOXRXVDqigdq44QIORbxpaa7gC1er1iyi/gYjB24FPF+abqa/cL5ebct9b5l3zi+N/mIaDRQs0dmBF4Sz+Cd4+eyjkR428H0zrkFLFJcplWa3eoF7fZpuizO831U488u6PG5bWVnl7fN9VGPTrOUjwpP2JfwDonK2cvYdgRTjSlWczRWznRvEppL09gM1gqrdKagyhDw==",
 *          "CoordPix" : {
 *             "X" : 922,
 *             "Y" : 515
 *          },
 *          "name" : "Point_5"
 *       },
 *       {
 *          "CoordPhy" : "QyGgnARq6pyjwKWbVkDo+P0XO5z+kI9hXb5aPNoiv+Ej6zANVljIzQJ9clVtGwGQTgmZ9oJ1T/TlKHcP0uXG59viOaNu74hVedHP8e8bL7I8y8TFrhdJZVMhaPWYZCDVLDRk+xZMI1EdmHtFLGYvThBh4kMNEbSL7v4rGQzo33rQbpNc39yEHg4MAndk3jCbHBkfbAAPI0gDvlQL9Z1/NHlFKCGqln6PS1Ln94KxAZ7gkp9fhHyNNnLRQK/rz7jr7syffn0kzGzWc4f6s/GKxh70hbxfzAvjn63xo0rGzlIkRGobyrhznORAiYN2JhvOSWU6Sf7XPNkKUlahTG4TSQ==",
 *          "CoordPix" : {
 *             "X" : 602,
 *             "Y" : 1055
 *          },
 *          "name" : "Point_R04"
 *       },
 *       {
 *          "CoordPhy" : "TjZYJfBppK1mFxigzc93YbVHC8HDOdVaA1LD2eW/phETJta1eItut5+6NoL8kuBbCB8uwtRftAKjvGbXBzoXwBSWiWWrMXXYu2oR7bXMmyMiySKYBRVVkmapq+Yp1oMMLyfzT/VsaRWPfrKKQSvUB6HwXGdA92nJL9X7MjHJoTfRjFVSXs6xzCBk5TQqBO9EzEmN6Jt3pmbDF8O2p1EZuROEVJFtmEq39BZwbd5nF3hv3dVqJXRxU9Bj2jJGO62gSs6IuvFmR0kq+D4AJFRdjc3vo4cM2Ur8KAVTLw7pjfufesHTrjs9aOy9qC0YEQKiXAVUnwrD1AS6f/UNhbw8aw==",
 *          "CoordPix" : {
 *             "X" : 605,
 *             "Y" : 171
 *          },
 *          "name" : "Point_R02"
 *       },
 *       {
 *          "CoordPhy" : "AwuKAv3bJGF6IlnHEgEMQrv+BD7bMStxb9o5nf/hcpBGbQVDmBaXYtV/tEQidaZovkb73faTtcmXUPeX778im9dx3V0Ypnhq9W/awLIYifDWJe5oIltqwBmaofDr5VXDIhufrel6ySWkZvcQK2AQEd1Uv1hwehcmd3LXbONrXkWWGgF9wLIV2XX1ya5Q2pppauWEAkVhKyykU8oBSlSoMMsUmri4b4QSp3StfBlizFE2iGcXV61A1I9jMcaS2cch0fucTkqASz1IgAXlkn54ZF0rm98U085gvBOquAtakRQ9NQzNQlCzPIiuHJ+xK7Jttq455fJ4JlbJQTIyJMATKw==",
 *          "CoordPix" : {
 *             "X" : 602,
 *             "Y" : 1073
 *          },
 *          "name" : "Point_3"
 *       },
 *       {
 *          "CoordPhy" : "c+jCrfnw25AFg5ykB1Xk7XDWFqiJbN0oyD36kp1QHre0Dl+EFcZy6BLJqlAPgB5w3cvo6wmglXuO6bHw5e1P5IuB0VIW0EVyr+5EF4ozeJjF90f1AiuWX6BubLN92Z2a1ryLJcNS1wC4KpIF7XG5m1NKWNyBsP4kS1V4elhVFUF7z6s+vK4S59bX9+zB54SgBm9B3WgED3UOKB4A+Eym6By7oZoT3fPwo2J9qVP0dtlACvkpBjsVMfdrEFwyyRY+5vjn6kyCf1TqzBKRaBrEryaauzuc7EIJ3Zy9Nc9skPJBTLxNjNScm7vEV+t1XGSjDNml6cfwaFouB0YLrQ0fHQ==",
 *          "CoordPix" : {
 *             "X" : 609,
 *             "Y" : 245
 *          },
 *          "name" : "Point_1"
 *       },
 *       {
 *          "CoordPhy" : "H4v6y+3kpQkvSIzHGlnMidVRexm+K/ZC2GbawQBtjl3GdjatUJ0tqKAnLyyMAkirjmXaKqolJVRp5c6C1PUBInZK1hU4Xtz9G0zSHNV1Ko1/d/VH99JvSuAJxwueKZ8HrsBogfb5mWThlGySJbI6KwHPtob9L6Bf2s8EkFiLm0KFJAxHt5Y+2UIZy8WcNreZHoG4ltJYw2NGDQhVwKJ/ory7X6P2H0q1D8QHJd2X85uc1qajbtn1eQwth3gGCmaygEIxi50vmY01RZ09OsIo40Sp0ERGTEkxiVxd80liWjyO9KbTDPPzlXWuX93oBGQw9F3Yoiu60pZGbYIAf3CGjw==",
 *          "CoordPix" : {
 *             "X" : 287,
 *             "Y" : 236
 *          },
 *          "name" : "Point_2"
 *       },
 *       {
 *          "CoordPhy" : "D2UzwuaDBR+8PmMzMmjWyIQ8FatVx4+JQtlwzm26271N8FA4PSI5hfs+nB+VpAcnSjyCv1iJD6PwdF8wvaiAZzDD6nYWB3vmBBFgguWo/SHKCY1lM2Vv8l50ue3nEqwVdjT+dlXaBj4H5dezNFPsqO241oGPBfeGYzlTxXPnP0c5N/eT0LLasgqkhSdamaOHCP+XZczpwQVoXUrV0kqXoOaUNIEksrKuLJF49FEhkQQ3F2EIbKtkxq/8pb99Y4OtinMqW542WKNcL3Ru6OPSqMhBoocOggYHNNEvVQWVBfLVXEfAt6DLbuJRZg/lCHS7WSKQL/NlHdzUsESMTdIhxw==",
 *          "CoordPix" : {
 *             "X" : 261,
 *             "Y" : 508
 *          },
 *          "name" : "Point_4"
 *       },
 *       {
 *          "CoordPhy" : "Rt/pWSlKJsV/IebtQlhNb+68ffdWQ/TY6yDlkGfkODP+vBU42oVPYzSJefZs/KiCDjRTaZIaiWI1qE2/xUy67JMmy3A9cbiRV+TzO2f4HBDWpHU2xxR24e3k+IXNAK9Yy9QgBEsORN/n98I8NsNkECMIV3PUPTRRmG2V+HSqX3FYti8MEfVdP4nnh8emk9jPTjT/p6pAMT/ZZ7ogQQvKxA6IukCtdjfhumuT45qk5pJCDfO4jjQOFaVegMnwxMTv7Mj5aYwRE6CRtTTSoSsDhVreZYk/OCQ7fRKR/Nkvpt2rs1fvo5TNj24JNHwi1vnuVB1uD2yzxBwzLFIyS7NGdg==",
 *          "CoordPix" : {
 *             "X" : 259,
 *             "Y" : 1055
 *          },
 *          "name" : "Point_6"
 *       },
 *       {
 *          "CoordPhy" : "Z1OgHK/FeKAPiGG44SDO2Hng6cuSaI5cbP4R+DA/S0FhWomEPoaXjqFS8jpgstCqwrBB5YsGmPOpi1prUhZlcB0OTUYvmMI0dYdgIp0Vy0/EnD86ZxVkpoY2eR6DKxZPlt6rVr4/LrrOYRPEz8sXGasc7wYWOC8H8vu9f734FtgLEfZ3iPMQ5glFK+zguVVTDMnrR5SnVaIFWoia//4rEvu1cEzv/Oju70Ao1JL+PSJ053ELHEQvuZab8fRpJp8E282QSGfpSWhM6PuVuBN/mzH0nblOyiamM+hltPuuZ8NKF8BDEowUA+RJynnmCOodCC0R1F86nRFIa2nurDY5Sg==",
 *          "CoordPix" : {
 *             "X" : 620,
 *             "Y" : 1053
 *          },
 *          "name" : "Point_R03"
 *       },
 *       {
 *          "CoordPhy" : "TkctEAXStRw139X7w2WdK9x6cUjzz/dfvEv9VfOA3DHO/waIcknivoZHJX7eS5dp1CCUP+Zg7PRDEcXESDZsMKvt/QCKB+ixYWBtAdmAw0MK1HlexabzObYpoBtoDQ5T7DGuaQ2QnVI7oyVJhBW103lLh8Hk20OT1FHtFhHMllwRLstw8A5jHi4NKM5OP0WYuZ/mjQXPhka5wg76ZDat6dUNCEFw06Bwc2jKrR8CliaJBPxkFAwxPNkYd9elWRVXT7Aq2kYlpGl3nuolGEFjYGvIjk2A+kl8tjyqct1pIHz0iF3mZk2/B5TFMp9x46EfDtGnX+ytisPqB0Ka5PTvZw==",
 *          "CoordPix" : {
 *             "X" : 978,
 *             "Y" : 1048
 *          },
 *          "name" : "Point_7"
 *       },
 *       {
 *          "CoordPhy" : "aWCI7sYHk4UG3UiPKNjoHtPub0FHqbEmJioytMNtiX1hV+BhAfaahcT5wzBfwcfqT5VXR5z0bx0U+AkIY6BnQubH3MrdQZBMsNrmdlSyJbCmXyrds3mqcZhyDjHCUflw1tC7fyC1spwZj1EraXU3AUPNyYqDvi+DarlKFEwAr88dI37jjIl8H55aXCqbkEHsQflsWJhzTDhr88fWhEBne6G7N2sD4SFAWk4A2RZBezOm0ZOCbkx2xyIuD3y89Bg4VlOAYuIJ2wviWONgQvPja8aExM/a5Myuuzhlywe78Z1/2mrmZja//CwNg5ujIkk0SOM6qaxsTl9GtfTFFxgPFg==",
 *          "CoordPix" : {
 *             "X" : 923,
 *             "Y" : 515
 *          },
 *          "name" : "Point_5"
 *       },
 *       {
 *          "CoordPhy" : "ZUGEc8VOgiK7VVf+JNYTag/UAXPAgrkzB5OvzAyuMFKmHzoSZQWkoOaCQwC9yKcQPlRdV3/ZFevxiE29f+cl5z6vk/FE/GDxNQXJp7qJc2br7p+L+0HYybrn3xNjLbD6gZ8vrKVSdmlZYizyLF8s9g0zcr/ZEBznVZ0BwdkmT+Tnjd566wnpKa5WCo96J8G2L+/4/UMB+t7L/90dwppS2NXz3x4LmPll2rwX5sPStSzuUEsXB+WLq+nt1n7WMpKiu54NVs6m2gTe+H27tXMR/wExaOfcdx3I9BLht5+ggO6kKRTvESKvBhMhfCQea0EMXP5r6rAxSQ1tybXOJvZr3Q==",
 *          "CoordPix" : {
 *             "X" : 927,
 *             "Y" : 226
 *          },
 *          "name" : "Point_3"
 *       }
 *    ],
 *    "Mapping" : [
 *       {
 *          "CoordPhy" : "MTULK4WdEJrgsI0Ik0vEPY1vLvhdvsjsupvPOEzRW64j0av+u9d9fH1Ci3qfg8xzIztIUO8tw8MxVM9RhB6Ap1+saNghcHLDt+3wgksfNoZC49cC/0g30ac9oLz9TGhFbd1en4VU598aDcUwhYI7f/0XHbImJkoaOrYb/cIhd90tNqok5HCqHoLV84n6QHlbRtWbqGAL3IqvfFRo7k15yG3MYq+Q1GTyrcISH/471Ksoiy9XfluaIzpP2RKuEMCvVBlBuO1XH+hi7Rx1OAFuJCLs9veR+9sNg5MyocdarQYnkRqe48chN6WgrhpqRVNhqUQjqX4XE8WmbQQ5XdJ1fA==",
 *          "CoordPix" : {
 *             "Center_X" : 725,
 *             "Center_Y" : 422,
 *             "LeftBotton_X" : 830,
 *             "LeftBotton_Y" : 540,
 *             "LeftTop_X" : 620,
 *             "LeftTop_Y" : 305,
 *             "RightBotton_X" : 625,
 *             "RightBotton_Y" : 543,
 *             "RightTop_X" : 818,
 *             "RightTop_Y" : 311
 *          },
 *          "name" : "默认图案_1"
 *       }
 *    ]
 * }
 *
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 15:58
 * @version    :1.0
 */
class CADMappingDto(
    @field:NotEmpty(message = "可印区域名称不能为空") val name: String,
    @JsonProperty(value = "CoordPix")
    @field:NotNull(message = "可印区域像素坐标不能为空") val coordPix: CoordinatePixDto,
//    @JsonProperty(value = "SizePhy")
//    @field:NotNull(message = "可印区域物理尺寸不能为空") val sizePhy: SizePhyDto,
    @JsonProperty(value = "CoordPhy")
    @field:NotNull(message = "可印区域物理尺寸不能为空") val coordPhy: String,
) {
}