package tech.tiangong.butted.common.enums

/**
 * 任务状态枚举
 * 0-排队中；10-生成中；20-已中止；30-已完成；50-失败；60-超时失败；
 */
enum class TaskStatusEnum(
    val code: Int,
    val desc: String,
) {
    /**
     * 排队中
     */
    QUEUEING(0, "排队中"),

    /**
     * 生成中
     */
    GENERATING(10, "生成中"),

    /**
     * 取消（已中止）
     */
    CANCELED(20, "取消"),

    /**
     * 已完成
     */
    COMPLETED(30, "已完成"),

    /**
     * 失败
     */
    FAILED(50, "失败"),

    /**
     * 超时失败
     */
    TIMEOUT_FAILED(60, "超时失败");

    fun processing(): Boolean {
        return processing(this.code)
    }
    fun canceled(): Boolean {
        return canceled(this.code)
    }
    fun completed(): Boolean {
        return completed(this.code)
    }
    fun failed(): <PERSON>olean {
        return failed(this.code)
    }

    fun failedOrCanceled(): Boolean {
        return failedOrCanceled(code)
    }
    fun finished(): Boolean {
        return finished(this.code)
    }

    companion object {
        @JvmStatic
        fun processing(code: Int?): <PERSON><PERSON><PERSON> {
            return code == QUEUEING.code || code == GENERATING.code
        }
        @JvmStatic
        fun canceled(code: Int?): Boolean {
            return code == CANCELED.code
        }
        @JvmStatic
        fun completed(code: Int?): Boolean {
            return code == COMPLETED.code
        }
        @JvmStatic
        fun failed(code: Int?): Boolean {
            return code == FAILED.code || code == TIMEOUT_FAILED.code
        }
        @JvmStatic
        fun failedOrCanceled(code: Int?): Boolean {
            return code == CANCELED.code
                    || code == FAILED.code
                    || code == TIMEOUT_FAILED.code
        }
        @JvmStatic
        fun finished(code: Int?): Boolean {
            return code == CANCELED.code
                    || code == COMPLETED.code
                    || code == FAILED.code
                    || code == TIMEOUT_FAILED.code
        }

        @JvmStatic
        fun of(code: Int): TaskStatusEnum {
            return entries.firstOrNull { it.code == code }
                ?: throw IllegalArgumentException("TaskStatusEnum not found by code $code")
        }

        @JvmStatic
        fun toDesc(code: Int): String {
            return of(code).desc
        }

        @JvmStatic
        fun processingStatus(): List<Int> {
            return listOf(QUEUEING.code, GENERATING.code)
        }

    }
}