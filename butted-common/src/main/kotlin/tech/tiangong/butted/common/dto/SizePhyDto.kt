package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotNull

/**
 * 物理尺寸
 *```json
 *  //可印区域物理尺寸，单位cm
 * {
 * 	"Width": 100,
 * 	"Height": 50
 * }
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 16:12
 * @version    :1.0
 */
class SizePhyDto(
    /**
     * 宽
     */
    @JsonProperty(value = "Width")
    @field:NotNull(message = "宽不能为空")
    var width: Double,
    /**
     * 高
     */
    @JsonProperty(value = "Height")
    @field:NotNull(message = "高不能为空")
    var height: Double,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -6539334158666302279L
    }
}