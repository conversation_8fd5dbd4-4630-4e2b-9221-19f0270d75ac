package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import java.math.BigDecimal

/**
 * logo_gen任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class LogoGenTaskVo(
    /**
     * 采样次数，默认为50
     */
    var samplingNum: Int? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,
    /**
     * 随机数种子
     */
    var seed: Int? = null,

    /**
     * 图片参考范围
     */
    var imageRefScale: BigDecimal? = null,

    /**
     * 文字参考范围
     */
    var textRefScale: BigDecimal? = null,
    /**
     * 提示词
     */
    var prompt: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 7956554366287076770L
    }

}