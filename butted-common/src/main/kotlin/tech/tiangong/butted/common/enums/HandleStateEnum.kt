package tech.tiangong.butted.common.enums

/**
 * 0-待处理；1-已处理；2-已处理；3-处理失败
 */
enum class HandleStateEnum(
    val code: Int,
    val desc: String,
) {
    /**
     * 不用处理
     */
    NOTHING(-1, "不用处理"),

    /**
     * 待处理
     */
    WAITING(0, "待处理"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 已处理
     */
    DONE(2, "已处理"),

    /**
     * 处理失败
     */
    FAILED(4, "处理失败")
    ;

    fun nothing(): Boolean {
        return nothing(this.code)
    }

    fun waiting(): Boolean {
        return waiting(this.code)
    }

    fun processing(): <PERSON><PERSON><PERSON> {
        return processing(this.code)
    }

    fun completed(): Boolean {
        return completed(this.code)
    }

    fun failed(): <PERSON><PERSON>an {
        return failed(this.code)
    }

    fun finished(): Boolean {
        return finished(this.code)
    }

    fun nothingOrCompleted(): <PERSON><PERSON>an {
        return nothingOrCompleted(this.code)
    }

    companion object {
        fun nothing(code: Int?): Boolean {
            return code == NOTHING.code
        }

        fun waiting(code: Int?): Bo<PERSON>an {
            return code == WAITING.code
        }

        fun processing(code: Int?): Boolean {
            return code == PROCESSING.code
        }

        fun completed(code: Int?): Boolean {
            return code == DONE.code
        }

        fun failed(code: Int?): Boolean {
            return code == FAILED.code
        }

        fun finished(code: Int?): Boolean {
            return code == DONE.code
                    || code == FAILED.code
        }

        fun nothingOrCompleted(code: Int?): Boolean {
            return nothing(code) || completed(code)
        }

        fun of(code: Int): HandleStateEnum =
            HandleStateEnum.entries.firstOrNull { it.code == code }
                ?: throw IllegalArgumentException("HandleActionEnum not found by code $code")

    }
}