package tech.tiangong.butted.common.enums

/**
 * Comfyui工作流类型Enum
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:47
 * @version    :1.0
 */
enum class ComfyuiWorkflowTypeEnum(
    val code: String,
    val desc: String,
) {
    /**
     * 常规生图
     */
    REGULAR_RAW_IMAGE("1", "logo2.1changguishengtu1.0"),

    /**
     * 元素化衍生
     */
    ELEMENTAL_DERIVATIVES("2", "logo2.1yuansuhuayansheng1.0"),
    /**
     * 元素化衍生2.0
     */
    ELEMENTAL_DERIVATIVES_2("20", "JV-yuansuhuayansheng-v2.0"),
    /**
     * 图生ID
     * // 需要设计ID主图
     */
    IMAGE_GEN_ID("3", "ren-Fluximg2imgID-v1.0"),

    /**
     * 文生ID
     */
    TEXT_GEN_ID("4", "ren-Fluxtext2imgID-v1.0"),

    /**
     * ID一致性
     * // 不需要设计ID主图
     */
    ID_CONSISTENCY("5", "ren-IDyzx-v1.2"),
    /* ***特效*** */
    /**
     * 特效:小清新
     */
    SPECIAL_EFFECT_1XIAOQINGXINB("610", "1xiaoqingxinB"),

    /**
     * 特效:水彩画
     */
    SPECIAL_EFFECT_6GANGBIB("611", "6gangbiB"),

    /**
     * 特效:可爱卡通
     */
    SPECIAL_EFFECT_7GUOFENGB("612", "7guofengB"),

    /**
     * 特效:像素风格
     */
    SPECIAL_EFFECT_PIXEL_STYLE("613", "pixel_style"),

    /**
     * 特效:美式漫画
     */
    SPECIAL_EFFECT_MCOMICNEWYEARSINGLEPIC("614", "McomicNewYearSinglePic"),

    /**
     * 特效:卡通贴纸
     */
    SPECIAL_EFFECT_CARTOON_TAGS("615", "Cartoon_tags"),

    /**
     * 特效:像素风
     */
    SPECIAL_EFFECT_WST_PIXEL("616", "wst_pixel"),
    /* ***特效*** */


    /**
     * 商品主图筛选
     */
    SPECIAL_EFFECT_GET_BEST_TRYON_PIC("710", "get_best_tryon_pic"),
    ;

    companion object {
        @JvmStatic
        fun of(code: String): ComfyuiWorkflowTypeEnum {
            return ComfyuiWorkflowTypeEnum.entries.firstOrNull { it.code == code }
                ?: throw IllegalArgumentException("ComfyuiWorkflowTypeEnum not found by code $code")
        }

        @JvmStatic
        fun from(desc: String): ComfyuiWorkflowTypeEnum {
            return ComfyuiWorkflowTypeEnum.entries.firstOrNull { it.desc == desc }
                ?: throw IllegalArgumentException("ComfyuiWorkflowTypeEnum not found by desc $desc")
        }

        @JvmStatic
        fun specialEffect() = setOf(
            SPECIAL_EFFECT_1XIAOQINGXINB.code,
            SPECIAL_EFFECT_6GANGBIB.code,
            SPECIAL_EFFECT_7GUOFENGB.code,
            SPECIAL_EFFECT_PIXEL_STYLE.code,
            SPECIAL_EFFECT_MCOMICNEWYEARSINGLEPIC.code,
            SPECIAL_EFFECT_CARTOON_TAGS.code,
        )

        @JvmStatic
        fun specialEffect(code: String) = specialEffect().contains(code)

        @JvmStatic
        fun batchSize(code: String) = setOf(
            REGULAR_RAW_IMAGE.code,
            ELEMENTAL_DERIVATIVES.code,
            ELEMENTAL_DERIVATIVES_2.code,
        ).contains(code)

        @JvmStatic
        fun text(code: String) = setOf(
            TEXT_GEN_ID.code,
            IMAGE_GEN_ID.code,
            ID_CONSISTENCY.code,
        ).contains(code)

        @JvmStatic
        fun wstPixel(code: String) = setOf(
            SPECIAL_EFFECT_WST_PIXEL.code,
        ).contains(code)

        @JvmStatic
        fun imageGenId(code: String) = setOf(
            IMAGE_GEN_ID.code,
        ).contains(code)

        @JvmStatic
        fun idConsistency(code: String) = setOf(
            ID_CONSISTENCY.code,
        ).contains(code)

        @JvmStatic
        fun bestTryOnPic(code: String) = setOf(
            SPECIAL_EFFECT_GET_BEST_TRYON_PIC.code,
        ).contains(code)
    }
}