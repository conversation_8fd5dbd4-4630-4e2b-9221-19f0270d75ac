package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import java.io.Serial

/**
 * 图片向量任务Req
 *
 * <AUTHOR>
 */
data class VectorTaskTestVo(


// 所有调用过testR的批次ID
    val invokedBatchIds: Set<Long>,
    //任务和批次id集合
    val batchTaskMap: Map<Long, Set<Long>>


    )

