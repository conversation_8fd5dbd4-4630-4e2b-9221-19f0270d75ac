package tech.tiangong.butted.common.req

import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 通义千问新增参数
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:53
 * @version    :1.0
 */
class QwenTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 正向提示词
     */
    var prompt: String? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 3669924785636425742L
    }

    fun check(): Boolean =
        if (prompt == null && refImgUrl == null) {
            false
        } else {
            prompt?.isNotBlank() == true || refImgUrl?.isNotBlank() == true
        }
}