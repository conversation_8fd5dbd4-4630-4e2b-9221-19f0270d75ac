package tech.tiangong.butted.common.vo


import tech.tiangong.butted.common.vo.base.DurationBaseTaskVo
import tech.tiangong.butted.common.vo.base.RefImgVo


/**
 * 智能识别响应VO
 */
data class SmartDesignTaskVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 参考图类型：0-非真人模特图、1-真人模特图
     */
    var refImgType: Int? = null,
    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: Int? = null,
    /**
     * 扩展处理:1-风格,2-花型识别,3-多姿势,4-面料识别及推荐,5-花型提取,6-场景,7-模特,8-Try换装
     */
    var extendActions: String? = null,
    /**
     * 品类名称
     */
    var category: String? = null,
    /**
     * 识别品类名称
     */
    var identifyCategory: String? = null,
    /**
     * 是否生成多姿势，1-是，0-否
     */
    var multiPose: Int? = null,
    /**
     * 背景图列表
     */
    var bgImgInfoList: List<RefImgVo>? = null,
    /**
     * 模特图列表
     */
    var modelImgInfoList: List<RefImgVo>? = null,

    /**
     * 模特素材ID
     */
    var modelMaterialId: Long? = null,
    /**
     * 模特素材名称
     */
    var modelMaterialName: String? = null,
    /**
     * 模特素材URL
     */
    var modelMaterialUrl: String? = null,
    /**
     * 模特素材描述
     */
    var modelMaterialCaption: String? = null,

    /**
     * 生成图尺寸，宽高，如800x1200，默认1024x1024
     */
    var size: String? = null,
    /**
     * 随机种子
     */
    var seed: Long? = null,
    /**
     * 生成图列表
     */
    var resImgList: List<ResGroupRepairImgVo>? = null,
    /**
     * 生成描述词
     */
    var prompts: String? = null,
    /**
     * 模型版本
     */
    var modelVersion: String? = null,

    /**
     * 分割标签列表
     */
    var clipLabelList: List<PredLabelVo>? = null,
    /**
     * 花型标签列表
     */
    var flowerPatternLabelList: List<PredLabelVo>? = null,
//    /**
//     * 风格标签列表
//     */
//    var styleLabelList: List<PredLabelVo>? = null,
    /**
     * 面料标签
     */
    var fabricLabel: FabricLabelVo? = null,
    /**
     * 推荐面料列表
     */
    var recommendFabricList: List<FabricRecommendSkuVo>? = null,


    ) : DurationBaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
