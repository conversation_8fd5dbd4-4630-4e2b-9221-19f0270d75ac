package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.dto.PoseInfoDto
import tech.tiangong.butted.common.vo.base.BaseSyncTaskVo
import java.math.BigDecimal

/**
 * 姿势任务Vo
 *
 * <AUTHOR>
 */
data class PoseTaskVo(
    /**
     * 来源
     */
    var source: String? = null,
    /**
     * 来源ID
     */
    var sourceId: Long? = null,
    /**
     * 输入图片URL
     */
    var inputImage: String? = null,
    /**
     * 倾斜角度
     */
    var tiltAngle: BigDecimal? = null,
    /**
     * 姿势信息JSON：
     * ["背面":0.99,"正面":0.0,"左背侧面":0.01,"左正侧面":0.0,"右背侧面":0.0,"右正侧面":0.0,"坐姿":0.0,"左中侧面":0.0,"右中侧面":0.0]
     */
    var poseInfo: PoseInfoDto? = null,

    ) : BaseSyncTaskVo() {

    companion object {
        private const val serialVersionUID = 1L
    }
}
