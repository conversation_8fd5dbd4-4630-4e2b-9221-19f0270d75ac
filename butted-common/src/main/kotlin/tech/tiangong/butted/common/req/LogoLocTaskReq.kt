package tech.tiangong.butted.common.req

import tech.tiangong.butted.common.dto.CADInfoDto
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 区域定位新增参数
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:53
 * @version    :1.0
 */
class LogoLocTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    @field:NotEmpty(message = "参考图url不能空")
    var refImgUrl: String? = null,

    /**
     * CAD平铺图url
     */
    @field:NotEmpty(message = "CAD平铺图url不能空")
    var flatImgUrl: String? = null,

    /**
     * CAD信息
     */
    @field:NotNull(message = "CAD信息不能空")
    var cad: CADInfoDto? = null,

    /**
     * 正背面:
     * 默认0，0正面，1背面
     */
    @field:NotNull(message = "正背面不能空")
    var direction: Int? = null,
    /**
     * 品类
     */
    @field:NotEmpty(message = "品类不能空")
    var category: String? = null,
    /**
     * 自定义区域信息
     */
    var customRegion: CustomRegionReq? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -7232405652188808306L
    }
}