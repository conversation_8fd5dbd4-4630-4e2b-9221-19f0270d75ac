package tech.tiangong.butted.common.vo

import java.io.Serializable
import java.math.BigDecimal

/**
 * 面料标签模型Vo
 */
class FabricRecommendLabelVo(
    /**
     * AI识别编码：
     * 0-成功；-1-输入错误；-2-下载图片出错；-3-图片不可用；
     * -4-品类错误或品类暂时不支持；-5-无法提取对应品类；
     * -6-推理错误；-7-得分小于阈值
     */
    var code_e: Int? = null,

    /**
     * 识别品类（eg：T恤）
     */
    var category: String? = null,

    /**
     * LAB色值（eg：[12 , 1 , -2]）
     */
    var lab_value: List<BigDecimal>? = null,

    /**
     * top5的家族ID
     */
    var top5_familyIds: List<String>? = null,

    /**
     * top5的家族代表
     */
    var top5_familyNames: List<String>? = null,

    /**
     * top5的所有面料，如果中台没有，则为空[] 是个二维数组
     */
    var top5_familyNames_all: List<List<String>>? = null,

    /**
     * top5的家族代表分数值
     */
    var top5_score: List<BigDecimal>? = null,

    ) : Serializable {

    fun successful(): Boolean {
        return code_e == 0
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}