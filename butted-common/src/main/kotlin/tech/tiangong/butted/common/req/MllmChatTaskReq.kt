package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * mllm_chat任务新增参数
 *
 * <AUTHOR>
 * @date       ：2025/2/17 10:45
 * @version    :1.0
 */
class MllmChatTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 提示词
     */
    @field:NotEmpty(message = "提示词不能空")
    var prompts: List<String> = listOf()
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 5566904964337936808L
    }


}