package tech.tiangong.butted.common.req;

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.BizSourceEnum
import java.io.Serializable

/**
 * 图片描述说明任务Req
 *
 * <AUTHOR>
 */
data class PictureCaptionCallReq(
    /**
     * 图片来源
     */
    @field:NotNull(message = "图片来源不能为空")
    var source: BizSourceEnum? = null,
    /**
     * 输入图片
     */
    @field:NotBlank(message = "输入图片不能为空")
    var inputImg: String? = null,

    ) : Serializable {
    companion object {
        @java.io.Serial
        private const val serialVersionUID: Long = 1L
    }
}
