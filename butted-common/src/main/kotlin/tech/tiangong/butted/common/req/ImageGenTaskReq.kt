package tech.tiangong.butted.common.req

import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * image_gen处理参数
 *
 * <AUTHOR>
 * @date       ：2024/11/25 10:13
 * @version    :1.0
 */
class ImageGenTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 正向提示词
     */
    var prompt: String? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,

    /**
     * 参考图宽
     */
    var imgWidth: Int? = null,

    /**
     * 参考图高
     */
    var imgHeight: Int? = null,
    /**
     * 负向提示词
     */
    var negPrompt: String? = null,
    /**
     * 随机数种子
     */
    var seed: Int? = null,

    /**
     * 参考范围
     */
    var refScale: BigDecimal? = null,
) : Serializable {
    fun check(): Boolean =
        if (prompt == null && refImgUrl == null) {
            false
        } else {
            prompt?.isNotBlank() == true && refImgUrl?.isNotBlank() == true
        }

    companion object {
        @Serial
        private const val serialVersionUID: Long = -8938669965114142292L
    }
}