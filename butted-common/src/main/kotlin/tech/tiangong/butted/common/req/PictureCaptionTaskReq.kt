package tech.tiangong.butted.common.req;

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 图片描述说明任务Req
 *
 * <AUTHOR>
 */
data class PictureCaptionTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 输入图片
     */
    @field:NotBlank(message = "输入图片不能为空")
    var inputImg: String? = null,

    /**
     * 回调URL
     */
    var callback: String? = null,

    ) : Serializable
