package tech.tiangong.butted.common.req.tryon

import java.io.Serializable
import jakarta.validation.constraints.*

/**
 * 虚拟换装任务Req
 *
 * <AUTHOR>
 */
data class TryOnTaskReq(
    /**
     * clip任务ID
     */
    var clipTaskId: Long? = null,
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 品类名
     */
    @field:NotBlank(message = "品类不能空")
    var category: String? = null,
    /**
     * 服装图
     */
    @field:NotBlank(message = "服装图不能空")
    var garmImgUrl: String? = null,
    /**
     * 模特图参考图
     */
    @field:NotBlank(message = "模特图不能空")
    var humanImgUrl: String? = null,
    /**
     * 模特图重绘区域
     */
    @field:NotBlank(message = "模特图重绘区域不能空")
    var maskImgUrl: String? = null,
    /**
     * 换脸：0-否；1-是
     */
    @field:NotNull(message = "是否换脸不能空")
    var shiftFace: Int? = null,
    /**
     * 换脸模特头像列表
     */
    @field:Size(max = 8, message = "换脸模特头像数量不能大于8")
    var shiftFaceImgList: List<String>? = null,
    /**
     * 生成数量
     */
    @field:NotNull(message = "生成数量不能空")
    @field:Min(value = 1, message = "数量不能小于1")
    @field:Max(value = 4, message = "数量不能大于4")
    var genCount: Int? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
