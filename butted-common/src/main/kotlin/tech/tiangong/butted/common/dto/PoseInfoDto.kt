package tech.tiangong.butted.common.dto

import java.io.Serializable
import java.math.BigDecimal

/**
 * 姿势信息Vo
 */
data class PoseInfoDto(
    /**
     * 背面
     */
    var back: BigDecimal? = null,
    /**
     * 正面
     */
    var front: BigDecimal? = null,
    /**
     * 坐姿
     */
    var sitting: BigDecimal? = null,
    /**
     * 左中侧面
     */
    var leftSide: BigDecimal? = null,
    /**
     * 左正侧面
     */
    var leftFrontSide: BigDecimal? = null,
    /**
     * 左背侧面
     */
    var leftBackSide: BigDecimal? = null,
    /**
     * 右中侧面
     */
    var rightSide: BigDecimal? = null,
    /**
     * 右正侧面
     */
    var rightFrontSide: BigDecimal? = null,
    /**
     * 右背侧面
     */
    var rightBackSide: BigDecimal? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
