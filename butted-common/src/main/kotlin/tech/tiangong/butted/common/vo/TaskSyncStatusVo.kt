package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.enums.TaskStatusEnum
import java.io.Serializable

/**
 * 任务同步状态VO
 */
data class TaskSyncStatusVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 任务模型
     */
    var taskMode: String? = null,
    /**
     * 当前任务状态
     */
    var taskStatus: TaskStatusEnum? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }


}