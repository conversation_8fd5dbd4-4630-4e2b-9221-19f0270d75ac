package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 *  ptn_mark_task任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class PtnMarkTaskVo(

    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 标签结果
     */
    var inferenceResult: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {

        @Serial
        private const val serialVersionUID: Long = 1707159872749152543L
    }

}