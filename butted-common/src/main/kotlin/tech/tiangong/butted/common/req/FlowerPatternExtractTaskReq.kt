package tech.tiangong.butted.common.req

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 花型提取Req
 */
data class FlowerPatternExtractTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    @field:NotBlank(message = "参考图url不能为空")
    var refImgUrl: String? = null,
//    /**
//     * 提取矩形区域列表
//     */
//    @field:NotEmpty(message = "提取矩形区域不能为空")
//    var regionList: List<RectangleCoordinateReq>? = null,

    /**
     * 提取区域：1-上半身；2-下半身；3-全身
     */
    @field:NotNull(message = "提取区域不能空")
    var extractRegion: Int? = null,
    /**
     * 生成图片数量
     */
    @field:NotNull(message = "生成图片数量不能为空")
    @field:Min(value = 1, message = "生成图片数量不能小于1")
    @field:Max(value = 8, message = "生成图片数量不能大于8")
    var count: Int? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null

) {
    companion object {
        private const val serialVersionUID = 1L
    }
}
