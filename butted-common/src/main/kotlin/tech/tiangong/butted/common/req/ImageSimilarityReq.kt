package tech.tiangong.butted.common.req

import java.io.Serializable
import jakarta.validation.constraints.NotBlank

/**
 * 图片相似度比较Req
 */
data class ImageSimilarityReq(
    /**
     * 参考图a的url
     */
    @field:NotBlank(message = "参考图a的url不能为空")
    var aImgUrl: String? = null,

    /**
     * 参考图b的url
     */
    @field:NotBlank(message = "参考图b的url不能为空")
    var bImgUrl: String? = null,

    ): Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
