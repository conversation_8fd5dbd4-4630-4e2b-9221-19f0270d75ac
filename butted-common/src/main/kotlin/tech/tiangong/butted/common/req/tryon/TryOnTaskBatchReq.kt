package tech.tiangong.butted.common.req.tryon

import java.io.Serializable
import jakarta.validation.constraints.*

/**
 * 虚拟换装批量任务Req
 *
 * <AUTHOR>
 */
data class TryOnTaskBatchReq(
    /**
     * 服装图列表
     */
    @field:NotEmpty(message = "服装图不能空")
    @field:Size(min = 1, max = 5, message = "服装图数量不能小于1且不能大于5")
    var garmImgList: List<TryOnTaskBatchGarmReq>? = null,
    /**
     * 模特图参考图
     */
    @field:NotBlank(message = "模特图不能空")
    var humanImgUrl: String? = null,
    /**
     * 模特图重绘区域
     */
    @field:NotBlank(message = "模特图重绘区域不能空")
    var maskImgUrl: String? = null,
    /**
     * 换脸：0-否；1-是
     */
    @field:NotNull(message = "是否换脸不能空")
    var shiftFace: Int? = null,
    /**
     * 换脸模特头像列表
     */
    @field:Size(max = 8, message = "换脸模特头像数量不能大于8")
    var shiftFaceImgList: List<String>? = null,
    /**
     * 生成数量
     */
    @field:NotNull(message = "生成数量不能空")
    @field:Min(value = 1, message = "数量不能小于1")
    @field:Max(value = 4, message = "数量不能大于4")
    var genCount: Int? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
