package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 图片分割Vo
 *
 */
data class PartitionMaskVo(
    /**
     * mask图ID
     */
    var maskId: Long? = null,
    /**
     * Mask类型（暂时没用）
     */
    var maskType: String? = null,
    /**
     * Mask图片路径
     */
    var maskUrl: String? = null,
    /**
     * 序号
     */
    var serialNum: Int? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
