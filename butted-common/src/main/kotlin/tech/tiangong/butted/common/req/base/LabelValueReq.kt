package tech.tiangong.butted.common.req.base

import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import java.io.Serializable

/**
 * 标签值Req
 */
data class LabelValueReq(
    /**
     * 标签名
     */
    var name: String? = null,
    /**
     * 标签编号
     */
    var code: String? = null,
    /**
     * 标签值列表
     */
    var values: List<LabelValueReq>? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }

    fun validIncludeCode(): Boolean {
        val checkValues = if (values.isEmpty()) {
            true
        } else {
            values!!.all { it.validIncludeCode() }
        }
        return name.isNotBlank() && code.isNotBlank() && checkValues
    }

    fun valid(): Boolean {
        val checkValues = if (values.isEmpty()) {
            true
        } else {
            values!!.all { it.valid() }
        }
        return name.isNotBlank() && checkValues
    }
}