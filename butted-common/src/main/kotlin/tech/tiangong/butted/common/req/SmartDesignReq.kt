package tech.tiangong.butted.common.req

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.SmartDesignType
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import tech.tiangong.butted.common.req.base.PredLabelReq
import java.math.BigDecimal

/**
 * 智能设计开款Req
 */
data class SmartDesignReq(
    /**
     * 智能设计类型（默认：SMART_DESIGN）
     */
    var smartDesignType: SmartDesignType? = null,
    /**
     * 模型编码（字典配置编码）
     */
    var modeCode: String? = null,
    /**
     * 模型名称（字典配置名称）
     */
    var modeName: String? = null,
//    /**
//     * 智能设计模型（默认0）：0-FG2.0；1-FG2.0小红书；2-FG2.0小红书2（作废）
//     */
//    var redBookStyle: Int? = null,

    /**
     * 智能识别ID
     */
    @field:NotNull(message = "智能识别ID不能空")
    var smartIdentifyId: Long? = null,
    /**
     * 是否使用识别Clip标签：1-是，0-否
     */
    var useIdentifyClipLabel: Int? = null,
    /**
     * 提交的Clip标签
     */
    var submitClipLabels: List<PredLabelReq>? = null,
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 父业务主键ID
     */
    var parentBusId: Long? = null,
    /**
     * 父业务编号
     */
    var parentBusCode: String? = null,
    /**
     * 参考图url
     */
    @field:NotBlank(message = "参考图url不能为空")
    var refImgUrl: String? = null,
    /**
     * 参考图权重，小于等于0，表示不启用图文生图（仅文生图）；值越大，参考度越低，建议1~8的浮点数（默认0）
     */
    var refWeight: BigDecimal? = null,
    /**
     * 品类名称
     */
    @field:NotBlank(message = "品类名称不能为空")
    var category: String? = null,
    /**
     * 是否生成多姿势，1-是，0-否
     */
    @field:NotNull(message = "是否生成多姿势不能为空")
    var multiPose: Int? = null,
    /**
     * 背景增强：0-否；1-是
     */
    @field:NotNull(message = "背景增强不能为空")
    var filterBack: Int? = null,
    /**
     * 脸部修复：0-否；1-是
     */
    @field:NotNull(message = "脸部修复不能为空")
    var faceRepair: Int? = null,

    /**
     * 履约增强：0-否；1-是
     */
    var promiseEnhanced: Int? = 0,
    /**
     * 背景图列表
     */
    var bgImgInfoList: List<RefImgReq>? = null,
    /**
     * 模特图列表
     */
    var modelImgInfoList: List<RefImgReq>? = null,

    /**
     * 模特素材ID
     */
    var modelMaterialId: Long? = null,
    /**
     * 模特素材名称
     */
    var modelMaterialName: String? = null,
    /**
     * 模特素材URL
     */
    var modelMaterialUrl: String? = null,
    /**
     * 模特素材描述
     */
    var modelMaterialCaption: String? = null,

    /**
     * 生成图片数量
     */
    @field:NotNull(message = "生成图片数量不能为空")
    @field:Min(value = 1, message = "生成图片数量不能小于1")
    @field:Max(value = 10, message = "生成图片数量不能大于10")
    var genCount: Int? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) : BaseTenantUserReq() {
    companion object {
        private const val serialVersionUID = 1L
    }

}
