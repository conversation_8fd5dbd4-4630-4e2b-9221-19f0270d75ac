package tech.tiangong.butted.common.req.label

import java.io.Serializable
import jakarta.validation.constraints.NotEmpty

/**
 * 营销市场风格识别任务
 *
 */
data class MarketStyleTaskBatchCreateReq(

    /**
     * 输入图片URL
     */
    @field:NotEmpty(message = "输入图片URL不能为空")
    var inputImage: List<String>? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
