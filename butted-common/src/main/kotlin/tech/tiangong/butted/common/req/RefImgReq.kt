package tech.tiangong.butted.common.req

import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.util.json.toJson
import java.io.Serializable

/**
 * 参考图
 */
class RefImgReq(
    /**
     * 图片url
     */
    var url: String? = null,
    /**
     * 图片文本描述
     */
    var captions: String? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }

    fun valid(): Boolean {
        return url.isNotBlank()
    }


}