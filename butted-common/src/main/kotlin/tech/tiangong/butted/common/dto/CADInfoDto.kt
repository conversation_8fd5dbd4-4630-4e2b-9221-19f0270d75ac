package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty

/**
 * CAD信息
 *```json
 * {
 *     "Mapping": [   //可印区域数组,模板只给出一个可印区域定义，实际输出多少个可印区域由服装制作的人操作决定。
 *       {
 *         "name": "Mapping_1", //可印区域名称
 *         "CoordPix": { //可印区域像素坐标，数据类型为整形，渲染图片左上角为坐标原点
 *           "LeftTop_X": 0,
 *           "LeftTop_Y": 0,
 *           "RightTop_X": 100,
 *           "RightTop_Y": 0,
 *           "LeftBotton_X": 0,
 *           "LeftBotton_Y": 100,
 *           "RightBotton_X": 100,
 *           "RightBotton_Y": 100,
 *           "Center_X": 50,
 *           "Center_Y": 50
 *         },
 *
 *         "SizePhy": { //可印区域物理尺寸，单位cm
 *           "Width": 100,
 *           "Height": 50
 *         }
 *       }
 *     ],
 *
 *   "FeaturePoint": [ //特征点数组,模板只给出一个特征点定义，实际输出多少个特征点由服装制作的人操作决定。
 *     {
 *       "name": "FeaturePoint_1", //特征点名称
 *       "CoordPix": { //特征点像素坐标,数据类型为整形，渲染图片左上角为坐标原点
 *         "X": 10,
 *         "Y": 10
 *       },
 *       "CoordPhy": { //特征点物理坐标，单位cm，数据类型为浮点型，图案可印区域中心点作为特征点坐标系的原点，只有把服装平铺后算出来的才是有意义，而且特征点和可印区域尽量要“压”到很平整
 *         "X": 10.134,
 *         "Y": 10.245
 *       }
 *     }
 *   ]
 * }
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 15:56
 * @version    :1.0
 */
class CADInfoDto(
    @field:Valid
    @JsonProperty(value = "Mapping")
    @field:NotEmpty(message = "可印区域数组不能为空")
    val mapping: List<CADMappingDto>,
    @field:Valid
    @JsonProperty(value = "FeaturePoint")
    @field:NotEmpty(message = "特征点数组不能为空")
    val featurePoint: List<FeaturePointDto>,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 7566444665359588721L
    }
}