package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo

/**
 * 面料推荐任务Vo
 *
 * <AUTHOR>
 */
data class FabricRecommendTaskVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 父任务ID
     */
    var parentTaskId: Long? = null,
    /**
     * 输入图片
     */
    var inputImage: String? = null,
    /**
     * 品类名称
     */
    var category: String? = null,
    /**
     * 预测的标签
     */
    var predLabelInfo: FabricRecommendLabelVo? = null,
    /**
     * 面料标签
     */
    var fabricLabel: FabricLabelVo? = null,
    /**
     * 推荐面料列表
     */
    var recommendFabricList: List<FabricRecommendSkuVo>? = null,
) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}