package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * 图片向量任务Vo
 *
 * <AUTHOR>
 */
data class VectorTaskVo(

    /**
     * 业务主键ID
     */
    var busId: Long? = null,

    /**
     * 业务编号
     */
    var busCode: String? = null,

    /**
     * 输入图片URL
     */
    var inputImage: String? = null,


    /**
     * 向量维度
     */
    var vectors: List<Float>? = null,

    ) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
