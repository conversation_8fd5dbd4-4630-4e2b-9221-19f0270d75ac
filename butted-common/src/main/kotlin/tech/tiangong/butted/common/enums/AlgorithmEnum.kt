package tech.tiangong.butted.common.enums

/**
 * 算法平台：
 */
enum class AlgorithmEnum(
    val code: String,
    val desc: String
) {
    /**
     * 杉缔
     */
    JV("jv", "杉缔"),

    /**
     * 致景
     */
    ZJ("zj", "致景"),

    /**
     * 潮际
     */
    CJ("cj", "潮际"),

    LAZADA("lazada", "啦咋哒"),

    /**
     * CHAT_GPT
     */
    CHAT_GPT("chat_gpt", "CHAT_GPT"),

    /**
     * 佐糖
     */
    PICWISH("picwish", "佐糖"),

    ;


    companion object {

    }

}

