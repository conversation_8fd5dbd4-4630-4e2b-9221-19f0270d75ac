package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.dto.CoordinatePixDto
import java.io.Serial
import java.io.Serializable

/**
 * 区域定位任务结果Logo信息VO
 *
 * <AUTHOR>
 * @date       ：2025/1/6 18:42
 * @version    :1.0
 */
class LogoLocResultLogoMappingVo(
    /**
     * Mask图片
     */
    val logoMaskUrl: List<String>,
    /**
     * logo的区域坐标
     */
    val logoRegion: List<CoordinatePixDto>
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -1579697632745180346L
    }
}