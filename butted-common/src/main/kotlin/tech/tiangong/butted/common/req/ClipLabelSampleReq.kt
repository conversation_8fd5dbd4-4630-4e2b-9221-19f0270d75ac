package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.BizSourceEnum
import tech.tiangong.butted.common.req.base.BaseTenantUserReq

/**
 * 提取标签任务Req
 *
 * <AUTHOR>
 */
open class ClipLabelSampleReq(
    /**
     * 来源
     * ("PATTERN_MATERIAL", "版型耗材"),
     * ("DESIGN_MATERIAL", "AI素材"),
     * ("DRESS_UP", "服装上身"),
     * ("TRY_ON", "虚拟换装"),
     * ("TRY_ON_OUTPUT", "虚拟换装输出"),
     * ("SCENE_PICTURE", "场景图"),
     * ("MODEL_MATERIAL", "模特素材"),
     * ("DIGITAL_PRINT", "数码印花"),
     */
    @field:NotNull(message = "来源不能为空")
    var source: BizSourceEnum? = null,
    /**
     * 输入图片URL
     */
    @field:NotBlank(message = "输入图片URL不能为空")
    var inputImage: String? = null,

    ) : BaseTenantUserReq() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
