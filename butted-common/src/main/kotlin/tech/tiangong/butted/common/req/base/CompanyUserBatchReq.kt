package tech.tiangong.butted.common.req.base

import java.io.Serial
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size

/**
 * 用户租户批量请求参数
 *
 * <AUTHOR>
 * @date       ：2024/11/25 10:10
 * @version    :1.0
 */
class CompanyUserBatchReq<T>(
    /**
     * 创建人id
     */
    creatorId: Long,
    /**
     * 创建人
     */
    creatorName: String,
    /**
     * 租户id
     */
    companyId: Long,
    /**
     * 任务数据
     */
    @field: Valid
    @field:NotEmpty(message = "任务数据不能为空")
    @field:Size(min = 1, max = 200, message = "任务数据长度必须在1到200之间")
    var data: List<T>
) : BaseCompanyUserReq(creatorId, creatorName, companyId) {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 2479443119337713269L
    }
}