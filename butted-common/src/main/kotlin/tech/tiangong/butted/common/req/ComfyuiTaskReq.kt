package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * comfyui新增参数
 *
 * <AUTHOR>
 * @date       ：2025/2/17 10:45
 * @version    :1.0
 */
class ComfyuiTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
//    @field:NotEmpty(message = "参考图url不能空")
    var refImgUrl: String? = null,
    /**
     * 生成数量
     */
//    @field:NotNull(message = "生成数量不能空")
    var genCount: Int? = null,

    /**
     * 工作流类型
     */
    @field:NotEmpty(message = "工作流类型不能空")
    var workflowType: String? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 7408977874592504941L
    }

    /**
     * 提示词
     */
    var prompt: String? = null
}