package tech.tiangong.butted.common.req.category

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * 营销市场品类识别任务
 *
 */
data class MarketCategoryTaskBatchCreateReq(

    /**
     * 输入图片URL
     */
    @field:NotEmpty(message = "输入图片URL不能为空")
    var inputImage: List<String>? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
