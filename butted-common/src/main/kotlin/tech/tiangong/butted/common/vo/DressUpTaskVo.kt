package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.DurationBaseTaskVo
import tech.tiangong.butted.common.vo.base.ResImgVo
import java.io.Serial

/**
 * 服装上身任务Vo
 *
 * <AUTHOR>
 */
data class DressUpTaskVo(
    /**
     * clip任务ID
     */
    var clipTaskId: Long? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 品类名称
     */
    var categoryName: String? = null,
    /**
     * 服装图
     */
    var clothingImg: String? = null,

    /**
     * 模特图
     */
    var modelImg: String? = null,

    /**
     * 模特图Mark区域
     */
    var modelMarkImg: String? = null,

    /**
     * 随机种子，固定的话每次生成图都一样
     */
    var seed: Long? = null,

    /**
     * 生成数量count
     */
    var genCount: Int? = null,
    /**
     * 生成图列表
     */
    var resImgList: List<ResImgVo>? = null,

    ) : DurationBaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
