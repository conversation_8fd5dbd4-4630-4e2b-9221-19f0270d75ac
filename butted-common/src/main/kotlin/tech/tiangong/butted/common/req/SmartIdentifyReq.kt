package tech.tiangong.butted.common.req

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 智能识别
 */
data class SmartIdentifyReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID")
    var busId: Long? = null,
    /**
     * 参考图url
     */
    @field:NotBlank(message = "参考图url不能为空")
    var refImgUrl: String? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}