package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * Comfyui任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class ComfyuiTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,

    /**
     * 工作流类型
     */
    var workflowType: String? = null,
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 8603444197652590198L
    }
}