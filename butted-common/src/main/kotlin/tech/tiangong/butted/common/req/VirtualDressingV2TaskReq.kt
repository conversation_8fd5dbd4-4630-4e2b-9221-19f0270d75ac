package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 任务新增参数
 *
 */
class VirtualDressingV2TaskReq(

    /**
     * 回调URL
     */
    var callback: String? = null,
    /**
     * 任务信息
     */
    @field:NotEmpty(message = "任务信息不能空")
    var taskInfoList : List<TaskInfo>
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 7403259140254771571L
    }

    class TaskInfo{
        /**
         * 业务主键ID
         */
        @field:NotNull(message = "业务主键ID不能空")
        var busId: Long? = null
        /**
         * 业务编号
         */
        @field:NotBlank(message = "业务编号不能空")
        var busCode: String? = null
        /**
         * 商品图url
         */
        @field:NotBlank(message = "商品图url不能空")
        var imageClothUrl: String? = null

        /**
         * 姿势图url(多个逗号拼接)
         */
        @field:NotBlank(message = "姿势图url不能空")
        var imageModelUrl: String? = null

        /**
         * 上装、下装、全身 upper/lower/overall
         */
        @field:NotBlank(message = "衣服类型不能空")
        var clothType: String? = null

        /**
         * 模型信息
         */
        @field:NotEmpty(message = "模型信息不能空")
        var modelBatchInfo: List<ModelBatch>? = null
        /**
         * 裂变信息
         */
        var kontext: Kontext? = null
        /**
         * MJ信息
         */
        var midJourney: MidJourney? = null
    }
    class ModelBatch{
        /**
         * 模型名称 chaoji、lazada、huiwa
         */
        var modelName: String? = null

        /**
         * tryOn生成数
         */
        var tryOnBatchSize: Int? = null
    }

    class Kontext{
        /**
         * tryOn生成数
         */
        var kontextBatchSize: Int? = null
    }

    class MidJourney{
        /**
         * moodboardId
         */
        var moodboardId: String? = null
    }
}