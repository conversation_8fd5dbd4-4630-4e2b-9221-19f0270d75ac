package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotNull

/**
 * 矩形坐标Req
 */
data class RectangleCoordinateReq(
    /**
     * X坐标起点
     */
    @field:NotNull(message = "X坐标起点不能空")
    var xmin: Int? = null,
    /**
     * Y坐标起点
     */
    @field:NotNull(message = "Y坐标起点不能空")
    var ymin: Int? = null,
    /**
     * X坐标终点
     */
    @field:NotNull(message = "X坐标终点不能空")
    var xmax: Int? = null,
    /**
     * Y坐标终点
     */
    @field:NotNull(message = "Y坐标终点不能空")
    var ymax: Int? = null,

    ) {
    companion object {
        private const val serialVersionUID = 1L
    }
}

