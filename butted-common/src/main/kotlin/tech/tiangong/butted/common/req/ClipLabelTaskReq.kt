package tech.tiangong.butted.common.req

/**
 * 提取标签任务Req
 *
 * <AUTHOR>
 */
data class ClipLabelTaskReq(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    var callback: String? = null,

    ) : ClipLabelSampleReq() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
