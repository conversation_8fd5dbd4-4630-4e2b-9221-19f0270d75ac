package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 美图智能抠图模型Vo
 * {
 *         "code": 0,
 *         "data": {
 *                 "status": 10,
 *                 "result": {
 *                         "id": "1cc79381-d367-4630-a7bc-0633fbc72637",
 *                         "urls": ["https://obs.mtlab.meitu.com/mtopen/7071f5094b73406da8db0136fc1f7831/MTY4OTgzNjQwMA==/50a84e28-d3e9-453b-b486-da758c5b88cd.jpg"],
 *                         "parameters": {
 *                                 "Kind": 0,
 *                                 "bottom_x": "None",
 *                                 "bottom_y": "None",
 *                                 "exist_salient": true,
 *                                 "process_time": 120.13816833496094,
 *                                 "pull_time": 88.46640586853027,
 *                                 "rsp_media_type": "url",
 *                                 "top_x": "None",
 *                                 "top_y": "None",
 *                                 "use_fe": true,
 *                                 "version": "2.4.0"
 *                         }
 *                 },
 *                 "progress": 1,
 *                 "predict_elapsed": 10000
 *         }
 * }
 *
 */
data class MeituMattingResultParametersInnerVo(


    /**
     * 0：表示人像；1：表示商品；2：表示图形
     */
    var Kind: Int? = null,
    /**
     * 右下角X坐标
     */
    var bottom_x: String? = null,
    /**
     * 右下角Y坐标
     */
    var bottom_y: String? = null,
    /**
     * 表示是否有显著性目标
     */
    var exist_salient: Boolean? = null,

    /**
     * ‘jpg’，表示 media_data 是经过 base64 压缩过的图片
     */
    var rsp_media_type: String? = null,
    /**
     * 左上角X坐标
     */
    var top_x: String? = null,
    /**
     * 左上角Y坐标
     */
    var top_y: String? = null,

    /**
     * true:表示需要使用前景估计，false:表示不需要使用前景估计
     */
    var use_fe: Boolean? = null,
    /**
     * 版本号
     */
    var version: String? = null,
    ) : Serializable {

    companion object {
        private const val serialVersionUID = 1L
    }
}