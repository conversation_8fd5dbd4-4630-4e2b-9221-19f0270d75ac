package tech.tiangong.butted.common.req

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 批量推面料项请求
 *
 * <AUTHOR>
 */
data class BatchRecommendFabricItemReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 输入图片
     */
    @field:NotBlank(message = "输入图片不能为空")
    var inputImage: String? = null,


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
