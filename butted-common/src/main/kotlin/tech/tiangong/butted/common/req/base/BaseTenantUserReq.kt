package tech.tiangong.butted.common.req.base

import java.io.Serializable

/**
 * BaseUserReq
 *
 * <AUTHOR>
 */
open class BaseTenantUserReq(
    /**
     * 租户ID
     */
    var tenantId: Long? = null,
    /**
     * 创建人ID
     */
    var creatorId: Long? = null,
    /**
     * 创建人名称
     */
    var creatorName: String? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
