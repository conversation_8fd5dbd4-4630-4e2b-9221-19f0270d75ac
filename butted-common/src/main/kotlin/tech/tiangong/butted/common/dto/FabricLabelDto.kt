package tech.tiangong.butted.common.dto

import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.util.json.toJson
import java.io.Serializable

/**
 * 面料标签VO
 */
data class FabricLabelDto(
    /**
     * 面料市场名
     */
    var marketName: String? = null,
    /**
     * 面料市场名英文
     */
    var marketNameEn: String? = null,
    /**
     * 色号
     */
    var colorCode: String? = null,
    /**
     * 色相/色系
     */
    var colorHue: String? = null,

    ) : Serializable {
    companion object {
        const val serialVersionUID = 1L
    }

    fun checkToJson(): String? {
        if (marketName.isBlank() && marketNameEn.isBlank() && colorCode.isBlank() && colorHue.isBlank()) {
            return null
        }
        return this.toJson()
    }


}