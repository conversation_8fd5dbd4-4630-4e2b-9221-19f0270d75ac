package tech.tiangong.butted.common.req

import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 通义千问新增参数
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:53
 * @version    :1.0
 */
class FluxOutpaintTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 正向提示词
     */
    var prompt: String? = null,
    /**
     * 生成数量
     */
    @field:NotNull(message = "生成数量不能空")
    @field:Min(value = 1, message = "生成数量不能小于1")
    var genCount: Int? = null,
    /**
     * 文本控制生成图像程度,默认3.5,值越大,图像越贴近提示但可能质量较低
     */
    var guidanceScale: BigDecimal? = null,
    /**
     * 推理步数
     */
    var steps: Int? = null,
    /**
     * 扩图左侧填充像素数,默认为100
     */
    var leftPad: Int? = null,

    /**
     * 扩图右侧填充像素数,默认为100
     */
    var rightPad: Int? = null,
    /**
     * 扩图上方填充像素数,默认为100
     */
    var topPad: Int? = null,
    /**
     * 扩图下方填充像素数,默认为100
     */
    var bottomPad: Int? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -7232405652188808306L
    }
}