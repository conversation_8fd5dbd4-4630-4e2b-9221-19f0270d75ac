package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotNull

/**
 * 坐标Int
 *```json
 * {"X": 922, "Y": 515}
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 16:19
 * @version    :1.0
 */
class CoordinateIntDto(
    @JsonProperty(value = "X")
    @field:NotNull(message = "x坐标不能为空")
    val x: Int,
    @JsonProperty(value = "Y")
    @field:NotNull(message = "y坐标不能为空")
    val y: Int
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 7693548192273640125L
    }
}