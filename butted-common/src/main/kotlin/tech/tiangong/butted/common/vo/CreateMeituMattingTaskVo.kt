package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 美图抠图任务Vo
 *
 * <AUTHOR>
 */
data class CreateMeituMattingTaskVo(
    /**
     * 任务ID
     */
    var taskId: Long? = null,

    /**
     * 生成图
     */
    var resImg: String? = null,

    /**
     * 0：表示人像；1：表示商品；2：表示图形
     */
    var Kind: Int? = null,
    /**
     * 右下角X坐标
     */
    var bottom_x: String? = null,
    /**
     * 右下角Y坐标
     */
    var bottom_y: String? = null,
    /**
     * 表示是否有显著性目标
     */
    var exist_salient: Boolean? = null,

    /**
     * ‘jpg’，表示 media_data 是经过 base64 压缩过的图片
     */
    var rsp_media_type: String? = null,
    /**
     * 左上角X坐标
     */
    var top_x: String? = null,
    /**
     * 左上角Y坐标
     */
    var top_y: String? = null,

    /**
     * true:表示需要使用前景估计，false:表示不需要使用前景估计
     */
    var use_fe: Boolean? = null,
    /**
     * 版本号
     */
    var version: String? = null
) : Serializable {


    companion object {
        private const val serialVersionUID = 1L
    }
}
