package tech.tiangong.butted.common.req

import tech.tiangong.butted.common.enums.BizSourceEnum
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import jakarta.validation.constraints.NotBlank

/**
 * 提取标签任务Req
 *
 * <AUTHOR>
 */
data class PoseTaskReq(
    /**
     * 来源
     * @see BizSourceEnum
     */
    var source: BizSourceEnum? = null,
    /**
     * 来源ID
     */
    var sourceId: Long? = null,
    /**
     * 输入图片URL
     */
    @field:NotBlank(message = "输入图片URL不能为空")
    var inputImage: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    var callback: String? = null,

    ) : BaseTenantUserReq() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
