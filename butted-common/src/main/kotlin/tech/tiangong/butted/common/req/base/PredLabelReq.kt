package tech.tiangong.butted.common.req.base

import java.io.Serializable

/**
 * 算法标签Req
 */
data class PredLabelReq(
    /**
     * 中文标签
     */
    var cn: LabelValueReq? = null,
    /**
     * 英文标签
     */
    var en: LabelValueReq? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }

    fun valid(): Boolean {
        return cn?.valid() == true || en?.valid() == true
    }

    fun validIncludeCode(): Boolean {
        return cn?.validIncludeCode() == true || en?.validIncludeCode() == true
    }
}