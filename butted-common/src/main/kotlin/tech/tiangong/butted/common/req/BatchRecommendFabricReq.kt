package tech.tiangong.butted.common.req

import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import java.io.Serializable
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 批量推面料请求
 *
 * <AUTHOR>
 */
data class BatchRecommendFabricReq(
    /**
     * 智能开款ID
     */
    @field:NotNull(message = "智能开款ID不能空")
    var smartDevelopId: Long? = null,
    /**
     * 任务属性：0-单任务，1-批量任务
     */
    var taskAttribute: Int? = null,
    /**
     * 回调URL
     */
    var callback: String? = null,
    /**
     * 重推面料列表
     */
    @field:NotEmpty(message = "重推面料列表不能空")
    var recommendList: List<BatchRecommendFabricItemReq>? = listOf(),


    ) : BaseTenantUserReq(), Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
