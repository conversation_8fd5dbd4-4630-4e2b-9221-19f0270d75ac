package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * ptn_mark打标签任务新增参数
 *
 * <AUTHOR>
 * @date       ：2025/2/17 10:45
 * @version    :1.0
 */
class PtnMarkTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -6524908485147161240L
    }

}