package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.DurationBaseTaskVo

/**
 * 智能识别响应VO
 */
open class SmartIdentifyTaskVo(
    /**
     * 智能识别ID
     */
    var smartIdentifyId: Long? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,
    /**
     * 参考图类型：0-非真人模特图、1-真人模特图
     */
    var refImgType: Int? = null,
    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: Int? = null,
    /**
     * 识别品类
     */
    var category: String? = null,
    /**
     * 识别品类编号
     */
    var categoryCode: String? = null,

    /**
     * 分割标签列表
     */
    var clipLabelList: List<PredLabelVo>? = null,
    /**
     * 花型标签列表
     */
    var flowerPatternLabelList: List<PredLabelVo>? = null,
//    /**
//     * 风格标签列表（已作废,不再做风格标签了）
//     */
//    var styleLabelList: List<PredLabelVo>? = null,
    /**
     * 面料标签
     */
    var fabricLabel: FabricLabelVo? = null,


    ) : DurationBaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }

}
