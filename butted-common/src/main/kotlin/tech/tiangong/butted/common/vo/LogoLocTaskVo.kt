package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import java.math.BigDecimal

/**
 * 区域定位任务VO
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:56
 * @version    :1.0
 */
class LogoLocTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * CAD平铺图url
     */
    var flatImgUrl: String? = null,
    /**
     * 品类
     */
    var category: String? = null,
    /**
     * CAD信息
     */
    var cadInfo: String? = null,

    /**
     * 自定义区域信息
     */
    var customRegion: String? = null,
    /**
     * 正背面:
     * 默认0，0正面，1背面
     */
    var direction: Int? = null,

    /**
     * 旋转角度
     */
    var rotationAngle: BigDecimal? = null,

    /**
     * LOGO信息
     */
    var logoRegion: String? = null,

    /**
     * MASK信息
     */
    var maskRegion: String? = null,
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 6232740479951549746L
    }

}