package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo

/**
 * 美图抠图任务Vo
 *
 * <AUTHOR>
 */
data class MeituMattingTaskInnerVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 生成图
     */
    var resImg: String? = null,

    /**
     * 返回结果参数
     */
    var resultParameters: MeituMattingResultParametersInnerVo? = null
    ) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
