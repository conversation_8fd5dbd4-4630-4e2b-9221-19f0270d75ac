package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo

/**
 * 4K高清任务Vo
 *
 * <AUTHOR>
 */
data class UltraHdTaskVo(
    /**
     * 原始任务ID
     */
    var originTaskId: Long? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 4K任务模型：FLOWER_PATTERN_EXTRACT-花型提取，SMART_DESIGN-智能设计生图
     */
    var taskMode: String? = null,
    /**
     * 图片ID
     */
    var pictureId: Long? = null,
    /**
     * 图片url
     */
    var pictureUrl: String? = null,
    /**
     * 生成图
     */
    var resImg: String? = null,

    ) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
