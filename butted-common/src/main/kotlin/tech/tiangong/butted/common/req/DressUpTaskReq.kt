package tech.tiangong.butted.common.req

import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import java.io.Serial
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * 服装上身任务Req
 *
 * <AUTHOR>
 */
data class DressUpTaskReq(
    /**
     * clip任务ID
     */
    @field:NotNull(message = "Clip任务ID不能为空")
    var clipTaskId: Long? = null,
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能为空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotNull(message = "业务编号不能为空")
    var busCode: String? = null,
    /**
     * 品类名称
     */
    var categoryName: String? = null,
    /**
     * 服装图
     */
    @field:NotBlank(message = "服装图不能为空")
    var clothingImg: String? = null,
    /**
     * 模特图
     */
    @field:NotBlank(message = "模特图不能为空")
    var modelImg: String? = null,
    /**
     * 模特图Mark区域
     */
    //@field:NotBlank(message = "模特图Mark区域不能为空")
    var modelMarkImg: String? = null,

    /**
     * 生成数量count
     */
    @field:NotNull(message = "生成数量不能空")
    @field:Min(value = 1, message = "数量不能小于1")
    @field:Max(value = 4, message = "数量不能大于4")
    var genCount: Int? = null,

    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) : BaseTenantUserReq() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }

}
