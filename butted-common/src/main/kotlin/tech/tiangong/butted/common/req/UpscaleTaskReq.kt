package tech.tiangong.butted.common.req

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 超分任务新增参数
 *
 * <AUTHOR>
 * @date       ：2025/2/17 10:45
 * @version    :1.0
 */
class UpscaleTaskReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 参考图url
     */
    @field:NotEmpty(message = "参考图url不能空")
    var refImgUrl: String? = null,
    /**
     * 放大倍数
     */
    @field:NotNull(message = "放大倍数不能为空")
    @field:Min(value = 1, message = "放大倍数最小大于等于1")
    @field:Max(value = 20, message = "放大倍数最大小于等于20")
    var enlarge: Int,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1213783536722876702L
    }

}