package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 面料标签VO
 */
data class FabricLabelVo(
    /**
     * 面料市场名
     */
    val marketName: String? = null,
    /**
     * 面料市场名英文
     */
    var marketNameEn: String? = null,
    /**
     * 色号
     */
    val colorCode: String? = null,
    /**
     * 色相/色系
     */
    val colorHue: String? = null,

    ) : Serializable {
    companion object {
        const val serialVersionUID = 1L
    }

}