package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * 裁剪任务Vo
 *
 * <AUTHOR>
 */
data class CroppingTaskVo(

    /**
     * 业务主键ID
     */
    var busId: Long? = null,

    /**
     * 业务编号
     */
    var busCode: String? = null,

    /**
     * 输入图片URL
     */
    var inputImage: String? = null,

    /**
     * 裁剪后的图像大小，默认：1400x1400 （宽×高）
     */
    var size: String? = null,

    /**
     * 生成数量count
     */
    var genCount: Int? = null,

    /**
     * 生成图
     */
    var resImg: String? = null,

    ) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
