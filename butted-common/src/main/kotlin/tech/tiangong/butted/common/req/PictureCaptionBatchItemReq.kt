package tech.tiangong.butted.common.req;

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 图片描述说明任务Req
 *
 * <AUTHOR>
 */
data class PictureCaptionBatchItemReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 输入图片
     */
    @field:NotBlank(message = "输入图片不能为空")
    var inputImg: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
