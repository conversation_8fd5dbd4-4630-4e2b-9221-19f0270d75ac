package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import java.io.Serial

/**
 * 裁剪任务Req
 *
 * <AUTHOR>
 */
data class CroppingTaskReq(

    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,

    /**
     * 业务编号
     */
    var busCode: String? = null,

    /**
     * 输入图片URL
     */
    @field:NotBlank(message = "输入图片URL不能空")
    var inputImage: String? = null,

    /**
     * 裁剪后的图像大小，默认：1340x1785 （宽×高）
     */
    var size: String? = null,

    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能空")
    var callback: String? = null,


    ) : BaseTenantUserReq() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
