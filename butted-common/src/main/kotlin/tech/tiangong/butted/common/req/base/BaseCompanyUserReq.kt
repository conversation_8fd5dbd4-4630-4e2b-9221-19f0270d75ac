package tech.tiangong.butted.common.req.base

import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 用户租户请求参数
 *
 * <AUTHOR>
 * @date       ：2024/11/25 10:01
 * @version    :1.0
 */
open class BaseCompanyUserReq(
    /**
     * 创建人id
     */
    @field:NotNull(message = "创建人ID不能为空")
    var creatorId: Long,
    /**
     * 创建人
     */
    @field:NotEmpty(message = "创建人名称不能为空")
    var creatorName: String,
    /**
     * 租户id
     */
    @field:NotNull(message = "租户ID不能为空")
    var companyId: Long,
) : Serializable {
    /**
     * 回调URL
     */
    var callback: String? = null

    companion object {
        @Serial
        private const val serialVersionUID: Long = -8255685204766017182L
    }
}