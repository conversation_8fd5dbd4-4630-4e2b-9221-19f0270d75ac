package tech.tiangong.butted.common.vo.tryon

import java.io.Serial
import java.io.Serializable

/**
 * 虚拟换装关联的第三方算法任务ID VO
 */
data class TryOnRelatedThirdIdVo(
    /**
     * 虚拟换衣关联的第三方算法任务ID
     */
    var tryOnThirdId: String? = null,
    /**
     * AI换脸关联的第三方算法任务ID
     */
    var shiftFaceThirdId: String? = null,
    /**
     * 换背景关联的第三方算法任务ID列表
     */
    var shiftSceneThirdIdList: List<String>? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID = 1L
    }
}
