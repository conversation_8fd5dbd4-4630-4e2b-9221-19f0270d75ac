package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import java.math.BigDecimal

/**
 * image_gen任务VO
 *
 * <AUTHOR>
 * @date       ：2024/11/25 17:07
 * @version    :1.0
 */
class ImageGenTaskVo(
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 正向提示词
     */
    var prompt: String? = null,

    /**
     * 负向提示词
     */
    var negPrompt: String? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,

    /**
     * 参考图宽
     */
    var imgWidth: Int? = null,

    /**
     * 参考图高
     */
    var imgHeight: Int? = null,

    /**
     * 随机数种子
     */
    var seed: Int? = null,

    /**
     * 参考范围
     */
    var refScale: BigDecimal? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -1429021197341371260L
    }
}