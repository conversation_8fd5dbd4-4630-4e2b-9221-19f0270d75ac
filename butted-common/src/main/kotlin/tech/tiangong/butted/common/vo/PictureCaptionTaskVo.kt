package tech.tiangong.butted.common.vo;

import tech.tiangong.butted.common.vo.base.BaseTaskVo

/**
 * 图片描述说明任务Vo
 *
 * <AUTHOR>
 */
data class PictureCaptionTaskVo(
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
    /**
     * 输入图片
     */
    var inputImg: String? = null,
    /**
     * 图片描述
     */
    var caption: String? = null,

    ) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
