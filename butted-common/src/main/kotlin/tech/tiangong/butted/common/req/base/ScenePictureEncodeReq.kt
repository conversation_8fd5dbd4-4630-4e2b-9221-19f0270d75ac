package tech.tiangong.butted.common.req.base;

import jakarta.validation.constraints.NotBlank
import java.io.Serial
import java.io.Serializable

/**
 * FM场景图编码Req
 *
 * <AUTHOR>
 */
data class ScenePictureEncodeReq(
    /**
     * 模型编码（字典编码）
     */
    @field:NotBlank(message = "模型编码不能为空")
    var modeCode: String? = null,
    /**
     * 模型名称（字典名称）
     */
    @field:NotBlank(message = "模型名称不能为空")
    var modeName: String? = null,
    /**
     * 场景图编码
     */
    @field:NotBlank(message = "场景图编码不能为空")
    var encode: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
