package tech.tiangong.butted.common.vo.category

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * 营销市场品类识别任务
 *
 */
data class MarketCategoryTaskBatchCreateVo(
    /**
     * 任务id
     */
    var taskId : Long ? = null,
    /**
     * 输入图片URL
     */
    var inputImage: String? = null,


    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
