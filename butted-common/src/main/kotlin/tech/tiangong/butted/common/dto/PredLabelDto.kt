package tech.tiangong.butted.common.dto

import java.io.Serializable

/**
 * 标签DTO
 *
 * <AUTHOR>
 * @date       ：2024/4/15 下午5:30
 * @version    :1.0
 */
class PredLabelDto(
    /**
     * 中文标签
     */
    var cn: LabelValueDto? = null,
    /**
     * 英文标签
     */
    var en: LabelValueDto? = null,
    /**
     * coloro的编码，非颜色标签不存在此字段；多个颜色用逗号分隔
     */
    var coloroCodes: String? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    fun valid(): Boolean {
        return cn?.valid() == true || en?.valid() == true
    }

    fun validIncludeCode(): Boolean {
        return cn?.validIncludeCode() == true || en?.validIncludeCode() == true
    }

    fun reserveIndexValue(fillIndexSet: Set<Int>) {
        cn?.reserveIndexValue(fillIndexSet)
        en?.reserveIndexValue(fillIndexSet)
    }

}
