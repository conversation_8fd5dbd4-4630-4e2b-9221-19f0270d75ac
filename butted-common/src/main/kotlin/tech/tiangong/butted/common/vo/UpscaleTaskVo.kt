package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * 超分任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class UpscaleTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 放大倍率
     */
    var enlargeRatio: Int? = null,
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -1720887894583414217L
    }


}