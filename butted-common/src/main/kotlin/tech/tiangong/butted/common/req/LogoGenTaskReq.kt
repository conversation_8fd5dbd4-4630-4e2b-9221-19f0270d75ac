package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal

/**
 * logo_gen任务新增参数
 *
 * <AUTHOR>
 * @date       ：2025/2/17 10:45
 * @version    :1.0
 */
class LogoGenTaskReq(

) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 5566904964337936808L
    }
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null
    /**
     * 业务编号
     */
    @field:NotEmpty(message = "业务编号不能空")
    var busCode: String? = null
    /**
     * 参考图url
     */
    var refImgUrl: String? = null
    /**
     * 采样次数，默认为50
     */
    var samplingNum: Int? = null
    /**
     * 生成数量
     */
    @field:NotNull(message = "生成数量不能为空")
    var genCount: Int? = null
    /**
     * 图片参考范围
     */
//    @field:NotNull(message = "图片参考范围不能为空")
    var imageScale: BigDecimal? = null
    /**
     * 图片描述参考范围
     */
//    @field:NotNull(message = "图片描述参考范围不能为空")
    var textScale: BigDecimal? = null
    /**
     * 提示词
     */
    @field:NotEmpty(message = "提示词不能空")
    var prompt: String? = null

}