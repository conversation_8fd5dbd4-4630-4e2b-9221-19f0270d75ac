package tech.tiangong.butted.common.req

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.req.base.BaseTenantUserReq
import tech.tiangong.butted.common.req.base.PredLabelReq
import java.io.Serial

/**
 * AI素材任务Req
 *
 * <AUTHOR>
 */
data class DesignMaterialTaskReq(

    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,

    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,

    /**
     * Clip任务ID
     */
    @field:NotNull(message = "Clip任务ID不能空")
    var clipTaskId: Long? = null,
    /**
     * 提交的Clip标签
     */
    var submitClipLabelList: List<PredLabelReq>? = null,

    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: Int? = null,

    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 品类编号
     */
    var categoryCode: String? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,

    /**
     * 背景图（场景）
     */
    var bgImgInfo: RefImgReq? = null,

//    /**
//     * 模特图
//     */
//    var modelImgInfo: RefImgReq? = null,
    /**
     * 模特（面容）ID（模特素材ID）
     */
    var modelId: Long? = null,

    /**
     * 模特编码（字典编码）
     */
    var modelCode: String? = null,

    /**
     * 模特（面容）名称
     */
    var modelName: String? = null,

    /**
     * 模特（面容）图片URL
     */
    var modelUrl: String? = null,

    /**
     * 模特（面容）图片描述
     */
    var modelCaption: String? = null,

    /**
     * 是否生成多姿势，1-是，0-否
     */
    @field:NotNull(message = "是否生成多姿势不能为空")
    var multiPose: Int? = null,

    /**
     * 脸部修复：0-否；1-是
     */
    @field:NotNull(message = "脸部修复不能为空")
    var faceRepair: Int? = null,

    /**
     * 角度：正面-FRONT,背面-BACK
     * 单姿势必传；多姿势不要传
     */
    var viewpoint: String? = null,

    /**
     * 生成数量
     */
    @field:NotNull(message = "生成图片数量不能为空")
    @field:Min(value = 1, message = "生成图片数量不能小于1")
    @field:Max(value = 40, message = "生成图片数量不能大于40")
    var genCount: Int? = null,
    /**
     * 生成图像大小，默认：1340x1785 （宽×高）
     */
    var size: String? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,


    ) : BaseTenantUserReq() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }

}
