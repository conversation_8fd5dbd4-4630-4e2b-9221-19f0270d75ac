package tech.tiangong.butted.common.req.tryon

import jakarta.validation.Valid
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.TryOnTypeEnum
import tech.tiangong.butted.common.req.base.ModelMaterialEncodeReq
import tech.tiangong.butted.common.req.base.ScenePictureEncodeReq
import java.io.Serializable

/**
 * 虚拟换装任务Req
 *
 * <AUTHOR>
 */
data class TryOnTaskCreateReq(
    /**
     * 模型编码（字典配置编码）
     */
    @field:NotBlank(message = "模型编码不能空")
    var modeCode: String? = null,
    /**
     * 模型名称（字典配置名称）
     */
    @field:NotBlank(message = "模型名称不能空")
    var modeName: String? = null,

    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    @field:NotBlank(message = "业务编号不能空")
    var busCode: String? = null,
    /**
     * 品类名
     */
    var category: String? = null,

    /**
     * 换装类型
     */
    @field:NotNull(message = "换装类型不能空")
    var tryOnType: TryOnTypeEnum? = null,

    /**
     * 上装图
     */
    var upperClothImg: String? = null,
    /**
     * 上装Mask图
     */
    var upperClothMaskImg: String? = null,

    /**
     * 下装图
     */
    var lowerClothImg: String? = null,
    /**
     * 下装Mask图
     */
    var lowerClothMaskImg: String? = null,

    /**
     * 全身（连体）图
     */
    var overallClothImg: String? = null,
    /**
     * 全身（连体）Mask图
     */
    var overallClothMaskImg: String? = null,


    /**
     * 模特参考图
     */
    @field:NotBlank(message = "模特参考图不能空")
    var humanImgUrl: String? = null,

    /**
     * 模特参考图重绘区域
     */
    //@field:NotBlank(message = "模特参考图重绘区域不能空")
    var humanMaskImgUrl: String? = null,

    /**
     * 场景图片ID
     */
    var scenePictureId: Long? = null,
    /**
     * 场景图片URL
     */
    var scenePicturePath: String? = null,
    /**
     * 场景图片输出尺寸：
     * 1比1：传one_to_one
     * 原尺寸：传original
     */
    var scenePictureOutputSize: String? = null,
    /**
     * 场景图片编码信息
     */
    @field:Valid
    var scenePictureEncode: ScenePictureEncodeReq? = null,

    /**
     * 指定模特（用于AI换脸）
     */
    var modelImgUrl: String? = null,
    /**
     * 指定模特（用于AI换脸）编码信息
     */
    @field:Valid
    var modelImgEncode: ModelMaterialEncodeReq? = null,

//    /**
//     * 手脚修复：0-否；1-是
//     */
//    @field:NotNull(message = "手脚修复不能空")
//    var repairHandFoot: Int? = null,

    /**
     * 生成数量
     */
    @field:NotNull(message = "生成数量不能空")
    @field:Min(value = 1, message = "数量不能小于1")
    @field:Max(value = 4, message = "数量不能大于4")
    var genCount: Int? = null,

    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能空")
    var callback: String? = null,


    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
