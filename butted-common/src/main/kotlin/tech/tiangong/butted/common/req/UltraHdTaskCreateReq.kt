package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.req.base.BaseTenantUserReq

/**
 * 4K图Req
 */
data class UltraHdTaskCreateReq(
    /**
     * 业务主键ID
     */
    @field:NotNull(message = "业务主键ID不能为空")
    var busId: Long? = null,
    /**
     * 业务编号
     */
    var busCode: String? = null,
    /**
     * 4K任务模型：FLOWER_PATTERN_EXTRACT-花型提取，SMART_DESIGN-智能设计生图，DRESS_UP-服装上身图，TRY_ON-虚拟换衣，DESIGN_MATERIAL-AI素材
     */
    @field:NotBlank(message = "4K任务模型不能为空")
    var taskMode: String? = null,
    /**
     * 图片url
     */
    @field:NotBlank(message = "图片url不能为空")
    var pictureUrl: String? = null,
    /**
     * 回调URL
     */
    @field:NotBlank(message = "回调URL不能为空")
    var callback: String? = null,

    ) : BaseTenantUserReq() {
    companion object {
        private const val serialVersionUID = 1L
    }
}
