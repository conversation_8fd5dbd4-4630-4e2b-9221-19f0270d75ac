package tech.tiangong.butted.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import tech.tiangong.butted.common.enums.TaskStatusEnum
import java.io.Serializable

/**
 * 任务同步状态请求
 */
data class TaskSyncStatusBatchReq(
    /**
     * 业务主键ID
     */
    @field:NotEmpty(message = "业务主键ID不能空")
    var busIdList: List<Long>? = null,
    /**
     * 任务模型
     */
    @field:NotBlank(message = "任务模型不能为空")
    var taskMode: String? = null,
    /**
     * 当前任务状态
     */
    @field:NotNull(message = "当前任务状态不能为空")
    var taskStatus: TaskStatusEnum? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }


}