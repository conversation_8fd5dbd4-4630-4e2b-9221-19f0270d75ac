package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * logo_det任务VO
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:03
 * @version    :1.0
 */
class LogoDetTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * logo坐标信息
     */
    var outputBoxes: String? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -3075111488361166192L
    }
}