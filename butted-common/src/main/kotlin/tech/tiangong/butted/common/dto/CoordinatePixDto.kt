package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotNull

/**
 * 坐标信息
 *```json
 *  //可印区域像素坐标，数据类型为整形，渲染图片左上角为坐标原点
 * {
 * 	"LeftTop_X": 0,
 * 	"LeftTop_Y": 0,
 * 	"RightTop_X": 100,
 * 	"RightTop_Y": 0,
 * 	"LeftBotton_X": 0,
 * 	"LeftBotton_Y": 100,
 * 	"RightBotton_X": 100,
 * 	"RightBotton_Y": 100,
 * 	"Center_X": 50,
 * 	"Center_Y": 50
 * }
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 16:01
 * @version    :1.0
 */
class CoordinatePixDto(
    @JsonProperty(value = "LeftTop_X")
    @field:NotNull(message = "左上角x坐标不能为空")
    val leftTopX: Double,
    @JsonProperty(value = "LeftTop_Y")
    @field:NotNull(message = "左上角y坐标不能为空")
    val leftTopY: Double,
    @JsonProperty(value = "RightTop_X")
    @field:NotNull(message = "右上角x坐标不能为空")
    val rightTopX: Double,
    @JsonProperty(value = "RightTop_Y")
    @field:NotNull(message = "右上角y坐标不能为空")
    val rightTopY: Double,
    @JsonProperty(value = "LeftBotton_X")
    @field:NotNull(message = "左下角x坐标不能为空")
    val leftButtonX: Double,
    @JsonProperty(value = "LeftBotton_Y")
    @field:NotNull(message = "左下角y坐标不能为空")
    val leftButtonY: Double,
    @JsonProperty(value = "RightBotton_X")
    @field:NotNull(message = "右下角x坐标不能为空")
    val rightButtonX: Double,
    @JsonProperty(value = "RightBotton_Y")
    @field:NotNull(message = "左下角y坐标不能为空")
    val rightButtonY: Double,
    @JsonProperty(value = "Center_X")
    @field:NotNull(message = "中心x坐标不能为空")
    val centerX: Double,
    @JsonProperty(value = "Center_Y")
    @field:NotNull(message = "中心y坐标不能为空")
    val centerY: Double,
    @JsonProperty(value = "Width")
    @field:NotNull(message = "宽不能为空")
    val width: Double = 0.00,
    @JsonProperty(value = "Height")
    @field:NotNull(message = "高不能为空")
    var height: Double = 0.00,

    ) : Serializable {
    @JsonProperty(value = "R_LeftTop_X")
    @field:NotNull(message = "左上角x坐标不能为空")
    var rLeftTopX: Double? = null

    @JsonProperty(value = "R_LeftTop_Y")
    @field:NotNull(message = "左上角y坐标不能为空")
    var rLeftTopY: Double? = null

    @JsonProperty(value = "R_RightTop_X")
    @field:NotNull(message = "右上角x坐标不能为空")
    var rRightTopX: Double? = null

    @JsonProperty(value = "R_RightTop_Y")
    @field:NotNull(message = "右上角y坐标不能为空")
    var rRightTopY: Double? = null

    @JsonProperty(value = "R_LeftBotton_X")
    @field:NotNull(message = "左下角x坐标不能为空")
    var rLeftButtonX: Double? = null

    @JsonProperty(value = "R_LeftBotton_Y")
    @field:NotNull(message = "左下角y坐标不能为空")
    var rLeftButtonY: Double? = null

    @JsonProperty(value = "R_RightBotton_X")
    @field:NotNull(message = "右下角x坐标不能为空")
    var rRightButtonX: Double? = null

    @JsonProperty(value = "R_RightBotton_Y")
    @field:NotNull(message = "左下角y坐标不能为空")
    var rRightButtonY: Double? = null

    companion object {
        @Serial
        private const val serialVersionUID: Long = 9148910337165085755L
    }
}