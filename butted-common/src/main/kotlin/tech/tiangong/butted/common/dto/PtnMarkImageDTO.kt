package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable

/**
 * ptn_mark结果DTO
 *
 * <AUTHOR>
 * @date       ：2025/4/23 17:05
 * @version    :1.0
 */
class PtnMarkImageDTO(
    @JsonProperty(value = "image_url")
    var imageUrl: String? = null,
    @JsonProperty(value = "task_id")
    var taskId: Long? = null,
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -2879799562317513191L
    }
}