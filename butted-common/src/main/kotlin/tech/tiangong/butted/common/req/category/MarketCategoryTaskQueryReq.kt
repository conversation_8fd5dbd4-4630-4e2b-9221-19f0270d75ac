package tech.tiangong.butted.common.req.category

import java.io.Serializable
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * 营销市场品类识别任务
 *
 */
data class MarketCategoryTaskQueryReq(

    /**
     * 任务ID
     */
    @field:NotEmpty(message = "输入任务ID不能为空")
    var taskIds: List<Long>? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
