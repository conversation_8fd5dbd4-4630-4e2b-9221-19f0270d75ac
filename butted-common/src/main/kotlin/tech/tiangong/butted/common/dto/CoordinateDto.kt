package tech.tiangong.butted.common.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serial
import java.io.Serializable
import jakarta.validation.constraints.NotNull

/**
 * 坐标
 *```json
 * {"X": 922, "Y": 515}
 *```
 * <AUTHOR>
 * @date       ：2025/1/6 15:26
 * @version    :1.0
 */
class CoordinateDto(
    @JsonProperty(value = "X")
    @field:NotNull(message = "x坐标不能为空")
    val x: Double,
    @JsonProperty(value = "Y")
    @field:NotNull(message = "y坐标不能为空")
    val y: Double
) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1407320553697450224L
    }
}