package tech.tiangong.butted.common.vo.base

import com.fasterxml.jackson.annotation.JsonFormat
import tech.tiangong.butted.common.enums.TaskStatusEnum
import java.time.LocalDateTime
import java.time.ZoneId

/**
 * DurationBaseTaskVo
 */
open class DurationBaseTaskVo(
    /**
     * 排队时长（秒）
     */
    var queueDuration: Int? = null,
    /**
     * 生成时长（秒）
     */
    var generatedDuration: Int? = null,
    /**
     * 生成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var generatedTime: LocalDateTime? = null,

    ) : BaseTaskVo() {
    companion object {
        private const val serialVersionUID: Long = 1L
    }


    fun calculateDuration() {
        val taskStartTime = aiStartTime ?: LocalDateTime.now()
        queueDuration = (toEpochSeconds(taskStartTime) - toEpochSeconds(createdTime!!)).toInt()
        if (TaskStatusEnum.completed(this.taskStatus)) {
            if (aiEndTime != null) {
                generatedTime = aiEndTime!!
                val generateStartTime = aiStartTime ?: pushTime ?: createdTime!!
                generatedDuration = (toEpochSeconds(aiEndTime!!) - toEpochSeconds(generateStartTime)).toInt()
            }
        }
    }

    private fun toEpochSeconds(localDateTime: LocalDateTime): Long {
        val zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
        val instant = zonedDateTime.toInstant()
        return instant.epochSecond
    }
}
