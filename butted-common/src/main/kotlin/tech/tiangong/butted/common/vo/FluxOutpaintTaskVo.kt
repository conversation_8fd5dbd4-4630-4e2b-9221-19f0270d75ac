package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import java.math.BigDecimal

/**
 * 扩图模型任务VO
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:56
 * @version    :1.0
 */
class FluxOutpaintTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 正向提示词
     */
    var prompt: String? = null,

    /**
     * 扩图左侧填充像素数,默认为100
     */
    var leftPad: Int? = null,

    /**
     * 扩图右侧填充像素数,默认为100
     */
    var rightPad: Int? = null,
    /**
     * 扩图上方填充像素数,默认为100
     */
    var topPad: Int? = null,
    /**
     * 扩图下方填充像素数,默认为100
     */
    var bottomPad: Int? = null,
    /**
     * 随机数种子
     */
    var seed: Int? = null,
    /**
     * 文本控制生成图像程度,默认3.5,值越大,图像越贴近提示但可能质量较低
     */
    var guidanceScale: BigDecimal? = null,

    /**
     * 生成数量
     */
    var genCount: Int? = null,
    /**
     * 推理步数,默认30
     */
    var numInferenceSteps: Int? = null,
    /**
     * 生成图（多张逗号,隔开）
     */
    var resImgs: String? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 6865035696115448465L
    }
}