package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo

/**
 * 分割任务Vo
 *
 * <AUTHOR>
 */
data class PartitionTaskVo(
    /**
     * 来源类型：虚拟换衣：try_on；服装上身：dress_up；
     */
    var sourceType: String? = null,
    /**
     * 算法
     */
    var algorithms: String? = null,
    /**
     * 参考图URL
     */
    var refImgUrl: String? = null,
    /**
     * 分割方法：服饰分割-clothseg
     */
    var method: String? = null,
    /**
     * 服饰分割类型：上装-upper；下装-lower；全身-overall
     */
    var cateToken: String? = null,

    /**
     * 分割图片列表
     */
    var maskList: List<PartitionMaskVo>? = null,

    ) : BaseTaskVo() {

    companion object {
        private const val serialVersionUID = 1L
    }
}
