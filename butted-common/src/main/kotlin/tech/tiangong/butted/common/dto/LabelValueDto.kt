package tech.tiangong.butted.common.dto

import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import java.io.Serializable

/**
 * 标签值DTO
 *
 * <AUTHOR>
 * @date       ：2024/4/15 下午5:29
 * @version    :1.0
 */
class LabelValueDto(
    /**
     * 标签名
     */
    var name: String? = null,
    /**
     * 标签编号
     */
    var code: String? = null,
    /**
     * 标签值列表
     */
    var values: List<LabelValueDto>? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    fun validIncludeCode(): Boolean {
        val checkValues = if (values.isEmpty()) {
            true
        } else {
            values!!.all { it.validIncludeCode() }
        }
        return name.isNotBlank() && code.isNotBlank() && checkValues
    }

    fun valid(): Boolean {
        val checkValues = if (values.isEmpty()) {
            true
        } else {
            values!!.all { it.valid() }
        }
        return name.isNotBlank() && checkValues
    }

    fun reserveIndexValue(fillIndexSet: Set<Int>) {
        this.values = this.values?.filterIndexed { index, lvd ->
            index in fillIndexSet
        }
    }

}