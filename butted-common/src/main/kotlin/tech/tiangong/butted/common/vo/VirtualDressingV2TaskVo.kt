package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial

/**
 * VirtualDressingV2Task任务VO
 *
 */
class VirtualDressingV2TaskVo(

    /**
     * 算法结果
     */
    var comfyuiHistoryResult: String? = null, // 使用String类型存储JSON数据，也可以根据需求替换为Map或自定义对象

    /**
     * youchuanMj结果
     */
    var youchuanMjUrls : List<String>? = null,

    /**
     * chaojiTryOn结果
     */
    var chaojiTryOnUrls : List<String>? = null,

    /**
     * chaojiTryOnKontext结果
     */
    var chaojiTryOnKontextUrls : List<String>? = null,

    /**
     * huiwaTryOn结果
     */
    var huiwaTryOnUrls : List<String>? = null,

    /**
     * huiwaTryOnKontext结果
     */
    var huiwaTryOnKontextUrls : List<String>? = null,

    /**
     * lazadaTryOn结果
     */
    var lazadaTryOnUrls : List<String>? = null,

    /**
     * lazadaTryOnKontext结果
     */
    var lazadaTryOnKontextUrls : List<String>? = null,
    /**
     * 业务主键ID
     */
    var busId: Long? = null,
) : BaseTaskVo() {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -3075111488361166192L
    }
}