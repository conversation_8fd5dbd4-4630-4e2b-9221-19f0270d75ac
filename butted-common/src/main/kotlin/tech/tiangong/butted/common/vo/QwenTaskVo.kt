package tech.tiangong.butted.common.vo

import tech.tiangong.butted.common.vo.base.BaseTaskVo
import java.io.Serial
import java.math.BigDecimal

/**
 * 通义千问VO
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:56
 * @version    :1.0
 */
class QwenTaskVo(
    /**
     * 参考图url
     */
    var refImgUrl: String? = null,

    /**
     * 正向提示词
     */
    var prompt: String? = null,

    /**
     * 控制模型生成的最大token数
     */
    var maxNewToken: Int? = null,

    /**
     * 控制图像分辨率
     */
    var maxInputTiles: Int? = null,

    /**
     * 值越小返回结果越确定
     */
    var temperature: BigDecimal? = null,

    /**
     * 值越低结果越确定,值越大结果越多样化
     */
    var topP: BigDecimal? = null,

    /**
     * 重复token施加惩罚,默认1.1,取值范围1.0~1.5
     */
    var repetitionPenalty: BigDecimal? = null,

) : BaseTaskVo() {
    /**
     * 文本回答
     */
    var responseTexts: List<String> = mutableListOf()
    companion object {
        @Serial
        private const val serialVersionUID: Long = -4477329767579653564L
    }

}