package tech.tiangong.butted.common.vo

import java.io.Serializable

/**
 * 扩图模型任务
 *
 * <AUTHOR>
 * @date       ：2024/11/26 10:20
 * @version    :1.0
 */
class FluxOutpaintTaskCreateVo(
    /**
     * 业务主键ID
     */
    val busId: Long,
    /**
     * 任务ID
     */
    val taskId: Long,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -6385978492124928975L
    }

}