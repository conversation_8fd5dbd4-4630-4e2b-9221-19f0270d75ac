package tech.tiangong.butted.common.vo

/**
 * 矩形坐标Vo
 */
data class RectangleCoordinateVo(
    /**
     * X坐标起点
     */
    var xmin: Int? = null,
    /**
     * Y坐标起点
     */
    var ymin: Int? = null,
    /**
     * X坐标终点
     */
    var xmax: Int? = null,
    /**
     * Y坐标终点
     */
    var ymax: Int? = null,

    ) {
    companion object {
        private const val serialVersionUID = 1L
    }
}
