package tech.tiangong.butted.common.enums

/**
 * 业务来源：
 */
enum class BizSourceEnum(
    val code: String,
    val desc: String
) {
    /**
     * 版型耗材
     */
    PATTERN_MATERIAL("pattern_material", "版型耗材"),
    /**
     * AI素材
     */
    DESIGN_MATERIAL("design_material", "AI素材"),
    /**
     * 服装上身
     */
    DRESS_UP("dress_up", "服装上身"),
    /**
     * 虚拟换装
     */
    TRY_ON("try_on", "虚拟换装"),
    /**
     * 虚拟换装输出
     */
    TRY_ON_OUTPUT("try_on_output", "虚拟换装输出"),

    /**
     * 场景图
     */
    SCENE_PICTURE("scene_picture", "场景图"),

    /**
     * 模特素材
     */
    MODEL_MATERIAL("model_material", "模特素材"),

    /**
     * 数码印花
     */
    DIGITAL_PRINT("digital_print", "数码印花"),


    ;


    companion object {

    }

}

