plugins {
    alias(commonLibs.plugins.google.ksp)
    alias(commonLibs.plugins.kapt)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
}

dependencies {
    implementation(projects.buttedCommon)
    implementation(projects.buttedOpenai)
//    implementation(libs.com.zjkj.scf.communal.sdk)
    implementation(commonLibs.hutool.core)
    implementation(commonLibs.hutool.crypto)
    implementation(commonLibs.hutool.http)
    implementation(commonLibs.hutool.extra)
    implementation(commonLibs.okhttp)
    implementation(commonLibs.blade.tenant.spring.boot.starter)
    implementation(commonLibs.blade.web.cloud.spring.boot.starter)
    implementation(commonLibs.blade.data.mybatis.plus.spring.boot.starter)
    implementation(commonLibs.mysql.connector)
    implementation(commonLibs.blade.feign.spring.boot.starter)

    //mapstruct
    implementation(commonLibs.mapstruct)
    kapt(commonLibs.mapstruct.processor)

}


