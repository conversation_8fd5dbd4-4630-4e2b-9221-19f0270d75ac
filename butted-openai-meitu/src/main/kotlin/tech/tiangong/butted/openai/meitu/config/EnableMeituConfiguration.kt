package tech.tiangong.butted.openai.meitu.config

import tech.tiangong.butted.openai.meitu.aimodel.properties.MeituProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration


/**
 * 配置文件Enable
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(
    value = [
        MeituProperties::class
    ]
)
class EnableMeituConfiguration {

}