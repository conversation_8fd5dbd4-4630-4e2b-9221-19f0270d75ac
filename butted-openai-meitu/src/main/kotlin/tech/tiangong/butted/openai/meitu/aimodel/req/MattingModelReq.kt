package tech.tiangong.butted.openai.meitu.aimodel.req;

import java.io.Serializable
import jakarta.validation.constraints.NotBlank

/**
 * 抠图入参模型
 *
 * <AUTHOR>
 */
data class MattingModelReq(
    /**
     * 请求超时时间，单位秒
     */
//    @field:NotBlank(message = "请求超时时间不能为空")
    var sync_timeout: Int? = 120,
    /**
     * 任务类型，固定“mtlab”
     */
//    @field:NotBlank(message = "任务类型不能为空")
    var task_type: String? = "mtlab",
    /**
     * 原始图片地址
     */
    @field:NotBlank(message = "原始图片地址不能为空")
    var init_images: List<MattingModelImageReq>? = null,
    /**
     * 任务地址，固定“v1/sod”
     */
//    @field:NotBlank(message = "参考图尺寸不能为空")
    var task: String? = "v1/sod",
    /**
     * 额外参数的json信息
     */
    var params: String? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
