package tech.tiangong.butted.openai.meitu.remote

import cn.hutool.extra.spring.SpringUtil
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpHeaders
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.enums.LogTypeEnum
import tech.tiangong.butted.openai.meitu.aimodel.properties.MeituProperties
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelReq
import tech.tiangong.butted.openai.meitu.aimodel.resp.MattingModelResponse
import tech.tiangong.butted.openai.meitu.constant.MeituConstant
import tech.tiangong.butted.rest.RestApi
import tech.tiangong.butted.rest.RestApiLogFlag


@Slf4j
object RestMeituApi {
    private val meituProperties: MeituProperties by lazy {
        SpringUtil.getBean(
            MeituProperties::class.java
        )
    }

    /**
     * 创建抠图任务
     */
    fun createMattingTask(
        taskId: Long,
        request: MattingModelReq
    ): MattingModelResponse {
        try {
            RestApiLogFlag.flagTaskId(taskId)
            RestApiLogFlag.flagTaskMode("MEITU_MATTING")
            RestApiLogFlag.flagLogType(LogTypeEnum.CREATE_AI_TASK)
            val headers = HttpHeaders()
            headers.set("Content-Type", "application/json");
            val url =
                "${meituProperties.url}${MeituConstant.PATH_MATTING}?api_key=${meituProperties.appKey}&api_secret=${meituProperties.appSecret}"
            val responseType = object :
                ParameterizedTypeReference<MattingModelResponse>() {}
            val result = RestApi.postJson(
                url = url,
                headers = headers,
                requestBody = request,
                responseType = responseType
            )
            return result
        } finally {
            RestApiLogFlag.clean()
        }

    }


}

