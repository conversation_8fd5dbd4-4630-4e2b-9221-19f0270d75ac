package tech.tiangong.butted.openai.meitu.enums

/**
 * 美图抠图接口返回编码枚举
 * 异常信息参考：https://ai.meitu.com/doc/?id=216&type=api&lang=zh
 */
enum class MattingResponseCodeEnum(
    val code: Int,
    val desc: String,
) {
    UNKNOWN(-1, "未知错误"),
    /**====================================接口异常返回码===========================================*/
    /**
     * 处理错误
     */
    PROCESS_ERROR(20001, "处理错误"),

    /**
     * 人脸缺失
     */
    DETECT_NOT_FACE(20003, "人脸缺失"),

    /**
     * 人脸数大于1
     */
    MORE_THAN_ONE_FACE(20004, "人脸数大于1"),

    /**
     * 未传入人脸点
     */
    MISSING_LANDMARK_ARGUMENTS(20007, "未传入人脸点"),

    /**
     * 照片不符合规范
     */
    UNSUITABLE_IMAGE(20008, "照片不符合规范"),

    /**
     * 不支持的type
     */
    UNSUPPORT_TYPE(20009, "不支持的type"),

    /**
     * 检测不到第二张图片的人脸
     */
    DETECT_NOT_FACE_2(20010, "检测不到第二张图片的人脸"),
    /**
     * 垂直高度不满足
     */
    UNSUITABLE_VERTICAL_IMAGE(20011, "垂直高度不满足"),
    /**
     * 水平宽度不满足
     */
    UNSUITABLE_HORIZONTAL_IMAGE(20012, "水平宽度不满足"),
    /**
     * 分辨率过大
     */
    RESOLUTION_TOO_LARGE_ERROR(20013, "分辨率过大"),
    /**
     * 查找不到图片
     */
    NOT_FOUND(20014, "查找不到图片"),
    /**
     * 图片超限
     */
    PICTURE_OVERRUN_ERROR(20015, "图片超限"),
    /**
     * 五官缺失
     */
    DETECT_FACE_OUTOFIMAGE(20020, "五官缺失"),
    /**
     * 非正脸 点头 俯仰角过大
     */
    DETECT_FACE_PITCHANGLE_BIG(20021, "非正脸 点头 俯仰角过大"),
    /**
     * 非正脸 摇头 旋转角过大
     */
    DETECT_FACE_YAWANGLE_BIG(20022, "非正脸 摇头 旋转角过大"),
    /**
     * 脸部占比小像素低
     */
    DETECT_FACE_LOWAREA(20023, "脸部占比小像素低"),

    /**
     * 检测到人数不足
     */
    MASK_NOT_ENOUGH(20024,"检测到人数不足"),

    /**
     * 初始化失败
     */
    INIT_FAIL(20025,"初始化失败"),

    /**
     * 加载模型失败
     */
    LOAD_MODEL_ERROR(21001, "加载模型失败"),
    /**
     * 头发mask缺失
     */
    HAIR_MASK_LOSS(21002, "加载模型失败"),
    /**
     * 人脸个数错误
     */
    FACE_NUM_ERROR(21003, "人脸个数错误"),
    /**
     * ar中plist解析错误
     */
    AR_PARSE_FAULT(21004, "ar中plist解析错误"),
    /**
     * ar人脸错误
     */
    AR_EEEOR_COUNT(21005, "ar人脸错误"),
    /**
     * ar超过人脸范围
     */
    AR_FACE_OUT(21006, "ar超过人脸范围"),
    /**
     * json内容错误
     */
    JSON_ERROR(21007, "json内容错误"),
    /**
     * 背景图片缺失
     */
    BACKGROUND_IMAGE_LOSS(21008, "背景图片缺失"),
    /**
     * bodymask缺失
     */
    BODY_MASK_LOSS(21009, "bodymask缺失"),
    /**
     * 人脸角度错误
     */
    FACE_ANGLE_ERROR(21010, "人脸角度错误"),


    /**====================================HTTP异常返回码===========================================*/
    HTTP_200(200,"成功的请求"),

    /**
     * 错误的请求，通常为照片不符合规范等
     */
    HTTP_400(400,"错误的请求"),

    /**
     * 鉴权失败，出现此错误时，请检查您的AKSK是否正确
     */
    HTTP_401(401,"鉴权失败"),

    /**
     * 	鉴权失败，出现此错误时，请检查您的应用是否过期
     */
    HTTP_403(403,"鉴权失败"),

    /**
     * 	请求的接口地址错误，请检查请求的接口地址是否正确
      */
    HTTP_404(404,"请求的接口错误"),

    /**
     * 	下载图片失败，请确认您使用的图片URL地址是否能正常下载
     */
    HTTP_424(424,"下载图片失败"),

    /**
     * 	请求实体过长，请检查您的图片是否超过接口限制
     */
    HTTP_433(433,"请求实体过长"),

    /**
     * 	算法内部异常，请稍后重试
     */
    HTTP_415(415,"算法内部异常"),

    /**
     * 	请求内部异常，请稍后重试
     */
    HTTP_500(500,"请求异常"),

    /**
     * 	请求无响应，请稍后重试
     */
    HTTP_502(502,"无响应"),

    /**
     * 	请求并发超过限制
     */
    HTTP_503(503,"并发过高"),

    /**
     * 	请求内部异常，请稍后重试
     */
    HTTP_504(504,"请求内部异常，请稍后重试"),

    /**
     * 	请求超时，请稍后重试
     */
    HTTP_599(599,"请求超时");
    companion object {

        fun of(code: Int): MattingResponseCodeEnum =
            entries.firstOrNull { it.code == code } ?: UNKNOWN

        fun toDesc(code: Int): String = of(code).desc

    }
}