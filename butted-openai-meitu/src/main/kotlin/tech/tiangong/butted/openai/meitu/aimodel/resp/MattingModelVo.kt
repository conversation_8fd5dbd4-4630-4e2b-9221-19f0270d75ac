package tech.tiangong.butted.openai.meitu.aimodel.resp

import java.io.Serializable

/**
 * 美图智能抠图模型Vo
 * {
 *         "code": 0,
 *         "data": {
 *                 "status": 10,
 *                 "result": {
 *                         "id": "1cc79381-d367-4630-a7bc-0633fbc72637",
 *                         "urls": ["https://obs.mtlab.meitu.com/mtopen/7071f5094b73406da8db0136fc1f7831/MTY4OTgzNjQwMA==/50a84e28-d3e9-453b-b486-da758c5b88cd.jpg"],
 *                         "parameters": {
 *                                 "Kind": 0,
 *                                 "bottom_x": "None",
 *                                 "bottom_y": "None",
 *                                 "exist_salient": true,
 *                                 "process_time": 120.13816833496094,
 *                                 "pull_time": 88.46640586853027,
 *                                 "rsp_media_type": "url",
 *                                 "top_x": "None",
 *                                 "top_y": "None",
 *                                 "use_fe": true,
 *                                 "version": "2.4.0"
 *                         }
 *                 },
 *                 "progress": 1,
 *                 "predict_elapsed": 10000
 *         }
 * }
 *
 */
class MattingModelVo(


    /**
     * 状态码:
     * 9=超时，2=失败，10=成功
     */
    val status: Int,
    /**
     * 任务处理结果内容
     */
    val result: MattingModelResultVo? = null,
    /**
     * 任务进度
     */
    var progress: Int? = null,
    /**
     */
    var predict_elapsed: Int? = null,

    ) : Serializable {

    companion object {
        private const val serialVersionUID = 1L
    }
}