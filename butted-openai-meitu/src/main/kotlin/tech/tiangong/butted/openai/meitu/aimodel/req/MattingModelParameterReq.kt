package tech.tiangong.butted.openai.meitu.aimodel.req

import java.io.Serializable

data class MattingModelParameterReq(
    /**
     * “url”：抠图结果以短期有效的url返回
     */
    var rsp_media_type: String? = null,
    /**
     * 是否返回mask图，True只返回mask图，False返回结果图
     */
    var nMask: Boolean? = null,
    /**
     * 选择要使用的抠图模型，传0：表示使用人像抠图；传1：表示使用商品抠图；传2：表示使用图形抠图。若不传，模型内部会自动判断选择使用哪个模型
     */
    var model_type: Int? = null,
    /**
     * 图标类型可以添加用户交互框参数，坐标应当使用相对坐标，示例：[[[0.01, 0.814], [0.12, 0.814], [0.12, 0.96], [0.01, 0.96]]
     */
    var userboxes: String? = null,
    /**
     * 是否返回黑白图，True只返回黑白mask图，False返回四通道Mask图，默认为False
     */
    var blackwhite: Boolean? = false,
    /**
     * 是否返回目标位置，True返回目标位置top_x,top_y,bottom_x,bottom_y,默认为False
     */
    var nbox: Boolean? = false,
    ) : Serializable {
        companion object {
            private const val serialVersionUID = 1L
        }
    }