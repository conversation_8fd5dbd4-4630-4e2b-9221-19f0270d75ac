package tech.tiangong.butted.openai.meitu.convert

import com.alibaba.fastjson2.JSONObject
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.req.MeituMattingTaskInnerReq
import tech.tiangong.butted.common.vo.CreateMeituMattingTaskVo
import tech.tiangong.butted.common.vo.MeituMattingResultParametersInnerVo
import tech.tiangong.butted.common.vo.MeituMattingTaskInnerVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.MeituMattingTask
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelImageReq
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelParameterReq
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelParamsReq
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelReq
import tech.tiangong.butted.openai.meitu.aimodel.resp.MattingModelResponse
import tech.tiangong.butted.openai.vo.req.MeituMattingTaskReq
import tech.tiangong.butted.openai.vo.resp.MeituMattingTaskVo

@Slf4j
object MeituMattingConvert : BaseConvert {

    fun buildCreateMeituMattingTaskInnerVo(task: MeituMattingTask): CreateMeituMattingTaskVo {
        return CreateMeituMattingTaskVo().apply {
            this.taskId = task.taskId
            this.resImg = task.resImgs
            task.resp?.let {
                var resp = JSONObject.parseObject(task.resp, MattingModelResponse::class.java);
                var resultParameters = resp.data?.result?.parameters
                this.Kind = resultParameters?.Kind ?: null
                this.bottom_x = resultParameters?.bottom_x ?: null
                this.bottom_y = resultParameters?.bottom_y ?: null
                this.exist_salient = resultParameters?.exist_salient ?: null
                this.rsp_media_type = resultParameters?.rsp_media_type ?: null
                this.top_x = resultParameters?.top_x ?: null
                this.top_y = resultParameters?.top_y ?: null
            }
        }
    }

    fun buildMeituMattingTask(taskReq: MeituMattingTaskReq): MeituMattingTask {
        return MeituMattingTask().apply {
            initBaseTask(this)
            this.refImgUrl = taskReq.refImgUrl
            var mattingModelParamsReq = MattingModelParamsReq()
            var mattingModelParameterReq = MattingModelParameterReq()
            mattingModelParameterReq.model_type = taskReq.modelType
            mattingModelParameterReq.nMask = taskReq.nMask
            mattingModelParameterReq.blackwhite = taskReq.blackwhite
            mattingModelParameterReq.userboxes = taskReq.userboxes
            mattingModelParameterReq.nbox = taskReq.nbox
            mattingModelParamsReq.parameter = mattingModelParameterReq
            var params = JSONObject.toJSONString(mattingModelParamsReq)

            this.req = JSONObject.toJSONString(MattingModelReq().apply {
                this.init_images = mutableListOf(taskReq.refImgUrl).stream().map { imageUrl ->
                    MattingModelImageReq().apply { this.url = imageUrl }
                }.toList()
                this.params = params
            })
        }
    }

    fun buildMeituMattingTaskReq(req: MeituMattingTaskInnerReq): MeituMattingTaskReq {
        return MeituMattingTaskReq().apply {
            this.refImgUrl = req.refImgUrl
            this.modelType = req.modelType
            this.nMask = req.nMask
            this.blackwhite = req.blackwhite
            this.userboxes = req.userboxes
            this.nbox = req.nbox
        }
    }

    fun buildMeituMattingTaskInnerVo(req: MeituMattingTaskVo?): MeituMattingTaskInnerVo {
        if (req == null) {
            return MeituMattingTaskInnerVo()
        }
        return MeituMattingTaskInnerVo().apply {
            this.refImgUrl = req.refImgUrl
            this.taskId = req.taskId
            this.resImg = req.resImg
            this.taskStatus = req.taskStatus
            this.aiEndTime = req.aiEndTime
            this.createdTime = req.createdTime
            this.message = req.message
            this.pushTime = req.pushTime
            this.taskProgress = req.taskProgress
            this.aiStartTime = req.aiStartTime
            this.resultParameters = req.resultParameters?.copy(MeituMattingResultParametersInnerVo::class)
        }
    }
}



