package tech.tiangong.butted.openai.meitu.aimodel.resp

import java.io.Serializable

/**
 * 美图奇想大模型-智能抠图 返回报文
 * // 请求成功示例
 * {
 *         "code": 0,
 *         "data": {
 *                 "status": 10,
 *                 "result": {
 *                         "id": "1cc79381-d367-4630-a7bc-0633fbc72637",
 *                         "urls": ["https://obs.mtlab.meitu.com/mtopen/7071f5094b73406da8db0136fc1f7831/MTY4OTgzNjQwMA==/50a84e28-d3e9-453b-b486-da758c5b88cd.jpg"],
 *                         "parameters": {
 *                                 "Kind": 0,
 *                                 "bottom_x": "None",
 *                                 "bottom_y": "None",
 *                                 "exist_salient": true,
 *                                 "process_time": 120.13816833496094,
 *                                 "pull_time": 88.46640586853027,
 *                                 "rsp_media_type": "url",
 *                                 "top_x": "None",
 *                                 "top_y": "None",
 *                                 "use_fe": true,
 *                                 "version": "2.4.0"
 *                         }
 *                 },
 *                 "progress": 1,
 *                 "predict_elapsed": 10000
 *         }
 * }
 * }
 */
data class MattingModelResponse(
    /**
     * code=0时表示请求成功，code>0表示请求失败
     */
    val code: Int,
    /**
     * 错误信息
     */
    val message: String? = null,
    /**
     * 返回任务处理详情
     */
    val data: MattingModelVo? = null,
) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): Boolean = code == 0

    /**
     * 失败的响应
     */
    fun failed(): Boolean = code != 0

    companion object {
        private const val serialVersionUID: Long = 1L
    }

}