package tech.tiangong.butted.openai.meitu.service.impl

import cn.hutool.crypto.digest.DigestUtil
import com.alibaba.fastjson2.JSONObject
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.CreateMeituMattingTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.openai.meitu.aimodel.req.MattingModelReq
import tech.tiangong.butted.openai.meitu.aimodel.resp.MattingModelResponse
import tech.tiangong.butted.openai.meitu.convert.MeituMattingConvert
import tech.tiangong.butted.openai.meitu.enums.MattingResponseCodeEnum
import tech.tiangong.butted.openai.meitu.remote.RestMeituApi
import tech.tiangong.butted.openai.repository.MeituMattingTaskRepository
import tech.tiangong.butted.openai.service.MeituTaskService
import tech.tiangong.butted.openai.vo.req.MeituMattingTaskReq
import tech.tiangong.butted.openai.vo.resp.MeituMattingResultParametersVo
import tech.tiangong.butted.openai.vo.resp.MeituMattingTaskVo
import tech.tiangong.butted.oss.AliYunOss
import tech.tiangong.butted.rest.RestApi

/**
 * @description:
 * @since: 2024年09月13日20:20:23
 * @version: 1.0
 **/
@Slf4j
@Service
class MeituTaskServiceImpl(
    val meituMattingTaskRepository: MeituMattingTaskRepository,
) : MeituTaskService {

    @Transactional(rollbackFor = [Exception::class])
    override fun createMattingTask(req: MeituMattingTaskReq): CreateMeituMattingTaskVo {
        val refImgByte: ByteArray = RestApi.noLogResBody {
            RestApi.get(req.refImgUrl!!, ByteArray::class.java)
        }
        val refImgSha256 = DigestUtil.sha256Hex(refImgByte)!!
        val mattingTask = MeituMattingConvert.buildMeituMattingTask(req)
        mattingTask.let {
            it.startAiTime()

            val response = RestMeituApi.createMattingTask(
                mattingTask.taskId!!,
                JSONObject.parseObject(it.req, MattingModelReq::class.java)
            )
            if (response.failed() || response.data == null) {
                //异常信息参考：https://ai.meitu.com/doc/?id=216&type=api&lang=zh
                val mattingResponseCode = MattingResponseCodeEnum.of(response.code);
                log.error { "meituMatting task create failed: ${it.toJson()},code:${response.code} msg: ${response.message}:${mattingResponseCode.desc}" }
                throw ButtedException("meituMatting task create failed: ${response.message}:${mattingResponseCode.desc}")
            }
            val data = response.data
            if (data.status != 10) {
                log.error { "meituMatting task create failed: ${it.toJson()}, status:${data.status}" }
                throw ButtedException("meituMatting task create failed, status:${data.status}")
            }
            it.resp = JSONObject.toJSONString(response)
            it.thirdTaskId = data.result?.id
            it.taskStatus = TaskStatusEnum.COMPLETED.code
            it.taskProgress = 100
            it.stopAiEndTime()
            val resultUrl = data.result?.urls?.firstOrNull()
                ?: throw ButtedException("MeituMatting task create failed, resultUrl is null");
            it.resImgs = AliYunOss.upload(resultUrl)
            it.refImgSha = refImgSha256
            meituMattingTaskRepository.save(it)
        }
        return MeituMattingConvert.buildCreateMeituMattingTaskInnerVo(mattingTask)
    }

    override fun detailMattingTask(taskId: Long): MeituMattingTaskVo {
        val task = meituMattingTaskRepository.obtainById(taskId, "${taskId}任务不存在")
        val result = task.copy(MeituMattingTaskVo::class).apply {
            resImg = task.resImgs?.splitAndFirst()
        }
        if (task.resp != null) {
            JSONObject.parseObject(task.resp, MattingModelResponse::class.java).let {
                it.data?.result?.parameters?.let { p ->
                    result.resultParameters = p.copy(MeituMattingResultParametersVo::class)
                }
            }
        }
        return result
    }


}


