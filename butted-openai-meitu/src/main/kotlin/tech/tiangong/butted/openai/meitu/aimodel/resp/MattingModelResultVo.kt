package tech.tiangong.butted.openai.meitu.aimodel.resp

import java.io.Serializable

/**
 * 美图智能抠图模型Vo
 * {
 *         "code": 0,
 *         "data": {
 *                 "status": 10,
 *                 "result": {
 *                         "id": "1cc79381-d367-4630-a7bc-0633fbc72637",
 *                         "urls": ["https://obs.mtlab.meitu.com/mtopen/7071f5094b73406da8db0136fc1f7831/MTY4OTgzNjQwMA==/50a84e28-d3e9-453b-b486-da758c5b88cd.jpg"],
 *                         "parameters": {
 *                                 "Kind": 0,
 *                                 "bottom_x": "None",
 *                                 "bottom_y": "None",
 *                                 "exist_salient": true,
 *                                 "process_time": 120.13816833496094,
 *                                 "pull_time": 88.46640586853027,
 *                                 "rsp_media_type": "url",
 *                                 "top_x": "None",
 *                                 "top_y": "None",
 *                                 "use_fe": true,
 *                                 "version": "2.4.0"
 *                         }
 *                 },
 *                 "progress": 1,
 *                 "predict_elapsed": 10000
 *         }
 * }
 *
 */
class MattingModelResultVo(

    /**
     * 任务ID
     */
    var id: String? = null,
    /**
     * code=0时表示请求成功，code>0表示请求失败
     */
    val code: Int? = null,

    /**
     * 处理结果图片集
     */
    val urls: MutableList<String>? = null,
    /**
     * 额外返回参数
     */
    val parameters: MattingModelResultParametersVo? = null,


    ) : Serializable {

    companion object {
        private const val serialVersionUID = 1L
    }
}