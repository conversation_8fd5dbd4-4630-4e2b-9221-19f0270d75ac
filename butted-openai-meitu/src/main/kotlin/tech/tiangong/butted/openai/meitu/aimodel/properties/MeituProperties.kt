package tech.tiangong.butted.openai.meitu.aimodel.properties

import org.springframework.boot.context.properties.ConfigurationProperties


/**
 * 美图奇想配置
 */
@ConfigurationProperties(prefix = "task.meitu")
data class MeituProperties(
    /**
     * 地址
     */
    val url: String = "https://openapi.meitu.com",
    /**
     * appCode
     */
    val appCode: String = "356182",

    /**
     * AppKey
     */
    val appKey: String = "9e879f79f33b4addbe9beed646cc0270",
    /**
     * AppSecret
     */
    val appSecret: String = "c04bcf95c09b444c969a52cff6cbe751",


)