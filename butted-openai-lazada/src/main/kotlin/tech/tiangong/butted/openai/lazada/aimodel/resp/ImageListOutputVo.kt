package tech.tiangong.butted.openai.lazada.aimodel.resp

import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazTaskOutputVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelOutputVo

/**
 * 图片列表输出VO
 */
data class ImageListOutputVo(
    /**
     * 图片列表
     */
    var data: List<String>? = null
) : LazTaskOutputVo(), TaskModelOutputVo {

    override fun toString(): String {
        return "ImageListOutputVo(data=$data, supper=${super.toString()})"
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}


//fun main(args: Array<String>) {
//    val json = "[\"https://example.com/image1.jpg\",\"https://example.com/image2.jpg\"]"
//
//    val parseJson = json.parseJson(ImageListOutputVo::class.java)
//    println(parseJson)
//    parseJson.forEach {
//        println(it)
//    }
//
//}