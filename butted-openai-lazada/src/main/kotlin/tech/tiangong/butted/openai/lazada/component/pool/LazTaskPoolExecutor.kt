package tech.tiangong.butted.openai.lazada.component.pool


import tech.tiangong.butted.core.pool.ButtedThreadPoolExecutor
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum

/**
 * LazTaskPoolExecutor
 */
object LazTaskPoolExecutor {
    private val taskExecutor = ButtedThreadPoolExecutor.build4("Laz-AiTask")
    fun execute(modeEnum: LazTaskModeEnum, taskAction: TaskActionEnum, taskId: Long, command: Runnable) {
        val tag = "${modeEnum.name}-${taskAction.name}-${taskId}"
        taskExecutor.execute(tag, command)
    }

}
