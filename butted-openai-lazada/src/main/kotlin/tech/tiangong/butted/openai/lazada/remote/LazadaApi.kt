package tech.tiangong.butted.openai.lazada.remote

import cn.hutool.extra.spring.SpringUtil
import com.fasterxml.jackson.core.type.TypeReference
import com.lazada.lazop.api.LazopClient
import com.lazada.lazop.api.LazopRequest
import com.lazada.lazop.util.LazopHashMap
import org.springframework.http.HttpMethod
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.butted.core.reflect.ParameterizedTypeImpl
import tech.tiangong.butted.core.toolkit.assertNotBlank
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.toJson
import tech.tiangong.butted.openai.lazada.aimodel.req.ProductImageMatchReq
import tech.tiangong.butted.openai.lazada.aimodel.resp.CancelTaskOutputVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.CreateTaskOutputVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.MatchImageOutputVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazResultVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazResultWapVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelResponse
import java.lang.reflect.Type
import kotlin.reflect.full.declaredMemberProperties


@Slf4j
object LazadaApi {
    private val lazopClient: LazopClient by lazy {
        SpringUtil.getBean(
            LazopClient::class.java
        )
    }


    /**
     * 商品图匹配（同步接口）
     * @param req ProductImageMatchReq
     *
     *
     * 返回参数
     * {
     *   "result" : {
     *     "success" : true,
     *     "resultCode" : "",
     *     "resultMessage" : "",
     *     "class" : "com.lazada.lazlike.openapi.response.ProductImageMatchResult",
     *     "matchImageUrls" : [ "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png" ]
     *   },
     *   "code" : "0",
     *   "request_id" : "214118c717443389626888101"
     * }
     */
    fun findProductImageList(
        req: ProductImageMatchReq,
    ): TaskModelResponse<MatchImageOutputVo> {
        val params = req.toLazopHashMap()
        val apiName = "/content/ai/productImageMatch"
        return post(apiName, params, MatchImageOutputVo::class.java)
    }

    /**
     * 创建任务
     * @param req Any
     * @return 返回任务ID
     */
    fun createTask(
        apiName: String,
        req: Any,
    ): TaskModelResponse<CreateTaskOutputVo> {
        val params = req.toLazopHashMap()
        return post(apiName, params, CreateTaskOutputVo::class.java)
    }

    /**
     * 查询任务结果
     * @param thirdTaskId 任务ID
     * @return 返回任务信息
     */
    fun <T : LazResultVo> query(
        thirdTaskId: String,
        tCls: Class<T>
    ): TaskModelResponse<T> {
        val request = LazopRequest()
        request.addApiParameter("task_id", thirdTaskId)
        request.apiName = "/content/ai/getTaskStatus"
//        val dataType: TypeReference<LazTaskModelDataVo<T>> = object :
//            TypeReference<LazTaskModelDataVo<T>>() {
//            override fun getType(): Type {
//                return ParameterizedTypeImpl(
//                    LazTaskModelDataVo::class,
//                    dataTypeReference.type::class
//                )
//            }
//        }
        return get(request, tCls)
    }


    /**
     * 取消任务
     * @param taskIds 任务ID列表
     * @return 返回任务信息
     *
     * LazopClient client = new LazopClient(url, appkey, appSecret);
     * LazopRequest request = new LazopRequest();
     * request.setApiName("/content/ai/cancelTask");
     * request.addApiParameter("task_ids", "1234,5678");
     * LazopResponse response = client.execute(request);
     */
    fun cancel(
        taskIds: List<String>,
    ): TaskModelResponse<CancelTaskOutputVo> {
        if (taskIds.isEmpty()) {
            return TaskModelResponse.success(CancelTaskOutputVo(canceledTaskCount = 0))
        }
        val params = LazopHashMap()
        params["task_ids"] = taskIds.joinToStr()
        val apiName = "/content/ai/cancelTask"
        return post(apiName, params, CancelTaskOutputVo::class.java)
    }

    /**
     * post
     */
    fun <T : LazResultVo> post(
        apiPath: String,
        params: LazopHashMap?,
        rCls: Class<T>
    ): TaskModelResponse<T> {
        val typeReference: TypeReference<T> = object :
            TypeReference<T>() {
            override fun getType(): Type {
                return rCls
            }
        }
        return post(apiPath, params, typeReference)
    }

    /**
     * post
     */
    fun <T : LazResultVo> post(
        apiPath: String,
        params: LazopHashMap?,
        typeReference: TypeReference<T>
    ): TaskModelResponse<T> {
        val request = LazopRequest()
        request.apiName = apiPath
        params?.forEach {
            request.addApiParameter(it.key, it.value)
        }
        return post(request, typeReference)
    }

    /**
     * post
     */
    fun <T : LazResultVo> post(
        request: LazopRequest,
        typeReference: TypeReference<T>
    ): TaskModelResponse<T> {
        return call(HttpMethod.POST, request, typeReference)
    }


    /**
     * post
     */
    fun <T : LazResultVo> get(
        request: LazopRequest,
        tCls: Class<T>
    ): TaskModelResponse<T> {
        return call(HttpMethod.GET, request, tCls)
    }


    /**
     * post
     */
    fun <T : LazResultVo> call(
        method: HttpMethod,
        request: LazopRequest,
        tCls: Class<T>
    ): TaskModelResponse<T> {
        return call(method, request, buildTypeReference(tCls))
    }

    /**
     * post
     */
    fun <T : LazResultVo> call(
        method: HttpMethod,
        request: LazopRequest,
        typeReference: TypeReference<T>
    ): TaskModelResponse<T> {
        request.httpMethod = method.name()
        request.apiName.assertNotBlank("请求路径不能为空")
        val lazopResp = lazopClient.execute(request)
        val response = TaskModelResponse.build<T>(
            lazopResp.requestId,
            lazopResp.type,
            lazopResp.code,
            lazopResp.message
        )
        val lazResultWapType: TypeReference<LazResultWapVo<T>> = object :
            TypeReference<LazResultWapVo<T>>() {
            override fun getType(): Type {
                return ParameterizedTypeImpl(
                    LazResultWapVo::class,
                    typeReference.type
                )
            }
        }
        log.info { "lazopResp: ${lazopResp.toJson()}" }
        if (response.succeed() && lazopResp.body.isNotBlank()) {
            val lazResultWapVo = lazopResp.body!!.parseJson(lazResultWapType)
            response.data = lazResultWapVo.result
        }
        return response
    }


    fun Any.toLazopHashMap(vararg ignoreProperties: String? = arrayOf()): LazopHashMap {
        val params = LazopHashMap()
        // 获取类的KClass实例
        val kClass = this::class
        // 遍历类的所有属性
        kClass.declaredMemberProperties.forEach { property ->
            // 打印属性名称和值
            //println("Property Name: ${property.name}, Property Value: ${property.getter.call(this)}")
            if (!ignoreProperties.contains(property.name)) {
                val value = property.getter.call(this)
                if (value != null) {
                    params[property.name] = value.toString()
                }
            }
        }
        return params
    }


    fun <T : LazResultVo> buildTypeReference(
        tCls: Class<T>
    ): TypeReference<T> {
        val lazResultType: TypeReference<T> = object :
            TypeReference<T>() {
            override fun getType(): Type {
                return tCls
            }
        }
        return lazResultType
    }

    fun defaultTypeReference(): TypeReference<LazResultVo> {
        val lazResultWapType: TypeReference<LazResultVo> = object :
            TypeReference<LazResultVo>() {
            override fun getType(): Type {
                return LazResultVo::class.java
            }
        }
        return lazResultWapType
    }

}

//fun main(args: Array<String>) {
//    val json =
//        "{\"result\":{\"success\":true,\"resultCode\":\"\",\"resultMessage\":\"\",\"class\":\"com.lazada.lazlike.openapi.response.ProductImageMatchResult\",\"matchImageUrls\":[\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png\",\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png\",\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png\",\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png\",\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png\"]},\"code\":\"0\",\"request_id\":\"214107b017443518398146051\"}"
//
//    val rType: TypeReference<MatchImageOutputVo> = object :
//        TypeReference<MatchImageOutputVo>() {
//        override fun getType(): Type {
//            return MatchImageOutputVo::class.java
//        }
//    }
//
//    val lazResultWapType: TypeReference<LazResultWapVo<MatchImageOutputVo>> = object :
//        TypeReference<LazResultWapVo<MatchImageOutputVo>>() {
//        override fun getType(): Type {
//            return ParameterizedTypeImpl(
//                LazResultWapVo::class,
//                rType.type
//            )
//        }
//    }
//    val lazResultWapVo = json.parseJson(lazResultWapType)
//    println(lazResultWapVo)
//}
