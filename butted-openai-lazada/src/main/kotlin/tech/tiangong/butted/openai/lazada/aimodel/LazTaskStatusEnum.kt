package tech.tiangong.butted.openai.lazada.aimodel

/**
 * 任务状态：
 * waiting： 等待中
 * running: 运⾏中
 * cancelled：取消中
 * fail: 失败
 * success: 成功
 */
enum class LazTaskStatusEnum(
    val code: String,
    val desc: String,
) {
    /**
     * 等待中
     */
    WAITING("waiting", "排队中"),

    /**
     * 运⾏中
     */
    RUNNING("running", "运⾏中"),

    /**
     * 取消
     */
    CANCELLED("cancelled", "取消"),

    /**
     * 失败
     */
    FAIL("fail", "失败"),

    /**
     * 成功
     */
    SUCCESS("success", "成功"),


    ;


}
