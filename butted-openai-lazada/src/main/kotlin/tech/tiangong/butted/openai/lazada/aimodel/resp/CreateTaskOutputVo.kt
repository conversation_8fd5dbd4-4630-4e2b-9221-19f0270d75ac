package tech.tiangong.butted.openai.lazada.aimodel.resp

import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazResultVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelOutputVo

/**
 * 创建任务输出VO
 */
class CreateTaskOutputVo(
    /**
     * Lazada任务ID
     */
    var taskId: String? = null
) : LazResultVo(), TaskModelOutputVo {

    companion object {
        private const val serialVersionUID = 1L
    }
}

