package tech.tiangong.butted.openai.lazada.service.impl

import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.exception.RemoteException
import tech.tiangong.butted.openai.lazada.aimodel.req.ProductImageMatchReq
import tech.tiangong.butted.openai.lazada.remote.LazadaApi
import tech.tiangong.butted.openai.lazada.service.ClothingPoseMatchService
import tech.tiangong.butted.openai.vo.req.ClothingPoseMatchReq
import tech.tiangong.butted.openai.vo.resp.ClothingPoseMatchVo

/**
 * @description:
 * @author: chazz
 * @since: 2025年04月11日10:01:03
 * @version: 1.0
 **/
@Slf4j
@Service
class ClothingPoseMatchServiceImpl : ClothingPoseMatchService {
    override fun list(req: ClothingPoseMatchReq): List<ClothingPoseMatchVo> {
        val response = LazadaApi.findProductImageList(
            ProductImageMatchReq(
                image_url = req.clothingImg,
                match_num = req.matchNum ?: 10
            )
        )
        if (response.failed()) {
            throw RemoteException(response.message.blankDefault("Lazada服装姿态匹配失败"))
        }
        return response.data?.matchImageUrls?.map {
            ClothingPoseMatchVo(
                pictureUrl = it,
            )
        } ?: listOf()

    }
}