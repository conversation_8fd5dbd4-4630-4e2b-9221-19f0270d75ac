package tech.tiangong.butted.openai.lazada.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.ShiftFaceTask
import tech.tiangong.butted.openai.lazada.aimodel.req.ShiftFaceModelReq
import tech.tiangong.butted.openai.vo.req.ShiftFaceTaskReq

@Slf4j
object ShiftFaceConvert : BaseConvert {
    fun build(req: ShiftFaceTaskReq): ShiftFaceTask {
        return ShiftFaceTask().apply {
            this.manualInitBaseTask(req)
            this.parentTaskId = req.parentTaskId
            this.sourceId = req.sourceId
            this.sourceType = req.source!!.code
            this.humanImgs = req.humanImgList.joinToStr()
            this.faceImgs = req.faceImgList.joinToStr()
            this.faceEncode = req.faceEncode
            this.algorithms = req.algorithms?.code
            this.taskAttribute = req.taskAttribute
        }
    }

    fun packShiftFaceModelReq(task: ShiftFaceTask): ShiftFaceModelReq {
        return ShiftFaceModelReq(
            raw_image_url = task.humanImgs!!.splitAndFirst(),
            model_face_image_url = task.faceImgs!!.splitAndFirst(),
            model_code = task.faceEncode!!,
            batch_size = 1
        )
    }

}


