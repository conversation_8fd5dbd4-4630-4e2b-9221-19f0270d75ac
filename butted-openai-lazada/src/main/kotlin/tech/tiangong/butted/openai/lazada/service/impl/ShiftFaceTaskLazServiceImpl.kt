package tech.tiangong.butted.openai.lazada.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pojo.CreatedResult
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.ShiftFaceTaskEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.entity.ShiftFaceTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.aimodel.req.ShiftFaceModelReq
import tech.tiangong.butted.openai.lazada.aimodel.resp.ImageListOutputVo
import tech.tiangong.butted.openai.lazada.config.properties.ShiftFaceLazProperties
import tech.tiangong.butted.openai.lazada.convert.ShiftFaceConvert
import tech.tiangong.butted.openai.lazada.service.base.LazTaskBaseSupportImpl
import tech.tiangong.butted.openai.repository.ShiftFaceTaskRepository
import tech.tiangong.butted.openai.service.ShiftFaceTaskService
import tech.tiangong.butted.openai.vo.query.ShiftFaceTaskQuery
import tech.tiangong.butted.openai.vo.req.ShiftFaceTaskReq
import tech.tiangong.butted.oss.upload

/**
 * Lazada换脸任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class ShiftFaceTaskLazServiceImpl(
    config: ShiftFaceLazProperties,
    val shiftFaceTaskRepository: ShiftFaceTaskRepository
) : LazTaskBaseSupportImpl<ShiftFaceTask, ImageListOutputVo>(
    config,
    shiftFaceTaskRepository
), ShiftFaceTaskService {
    init {
        modeEnum = LazTaskModeEnum.LAZ_SHIFT_FACE
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: ShiftFaceTaskReq): CreatedResult {
        val task = ShiftFaceConvert.build(req)
        task.thirdTaskId = createTask(task)
        shiftFaceTaskRepository.saveManualFill(task)
        return CreatedResult.success(task.taskId!!)
    }


    override fun buildLazModelReq(task: ShiftFaceTask): ShiftFaceModelReq {
        return ShiftFaceConvert.packShiftFaceModelReq(task)
    }

    override fun obtainThirdTaskId(task: ShiftFaceTask): String {
        return task.thirdTaskId!!
    }

    override fun getByParentTaskId(parentTaskId: Long): ShiftFaceTask? {
        return shiftFaceTaskRepository.getByParentTaskId(parentTaskId)
    }

    override fun getBySource(sourceId: Long, sourceType: String): ShiftFaceTask? {
        return shiftFaceTaskRepository.getBySource(sourceId, sourceType)
    }

    override fun handlePullResult(
        task: ShiftFaceTask,
        changeStateEnum: TaskStatusEnum,
        output: ImageListOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            try {
                if (output?.data.isEmpty()) {
                    handleException(task, "AI换脸失败，生成结果为空", TaskStatusEnum.FAILED)
                    return
                }
                output!!.let { ol ->
                    task.resOriginImgs = ol.data!!.toJson()
                    //转成阿里云oss存储
                    task.resImgs = ol.data!!.map { it.upload(ContentType.IMAGE_PNG) }.joinToStr()
                }
            } catch (e: Exception) {
                handleException(task, e.message, TaskStatusEnum.FAILED)
                return
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, ShiftFaceTaskEvent(task, changeStateEnum))
    }


    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return ShiftFaceTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.algorithms = algorithm().code
                it.pageNum = curPageNum
                shiftFaceTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    override fun handleException(task: ShiftFaceTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task, ShiftFaceTaskEvent(task, failStatus))
    }

    fun manualUpdateAiTaskInfo(task: ShiftFaceTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            shiftFaceTaskRepository.updateByIdManualFill(task)
            event?.also {
                afterCommitExec {
                    publishEvent(it)
                }
            }
        }
    }

    override fun suspendByParentTaskId(parentTaskId: Long) {
        throw UnsupportedOperationException("不支持终止")
    }

    override fun algorithm(): AlgorithmEnum {
        return AlgorithmEnum.LAZADA
    }
}
