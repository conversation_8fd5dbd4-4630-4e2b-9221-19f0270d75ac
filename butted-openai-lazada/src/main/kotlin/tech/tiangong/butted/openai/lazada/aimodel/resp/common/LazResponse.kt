package tech.tiangong.butted.openai.lazada.aimodel.resp.common

import java.io.Serializable

/**
 * lazada模型返回报文
 */
data class LazResponse<T>(
    /**
     * 请求ID
     */
    val requestId: String? = null,
    /**
     * 响应类型
     */
    var type: String? = null,
    /**
     * 响应码：0-成功
     */
    val code: String? = null,
    /**
     * 响应体
     */
    val body: String? = null,
    /**
     * 响应信息
     */
    val message: String? = null,
    /**
     * 响应数据
     */
    var data: T? = null,

    ) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): <PERSON><PERSON>an {
        return code == null || "0" == code
    }

    /**
     * 失败的响应
     */
    fun failed(): Boolean = !succeed()

    companion object {
        private const val serialVersionUID: Long = 1L

        fun <T> build(code: String?, data: T? = null): LazResponse<T> {
            return LazResponse(
                code = code,
                data = data
            )
        }

        fun <T> build(
            requestId: String?,
            type: String?,
            code: String?,
            message: String?
        ): LazResponse<T> {
            return LazResponse(
                requestId = requestId,
                type = type,
                code = code,
                message = message,
                data = null
            )
        }

        fun <T> success(data: T? = null): LazResponse<T> {
            return LazResponse(
                code = "0",
                data = data
            )
        }

    }

}