package tech.tiangong.butted.openai.lazada.service

import tech.tiangong.butted.openai.vo.req.ClothingPoseMatchReq
import tech.tiangong.butted.openai.vo.resp.ClothingPoseMatchVo

/**
 * 服装姿势匹配
 * @author: chazz
 * @since: 2025年04月11日9:59:59
 * @version: 1.0
 **/
interface ClothingPoseMatchService {

    /**
     * 服装姿势匹配图列表查询
     * @param req ClothingPoseMatchReq
     */
    fun list(req: ClothingPoseMatchReq): List<ClothingPoseMatchVo>
}