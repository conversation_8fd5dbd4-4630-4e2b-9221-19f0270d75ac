package tech.tiangong.butted.openai.lazada.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitToList
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.ShiftSceneTaskEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.entity.ShiftSceneTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.aimodel.req.ShiftSceneModelReq
import tech.tiangong.butted.openai.lazada.aimodel.resp.ImageListOutputVo
import tech.tiangong.butted.openai.lazada.config.properties.ShiftSceneLazProperties
import tech.tiangong.butted.openai.lazada.convert.ShiftSceneConvert
import tech.tiangong.butted.openai.lazada.service.base.LazTaskBaseSupportImpl
import tech.tiangong.butted.openai.repository.ShiftSceneTaskRepository
import tech.tiangong.butted.openai.service.ShiftSceneTaskService
import tech.tiangong.butted.openai.vo.query.ShiftSceneTaskQuery
import tech.tiangong.butted.openai.vo.req.ShiftSceneTaskReq
import tech.tiangong.butted.openai.vo.resp.ShiftSceneTaskVo
import tech.tiangong.butted.oss.upload

/**
 * Lazada换场景（背景）任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class ShiftSceneTaskLazServiceImpl(
    config: ShiftSceneLazProperties,
    val shiftSceneTaskRepository: ShiftSceneTaskRepository
) : LazTaskBaseSupportImpl<ShiftSceneTask, ImageListOutputVo>(
    config,
    shiftSceneTaskRepository
), ShiftSceneTaskService {
    init {
        modeEnum = LazTaskModeEnum.LAZ_SHIFT_SCENE
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: ShiftSceneTaskReq): Long {
        val task = ShiftSceneConvert.build(req)
        task.thirdTaskId = createTask(task)
        shiftSceneTaskRepository.saveManualFill(task)
        return task.taskId!!
    }


    override fun buildLazModelReq(task: ShiftSceneTask): ShiftSceneModelReq {
        return ShiftSceneConvert.packShiftSceneModelReq(task)
    }

    override fun obtainThirdTaskId(task: ShiftSceneTask): String {
        return task.thirdTaskId!!
    }


    override fun detail(taskId: Long): ShiftSceneTaskVo {
        val task = obtainById(taskId)
        return task.copy(ShiftSceneTaskVo::class).apply {
            this.resImgList = task.resImgs.splitToList()
        }
    }

    override fun getBySource(sourceId: Long, sourceType: String): ShiftSceneTask? {
        return shiftSceneTaskRepository.getBySource(sourceId, sourceType)
    }

    override fun getBySource(sourceIdList: List<Long>, sourceType: String): List<ShiftSceneTask> {
        return shiftSceneTaskRepository.getBySource(sourceIdList, sourceType)
    }

    override fun handlePullResult(
        task: ShiftSceneTask,
        changeStateEnum: TaskStatusEnum,
        output: ImageListOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            if (output?.data.isEmpty()) {
                handleException(task, "AI换背景图结果为空", TaskStatusEnum.FAILED)
                return
            }
            output!!.let { ol ->
                task.resImgs = ol.data!!.map { it.upload(ContentType.IMAGE_PNG) }.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, ShiftSceneTaskEvent(task, changeStateEnum))
    }


    override fun handleException(task: ShiftSceneTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task, ShiftSceneTaskEvent(task, failStatus))
    }


    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return ShiftSceneTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.algorithms = algorithm().code
                it.pageNum = curPageNum
                shiftSceneTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: ShiftSceneTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            shiftSceneTaskRepository.updateByIdManualFill(task)
            event?.also {
                afterCommitExec {
                    publishEvent(it)
                }
            }
        }
    }

    override fun algorithm(): AlgorithmEnum {
        return AlgorithmEnum.LAZADA
    }
}
