package tech.tiangong.butted.openai.lazada.service.base

import org.springframework.scheduling.annotation.Async
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazTaskOutputVo
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository
import java.util.concurrent.Future

/**
 * 潮际好麦MarketTaskSyncSupportImpl
 */
@Slf4j
abstract class LazTaskSyncSupportImpl<T : SyncTask, O : LazTaskOutputVo>(
    config: OpenAiTaskConfig,
    manualBaseRepository: ManualBaseRepository<*, T>
) : LazTaskBaseSupportImpl<T, O>(
    config,
    manualBaseRepository
), LazTaskSyncSupport<T> {

    @Async
    override fun sync(): Future<Int> {
        return batchProcessing(TaskActionEnum.SYNC) {
            val syncCount = loopDoHandle("循环同步", TaskActionEnum.SYNC) {
                handleSync(it)
            }
            syncCount
        }
    }

    override fun handleSync(taskId: Long) {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查是否同步给业务状态了
                doSync(task)
            }
        } catch (e: Exception) {
            log.error { "${modeEnum.name}任务同步失败：${e.stackTraceToString()}" }
        }
    }

    /**
     * 事务提交后回调业务
     */
    open fun callbackBusiness(task: T, responseText: String? = null) {
        super.callbackBusiness(task, modeEnum, responseText)
    }

    override fun handleSyncResponse(task: T, aiModeEnum: LazTaskModeEnum, response: DataResponse<*>) {
        super.handleSyncResponse(task, aiModeEnum, response)
        manualBaseRepository.updateByIdManualFill(task)
    }


    override fun userSuspendTask(task: T) {
        lockExec(task.taskId) {
            cancelTask(obtainThirdTaskId(task))
            task.synced().suspend()
            manualBaseRepository.updateById(task)
        }
    }


}


