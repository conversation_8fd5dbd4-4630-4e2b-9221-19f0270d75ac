package tech.tiangong.butted.openai.lazada.aimodel.resp.common

import java.io.Serializable

/**
 * lazada模型返回报文
 *
 *   "result" : {
 *     "success" : true,
 *     "resultCode" : "",
 *     "resultMessage" : "",
 *     "class" : "com.lazada.lazlike.openapi.response.ProductImageMatchResult",
 *     "matchImageUrls" : [ "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png" ]
 *   }
 *
 */
open class LazResultVo(
    /**
     * 是否成功
     */
    var success: Boolean? = null,
    /**
     * 响应码
     */
    var resultCode: String? = null,
    /**
     * 失败的原因
     */
    val resultMessage: String? = null,
    /**
     * 失败的原因
     */
    val failMessage: String? = null,

//    /**
//     * vo类型
//     */
//    val class: String? = null,

) : Serializable {

    /**
     * 失败的原因
     */
    fun failMessage(): String? {
        return failMessage ?: resultMessage
    }

    /**
     * 成功的响应
     */
    fun succeed(): Boolean {
        return success == true
    }

    /**
     * 失败的响应
     */
    fun failed(): Boolean = !succeed()

    companion object {
        private const val serialVersionUID: Long = 1L

    }

}