package tech.tiangong.butted.openai.lazada.aimodel.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serializable


/**
 * AI换脸
 */
data class ShiftFaceModelReq(
    /**
     * 原图Url
     */
    @field:NotBlank(message = "原图不能为空")
    var raw_image_url: String? = null,
    /**
     * 模特⾯容图像Url（暂时不支持）
     */
    @field:NotBlank(message = "模特⾯容图像Url不能为空")
    var model_face_image_url: String? = null,
    /**
     * 模特code，和业务约定好的字符串
     */
    @field:NotBlank(message = "模特编码不能为空")
    var model_code: String? = null,
    /**
     * ⽣成图⽚数量[1，4] ，默认是1
     */
    @field:NotNull(message = "生成图片数量不能为空")
    var batch_size: Int? = 1,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
