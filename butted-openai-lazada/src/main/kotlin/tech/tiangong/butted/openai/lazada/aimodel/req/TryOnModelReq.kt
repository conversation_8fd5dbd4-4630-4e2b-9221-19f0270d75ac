package tech.tiangong.butted.openai.lazada.aimodel.req

import java.io.Serializable
import jakarta.validation.constraints.NotBlank


/**
 * AI换背景
 */
data class TryOnModelReq(
    /**
     * 输⼊的服装Url
     */
    @field:NotBlank(message = "输⼊的服装Url不能为空")
    var cloth_image_url: String? = null,
    /**
     * ⽤于上下装⼀起更换等场景，输⼊下装的图像
     */
    var additional_cloth_image_url: String? = null,
    /**
     * 换装类型，取值：
     * upper （上装）
     * lower （下装）
     * overall （全身，组合换装，需要additionalClothImageUrl）
     */
    @field:NotBlank(message = "换装类型不能为空")
    var type: String? = null,
    /**
     * 模特参考图
     */
    @field:NotBlank(message = "模特参考图不能为空")
    var model_reference_image_url: String? = null,

    /**
     * 默认1:1
     */
    //@field:NotBlank(message = "ratio不能为空")
    var ratio: String? = "",
    /**
     * 默认False，保持模特身材、⼿部、配饰等
     */
    var keep_model: Boolean? = null,
    /**
     * ⽣成图⽚数量[1，4] ，默认是1
     */
    var batch_size: Int? = 1,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
