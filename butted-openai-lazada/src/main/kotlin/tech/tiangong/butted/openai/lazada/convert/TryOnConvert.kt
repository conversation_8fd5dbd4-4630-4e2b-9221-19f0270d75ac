package tech.tiangong.butted.openai.lazada.convert

import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.BizSourceEnum
import tech.tiangong.butted.common.enums.TryOnTypeEnum
import tech.tiangong.butted.common.req.tryon.TryOnTaskCreateReq
import tech.tiangong.butted.common.vo.ResShiftImgVo
import tech.tiangong.butted.common.vo.tryon.TryOnTaskVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.*
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.enums.ShiftFaceEnum
import tech.tiangong.butted.enums.TryOnModeEnum
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.TryOnTask
import tech.tiangong.butted.openai.entity.TryOnTaskOutput
import tech.tiangong.butted.openai.lazada.aimodel.req.TryOnModelReq
import tech.tiangong.butted.openai.lazada.aimodel.resp.ImageListOutputVo
import tech.tiangong.butted.openai.vo.req.ShiftFaceTaskReq
import tech.tiangong.butted.openai.vo.resp.base.ModelMaterialEncodeVo
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.oss.upload
import java.util.concurrent.atomic.AtomicInteger

@Slf4j
object TryOnConvert : BaseConvert {

    fun build(req: TryOnTaskCreateReq): TryOnTask {
        val task = TryOnTask().apply {
            this.taskId = req.busId!!
            this.busId = req.busId
            this.busCode = req.busCode
            this.initBase()
            this.modeCode = req.modeCode!!
            this.modeName = req.modeName!!
            this.category = req.category
            this.humanImgUrl = req.humanImgUrl.toFullImgPath()
            this.genCount = req.genCount
            this.shiftFace = ShiftFaceEnum.NO.code
            this.scenePictureId = req.scenePictureId
            this.scenePicturePath = req.scenePicturePath
            this.scenePictureOutputSize = req.scenePictureOutputSize
            this.scenePictureEncode = req.scenePictureEncode.toJson()
            this.algorithms = AlgorithmEnum.LAZADA.code
            this.callback = req.callback
        }

        //模特面容检验
        task.modelImgUrl = req.modelImgUrl
        if (req.modelImgUrl.isNotBlank()) {
            val modelImgEncode = req.modelImgEncode.assertNotNull("面容图编码不能空")
            if (!TryOnModeEnum.fromLaz(modelImgEncode.modeCode)) {
                throw IllegalArgumentException("不支持面容类型[${modelImgEncode.modeCode}]编码编码[${modelImgEncode.encode}]")
            }
            task.modelImgEncode = modelImgEncode.toJson()
        }

        task.tryOnType = req.tryOnType!!.code
        when (req.tryOnType) {
            TryOnTypeEnum.UPPER -> {
                task.upperClothImg = req.upperClothImg.assertNotBlank("上装图不能空")
                task.upperClothMaskImg = req.upperClothMaskImg
            }

            TryOnTypeEnum.LOWER -> {
                task.lowerClothImg = req.lowerClothImg.assertNotBlank("下装图不能空")
                task.lowerClothMaskImg = req.lowerClothMaskImg
            }

            TryOnTypeEnum.OVERALL -> {
                if (req.overallClothImg.isNotBlank()) {
                    task.overallClothImg = req.overallClothImg
                    task.overallClothMaskImg = req.overallClothMaskImg
                } else {
                    task.upperClothImg = req.upperClothImg.assertNotBlank("全身-上装图不能空")
                    task.upperClothMaskImg = req.upperClothMaskImg
                    task.lowerClothImg = req.lowerClothImg.assertNotBlank("全身-下装图不能空")
                    task.lowerClothMaskImg = req.lowerClothMaskImg
                }
            }

            else -> {
                throw IllegalArgumentException("换装类型错误")
            }
        }
        task.cleanPushed()
        return task

    }


    fun buildTryOnTaskOutputList(task: TryOnTask, outputList: ImageListOutputVo): List<TryOnTaskOutput> {
        val serialNumAtomic = AtomicInteger()
        return outputList.data!!.map {
            TryOnTaskOutput().apply {
                outputId = IdHelper.getId()
                taskId = task.taskId
                resImg = it.upload(ContentType.IMAGE_PNG)
                shiftFaceImg = null
                shiftState = ShiftFaceEnum.NO.code
                shiftSceneImg = null
                serialNum = serialNumAtomic.incrementAndGet()
                this.initBaseUserContent(task)
            }
        }
    }

    fun packTryOnTaskVo(task: TryOnTask, outputList: List<TryOnTaskOutput>): TryOnTaskVo {
        return task.copy(TryOnTaskVo::class).apply {
            shiftFaceImgList = task.shiftFaceImgs?.split(Constant.COMMA)
            resImgList = outputList.map {
                ResShiftImgVo(shiftImg = it.shiftFaceImg).apply {
                    if (it.shiftSceneImg.isNotBlank()) {
                        this.resImg = it.shiftSceneImg
                    } else {
                        this.resImg = it.resImg
                    }
                }
            }
        }
    }

    fun packShiftFaceTaskReq(task: TryOnTask): ShiftFaceTaskReq {
        val modelImgEncode: ModelMaterialEncodeVo =
            task.modelImgEncode.parseObj() ?: throw IllegalArgumentException("Laz面容编码不能空")
        return ShiftFaceTaskReq(
            parentTaskId = task.taskId!!,
            sourceId = task.taskId!!,
            source = BizSourceEnum.TRY_ON,
            humanImgList = listOf(task.humanImgUrl!!),
            faceImgList = listOf(task.modelImgUrl!!),
            faceEncode = modelImgEncode.encode!!,
            algorithms = AlgorithmEnum.LAZADA
        ).apply {
            this.tenantId = task.tenantId
            this.creatorId = task.creatorId
            this.creatorName = task.creatorName
        }
    }

    fun packTryOnModelReq(task: TryOnTask): TryOnModelReq {
        val req = TryOnModelReq()
        req.type = task.tryOnType
        when (task.tryOnType) {
            TryOnTypeEnum.UPPER.code -> {
                req.cloth_image_url = task.upperClothImg!!
            }

            TryOnTypeEnum.LOWER.code -> {
                req.cloth_image_url = task.lowerClothImg!!
            }

            TryOnTypeEnum.OVERALL.code -> {
                if (task.overallClothImg.isNotBlank()) {
                    req.cloth_image_url = task.overallClothImg!!
                } else {
                    req.cloth_image_url = task.upperClothImg!!
                    req.additional_cloth_image_url = task.lowerClothImg!!
                }
            }

            else -> {
                throw IllegalArgumentException("换装类型错误")
            }
        }
        req.model_reference_image_url = if (task.humanShiftFaceImgUrl.isNotBlank()) {
            task.humanShiftFaceImgUrl!!
        } else {
            task.humanImgUrl!!
        }
        req.ratio = ""
        req.keep_model = null
        req.batch_size = task.genCount!!
        return req
    }


}


