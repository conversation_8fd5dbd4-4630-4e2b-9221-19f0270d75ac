package tech.tiangong.butted.openai.lazada.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.core.toolkit.assertNotBlank
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.ShiftSceneTask
import tech.tiangong.butted.openai.lazada.aimodel.req.ShiftSceneModelReq
import tech.tiangong.butted.openai.vo.req.ShiftSceneTaskReq

@Slf4j
object ShiftSceneConvert : BaseConvert {

    fun build(req: ShiftSceneTaskReq): ShiftSceneTask {
        return ShiftSceneTask().apply {
            this.manualInitBaseTask(req)
            this.parentTaskId = req.parentTaskId
            this.sourceId = req.sourceId
            this.sourceType = req.source!!.code
            this.srcImg = req.srcImg
            this.bgImg = req.bgImg
            this.bgOutputSize = req.bgOutputSize
            this.algorithms = req.algorithms?.code
            this.bgEncode = req.bgEncode.assertNotBlank("背景图编码不能空")
        }
    }


    fun packShiftSceneModelReq(task: ShiftSceneTask): ShiftSceneModelReq {
        return ShiftSceneModelReq(
            product_image_url = task.srcImg!!,
            background_code = task.bgEncode!!,
            ratio = "1:1",
            batch_size = 1,
        )
    }


}


