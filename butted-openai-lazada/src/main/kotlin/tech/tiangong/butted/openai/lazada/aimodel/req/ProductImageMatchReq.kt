package tech.tiangong.butted.openai.lazada.aimodel.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serializable


/**
 * 商品图匹配Req
 * <AUTHOR>
 */
data class ProductImageMatchReq(
    /**
     * 输⼊的服装图像Url（单个，需要内⽹可下载）
     */
    @field:NotBlank(message = "输⼊的服装图像Url不能为空")
    var image_url: String? = null,
    /**
     * 获取top n商品图（默认为5，最⼤值 10）
     */
    @field:NotNull(message = "匹配个数不能为空")
    var match_num: Int? = 10,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
