package tech.tiangong.butted.openai.lazada.component

import jakarta.annotation.PostConstruct
import org.springframework.beans.factory.ObjectProvider
import org.springframework.stereotype.Component
import team.aikero.blade.core.toolkit.isNotEmpty
import tech.tiangong.butted.openai.component.TaskProcessFactory
import tech.tiangong.butted.openai.lazada.service.base.LazTaskBaseSupport
import tech.tiangong.butted.openai.lazada.service.base.LazTaskSyncSupport
import java.util.stream.Collectors

/**
 * @description:
 * @author: chazz
 * @since: 2024年11月21日16:50:03
 * @version: 1.0
 **/
@Component
class LazTaskProcessFactoryImpl(
    taskProvider: ObjectProvider<LazTaskBaseSupport<*>>,
) : TaskProcessFactory {
    val taskServiceMap: Map<String, LazTaskBaseSupport<*>> = taskProvider.orderedStream()
        .collect(Collectors.toList()).associateBy { it.taskMode() }

    override fun notify(taskMode: String, taskId: String, jsonBody: String?): Boolean {
        return true
    }

    //先简单处理，后面优化
    override fun pullJob(modeNames: List<String>?): Boolean {
        if (modeNames.isNotEmpty()) {
            modeNames!!.forEach { modeName ->
                taskServiceMap[modeName]?.also { service ->
                    service.pull()
                }
            }
            return true
        }
        taskServiceMap.values.forEach { service ->
            service.pull()
        }
        return true
    }

    //先简单处理，后面优化
    override fun syncJob(modeNames: List<String>?): Boolean {
        if (modeNames.isNotEmpty()) {
            modeNames!!.forEach { modeName ->
                taskServiceMap[modeName]?.also { service ->
                    if (service is LazTaskSyncSupport) {
                        service.sync()
                    }
                }
            }
            return true
        }

        taskServiceMap.forEach { (mode, service) ->
            if (service is LazTaskSyncSupport) {
                service.sync()
            }
        }
        return true
    }

    @PostConstruct
    fun initialize() {
        taskServiceMap.forEach { (key, service) ->
            println("初始化Lazada任务模型: $key[${service.javaClass.name}]")
        }
    }

}