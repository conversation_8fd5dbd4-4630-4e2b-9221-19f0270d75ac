package tech.tiangong.butted.openai.lazada.aimodel.resp.common

import java.io.Serializable

/**
 * lazada模型返回报文
 */
data class TaskModelResponse<T : LazResultVo>(
    /**
     * 请求ID
     */
    val requestId: String? = null,
    /**
     * 响应类型
     */
    var type: String? = null,
    /**
     * 响应码：0-成功
     */
    val code: String? = null,
    /**
     * 响应信息
     */
    val message: String? = null,
    /**
     * 响应数据
     */
    var data: T? = null,

    ) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): Boolean {
        return code == null || "0" == code
    }

    /**
     * 失败的响应
     */
    fun failed(): Boolean = !succeed()

    companion object {
        private const val serialVersionUID: Long = 1L

        fun <T : LazResultVo> build(code: String?, data: T? = null): TaskModelResponse<T> {
            return TaskModelResponse(
                code = code,
                data = data
            )
        }

        fun <T : LazResultVo> build(
            requestId: String?,
            type: String?,
            code: String?,
            message: String?
        ): TaskModelResponse<T> {
            return TaskModelResponse(
                requestId = requestId,
                type = type,
                code = code,
                message = message,
                data = null
            )
        }

        fun <T : LazResultVo> success(data: T? = null): TaskModelResponse<T> {
            return TaskModelResponse(
                code = "0",
                data = data
            )
        }

    }

}