package tech.tiangong.butted.openai.lazada.aimodel.resp.common

import java.io.Serializable

/**
 * lazada模型返回报文
 *
 * 返回参数
 * {
 *   "result" : {
 *     "success" : true,
 *     "resultCode" : "",
 *     "resultMessage" : "",
 *     "class" : "com.lazada.lazlike.openapi.response.ProductImageMatchResult",
 *     "matchImageUrls" : [ "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png" ]
 *   },
 *   "code" : "0",
 *   "request_id" : "214118c717443389626888101"
 * }
 */
data class LazResultWapVo<T : LazResultVo>(
    /**
     * 请求ID
     */
    val request_id: String? = null,
    /**
     * 响应类型
     */
    var type: String? = null,
    /**
     * 响应码：0-成功
     */
    val code: String? = null,
    /**
     * 响应信息
     */
    val message: String? = null,
    /**
     * 响应数据
     * 成功时的数据，对于换⾐、换背景、换脸这⼏个
     * API来说，data都是List<String>，图⽚的Url
     */
    var result: T? = null,

    ) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): Boolean {
        return code == null || "0" == code
    }

    /**
     * 失败的响应
     */
    fun failed(): Boolean = !succeed()

    companion object {
        private const val serialVersionUID: Long = 1L

    }

}