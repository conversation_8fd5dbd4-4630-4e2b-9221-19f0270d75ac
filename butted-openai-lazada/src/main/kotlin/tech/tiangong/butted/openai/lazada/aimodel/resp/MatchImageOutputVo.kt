package tech.tiangong.butted.openai.lazada.aimodel.resp

import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazResultVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelOutputVo

/**
 * 图片列表输出VO
 * 返回参数
 * {
 *   "result" : {
 *     "success" : true,
 *     "resultCode" : "",
 *     "resultMessage" : "",
 *     "class" : "com.lazada.lazlike.openapi.response.ProductImageMatchResult",
 *     "matchImageUrls" : [ "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png", "https://chuangxin-oss-cdn.tiangong.tech/tiangong_fb1ef6c7ec2a465d8049e0d8b7fc94d6.png" ]
 *   },
 *   "code" : "0",
 *   "request_id" : "214118c717443389626888101"
 * }
 *
 */
class MatchImageOutputVo(
    var matchImageUrls: List<String>? = null
) : LazResultVo(), TaskModelOutputVo {
    override fun toString(): String {
        return "MatchImageOutputVo(" +
                "matchImageUrls=$matchImageUrls" + "\n"
        "super=${super.toString()}"
        ")"
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}


//fun main(args: Array<String>) {
//    val json = "[\"https://example.com/image1.jpg\",\"https://example.com/image2.jpg\"]"
//
//    val parseJson = json.parseJson(ImageListOutputVo::class.java)
//    println(parseJson)
//    parseJson.forEach {
//        println(it)
//    }
//
//}