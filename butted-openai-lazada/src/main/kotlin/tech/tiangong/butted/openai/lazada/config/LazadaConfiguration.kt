package tech.tiangong.butted.openai.lazada.config

import com.lazada.lazop.api.LazopClient
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import tech.tiangong.butted.openai.lazada.config.properties.LazadaProperties
import tech.tiangong.butted.openai.lazada.config.properties.ShiftFaceLazProperties
import tech.tiangong.butted.openai.lazada.config.properties.ShiftSceneLazProperties
import tech.tiangong.butted.openai.lazada.config.properties.TryOnLazProperties


/**
 * 配置文件
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(
    value = [
        LazadaProperties::class, TryOnLazProperties::class,
        ShiftSceneLazProperties::class, ShiftFaceLazProperties::class
    ]
)
class LazadaConfiguration {

    @Bean
    @ConditionalOnMissingBean
    fun lazopClient(lazadaProperties: LazadaProperties): LazopClient {
        return LazopClient(
            lazadaProperties.endpoint,
            lazadaProperties.appKey,
            lazadaProperties.appSecret,
            lazadaProperties.connectTimeout,
            lazadaProperties.readTimeout
        )
    }

}