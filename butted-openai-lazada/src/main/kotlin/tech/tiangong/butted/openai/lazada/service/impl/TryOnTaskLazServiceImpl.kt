package tech.tiangong.butted.openai.lazada.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.beans.factory.ObjectProvider
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.data.mybatis.toolkit.toPageVo
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.BizSourceEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.tryon.TryOnTaskCreateReq
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.common.vo.tryon.TryOnRelatedThirdIdVo
import tech.tiangong.butted.common.vo.tryon.TryOnTaskVo
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.*
import tech.tiangong.butted.event.ShiftFaceTaskEvent
import tech.tiangong.butted.event.ShiftSceneTaskEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.entity.TryOnTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.aimodel.req.TryOnModelReq
import tech.tiangong.butted.openai.lazada.aimodel.resp.ImageListOutputVo
import tech.tiangong.butted.openai.lazada.config.properties.TryOnLazProperties
import tech.tiangong.butted.openai.lazada.convert.TryOnConvert
import tech.tiangong.butted.openai.lazada.remote.LazCallbackApi
import tech.tiangong.butted.openai.lazada.service.base.LazTaskSyncSupportImpl
import tech.tiangong.butted.openai.repository.TryOnTaskOutputRepository
import tech.tiangong.butted.openai.repository.TryOnTaskRepository
import tech.tiangong.butted.openai.service.ShiftFaceTaskService
import tech.tiangong.butted.openai.service.TryOnTaskExternalService
import tech.tiangong.butted.openai.service.TryOnTaskOutputService
import tech.tiangong.butted.openai.vo.query.TryOnTaskQuery
import java.time.LocalDateTime
import java.util.stream.Collectors

/**
 * 虚拟换装任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TryOnTaskLazServiceImpl(
    config: TryOnLazProperties,
    val tryOnTaskOutputService: TryOnTaskOutputService,
    val tryOnTaskRepository: TryOnTaskRepository,
    val tryOnTaskOutputRepository: TryOnTaskOutputRepository,
    shiftFaceProvider: ObjectProvider<ShiftFaceTaskService>,
) : LazTaskSyncSupportImpl<TryOnTask, ImageListOutputVo>(
    config,
    tryOnTaskRepository
), TryOnTaskExternalService {
    init {
        modeEnum = LazTaskModeEnum.LAZ_TRYON
    }

    val shiftFaceServiceMap: Map<String, ShiftFaceTaskService> = shiftFaceProvider.orderedStream()
        .collect(Collectors.toList()).associateBy { it.algorithm().name }

    override fun page(query: TryOnTaskQuery): PageVo<TryOnTaskVo> {
        return tryOnTaskRepository.findPage(query).toPageVo()
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: TryOnTaskCreateReq): Long {
        val task = TryOnConvert.build(req)
        tryOnTaskRepository.save(task)
        afterCommitExec {
            executor.delayExecute(1000L) {
                handlePull(task.taskId!!)
            }
        }
        return task.taskId!!
    }


    override fun getByBusId(busId: Long): TryOnTaskVo {
        val task = tryOnTaskRepository.getByBusId(busId)
        val outputList = if (TaskStatusEnum.completed(task.taskStatus)) {
            tryOnTaskOutputService.findByTaskId(task.taskId!!)
        } else {
            listOf()
        }
        return TryOnConvert.packTryOnTaskVo(task, outputList).apply {
            this.taskStatus = getTaskRealStatus(task).code
        }
    }

    override fun getThirdIdByBusId(busId: Long): TryOnRelatedThirdIdVo {
        val task = tryOnTaskRepository.getByBusId(busId)
        val thirdIdVo = TryOnRelatedThirdIdVo(tryOnThirdId = task.thirdTaskId)
        if (task.modelImgUrl.isNotBlank()) {
            //Laz换脸
            val shiftFaceTaskService = shiftFaceServiceMap[AlgorithmEnum.LAZADA.name]!!
            shiftFaceTaskService.getBySource(task.taskId!!, BizSourceEnum.TRY_ON.code)?.also {
                thirdIdVo.shiftFaceThirdId = it.thirdTaskId
            }
        }
        if (task.scenePicturePath.isNotBlank()) {
            thirdIdVo.shiftSceneThirdIdList = tryOnTaskOutputService.findShiftSceneThirdIdList(task)
        }
        return thirdIdVo
    }


    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查推送
                if (task.pushStatus == PushStatusEnum.UN_PUSH.code) {
                    checkToExecuteTask(task)
                    return@lockExec
                }
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            throw RuntimeException(e)
        }

    }


    override fun doHandlePull(taskId: Long) {
        val task = obtainById(taskId)
        if (TaskStatusEnum.processing(task.taskStatus)) {
            super.doHandlePull(task)
            return
        }
        //任务已结束
        if (TaskStatusEnum.failedOrCanceled(task.taskStatus!!)) {
            return
        }
        //已完成，检查子任务状态
        handleSubtask(task)

    }


    private fun getTaskRealStatus(task: TryOnTask): TaskStatusEnum {
        val statusEnum = TaskStatusEnum.of(task.taskStatus!!)
        if (!statusEnum.completed()) {
            return statusEnum
        }
        val subtaskStatusEnum = TaskStatusEnum.of(task.subtaskStatus!!)
        return subtaskStatusEnum
    }

    //处理子任务
    fun handleSubtask(task: TryOnTask) {
        lockExec("CheckToExecuteTask:${task.taskId}") {
            //已完成，检查换背景
            if (task.scenePicturePath.isNotBlank()) {
                val shiftSceneEnum = tryOnTaskOutputService.checkShiftSceneProgress(task)
                if (!shiftSceneEnum.finished()) {
                    task.subtaskStatus = TaskStatusEnum.GENERATING.code
                    manualUpdateAiTaskInfo(task)
                    return@lockExec
                }
            }
            task.subtaskStatus = TaskStatusEnum.COMPLETED.code
            task.waitSync()
            task.aiEndTime = LocalDateTime.now()
            manualUpdateAiTaskInfo(task)
        }

    }


    override fun buildLazModelReq(task: TryOnTask): TryOnModelReq {
        return TryOnConvert.packTryOnModelReq(task)
    }

    override fun obtainThirdTaskId(task: TryOnTask): String {
        return task.thirdTaskId!!
    }

    override fun handlePullResult(
        task: TryOnTask,
        changeStateEnum: TaskStatusEnum,
        output: ImageListOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            if (output?.data.isEmpty()) {
                handleException(task, "虚拟试衣生图结果为空")
                return
            }
            //子任务开始
            task.subtaskStatus = TaskStatusEnum.QUEUEING.code
            val taskOutputList = TryOnConvert.buildTryOnTaskOutputList(task, output!!)
            withNewTranExec {
                tryOnTaskOutputRepository.saveBatchManualFill(taskOutputList)
            }
        }
        task.waitSync()
        task.taskStatus = changeStateEnum.code
        if (changeStateEnum.failedOrCanceled()) {
            task.failTaskMode = AiTaskModeEnum.TRYON.fullMode()
        }
        withNewTranExec {
            if (completed) {
                afterCommitExec {
                    executor.delayExecute(1000L) {
                        //检查子任务
                        handlePull(task.taskId!!)
                    }
                }
            }
            manualUpdateAiTaskInfo(task)
        }

    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenShiftFaceTaskEvent(event: ShiftFaceTaskEvent) {
        val task = event.getTask()
        if (task.sourceType == BizSourceEnum.TRY_ON.code && task.algorithms == AlgorithmEnum.LAZADA.code) {
            tryOnTaskRepository.getById(task.sourceId)?.also {
                checkToExecuteTask(it)
            }
        }
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenShiftSceneTaskEvent(event: ShiftSceneTaskEvent) {
        val task = event.getTask()
        if (task.sourceType == BizSourceEnum.TRY_ON_OUTPUT.code) {
            tryOnTaskRepository.getById(task.parentTaskId)?.also {
                if (TryOnModeEnum.fromLaz(it.modeCode)) {
                    handleSubtask(it)
                }
            }
        }
    }

    fun checkToExecuteTask(task: TryOnTask) {
        try {
            lockExec("CheckToExecuteTask:${task.taskId}") {
                if (TaskStatusEnum.finished(task.taskStatus)) {
                    return@lockExec
                }
                if (!PushStatusEnum.unPushed(task.pushStatus)) {
                    return@lockExec
                }
                if (checkTaskReady(task) != TaskReadyEnum.READY) {
                    return@lockExec
                }
                try {
                    task.thirdTaskId = createTask(task)
                    task.waitSync().firstPushed()
                    manualUpdateAiTaskInfo(task)
                } catch (e: Exception) {
                    log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                    handlePushException(task, e.message.blankDefault("推送任务失败"), TaskStatusEnum.FAILED, null)
                }
            }
        } catch (e: Exception) {
            log.error { "检查推送TryOn任务失败：${e.stackTraceToString()}" }
        }
    }


    private fun checkTaskReady(
        task: TryOnTask,
    ): TaskReadyEnum {
        if (task.modelImgUrl.isBlank()) {
            return TaskReadyEnum.READY
        }
        //Laz换脸
        val shiftFaceTaskService = shiftFaceServiceMap[AlgorithmEnum.LAZADA.name]!!
        val shiftFaceTask = shiftFaceTaskService.getBySource(task.taskId!!, BizSourceEnum.TRY_ON.code)
        if (shiftFaceTask == null) {
            try {
                val shiftFaceTaskReq = TryOnConvert.packShiftFaceTaskReq(task)
                shiftFaceTaskService.manualCreate(shiftFaceTaskReq)
            } catch (e: Exception) {
                task.failTaskMode = LazTaskModeEnum.LAZ_SHIFT_FACE.name
                handlePushException(
                    task,
                    e.message.blankDefault("创建Laz换脸任务失败"),
                    TaskStatusEnum.FAILED,
                    null
                )
                return TaskReadyEnum.FAIL
            }
            return TaskReadyEnum.WAIT
        }
        val shiftFaceStatus = TaskStatusEnum.of(shiftFaceTask.taskStatus!!)
        if (shiftFaceStatus.processing()) {
            return TaskReadyEnum.WAIT
        }
        //失败
        if (!shiftFaceStatus.completed()) {
            handlePushException(
                task,
                shiftFaceTask.message,
                shiftFaceStatus,
                AiTaskModeEnum.SHIFT_FACE
            )
            return TaskReadyEnum.FAIL
        }
        task.humanShiftFaceImgUrl = shiftFaceTask.resImgs!!.splitAndFirst()
        return TaskReadyEnum.READY

    }


    private fun handlePushException(
        task: TryOnTask,
        message: String?,
        statusEnum: TaskStatusEnum,
        failMode: AiTaskModeEnum?
    ) {
        task.pushFailed()
        task.failTaskMode = failMode?.fullMode()
        handleException(task, message, statusEnum)
    }

    override fun handleException(task: TryOnTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }


    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return TryOnTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.algorithms = algorithm().code
                it.pageNum = curPageNum
                tryOnTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    override fun doSync(task: TryOnTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        tryOnTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: TryOnTask) {
        task.refreshRevisedTime()
        withTranExec {
            tryOnTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }


    fun cloneCallbackTask(task: TryOnTask): TryOnTask {
        return TryOnTask().apply {
            this.taskId = task.taskId
            this.busId = task.busId
            this.busCode = task.busCode
            this.taskStatus = getTaskRealStatus(task).code
            this.taskProgress = task.taskProgress
            this.message = task.message
            this.creatorId = task.creatorId
            this.creatorName = task.creatorName
            this.tenantId = task.tenantId
            this.callback = task.callback
        }
    }

    override fun callbackBusiness(task: TryOnTask, responseText: String?) {
        if (task.syncStatus == Bool.YES.code) {
            return
        }
        afterCommitExec {
            lockExec("TryOnTask:Callback:${task.taskId}") {
                val cloneTask = cloneCallbackTask(task)
                LazCallbackApi.callback(cloneTask, modeEnum).also {
                    super.fillSyncStatus(task, modeEnum, it)
                }
                tryOnTaskRepository.updateByIdManualFill(task)
            }
        }
    }


    override fun algorithm(): AlgorithmEnum {
        return AlgorithmEnum.LAZADA
    }
}
