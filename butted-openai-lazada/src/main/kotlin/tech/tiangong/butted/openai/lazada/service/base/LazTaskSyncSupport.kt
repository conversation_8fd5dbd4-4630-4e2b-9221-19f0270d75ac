package tech.tiangong.butted.openai.lazada.service.base

import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.success
import tech.tiangong.butted.jdbc.TransactionalManagerUtil
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.remote.LazCallbackApi
import tech.tiangong.butted.openai.service.base.TaskSyncSupport

/**
 * ChatTaskSyncSupport
 */
interface LazTaskSyncSupport<T : SyncTask> : TaskSyncSupport<T>, LazTaskBaseSupport<T> {

    fun doSync(task: T)

    fun handleSync(taskId: Long)

    override fun sync(taskIds: List<Long>) {
        taskIds.forEach {
            handleSync(it)
        }
    }


    /**
     * 事务提交后回调业务
     */
    fun callbackBusiness(task: T, modeEnum: LazTaskModeEnum, responseText: String? = null) {
        try {
            TransactionalManagerUtil.afterCommit {
                if (!needAsync(task) || task.callback.isBlank()) {
                    handleSyncResponse(task, modeEnum, ok(null))
                } else {
                    LazCallbackApi.callback(task, modeEnum, responseText).run {
                        handleSyncResponse(task, modeEnum, this)
                    }
                }
            }
        } catch (e: Exception) {
            log.error { "\"同步[${modeEnum.name}]任务失败：\n${e.stackTraceToString()}" }
        }
    }

    //子类可以覆盖此方法，TO更新持久化任务同步状态到数据库
    fun handleSyncResponse(task: T, aiModeEnum: LazTaskModeEnum, response: DataResponse<*>) {
        fillSyncStatus(task, aiModeEnum, response)
    }

    fun fillSyncStatus(task: T, aiModeEnum: LazTaskModeEnum, response: DataResponse<*>) {
        if (response.success()) {
            log.info { "同步[${aiModeEnum.name}]任务成功,${task.taskId}" }
            task.syncSuccess()
        } else {
            log.info { "同步[${aiModeEnum.name}]任务失败,${task.taskId}" }
            task.syncFail()
            task.message = response.message.blankDefault("同步[${aiModeEnum.name}]任务失败,${task.taskId}")
        }
    }


}