package tech.tiangong.butted.openai.lazada.service.base

import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import jakarta.annotation.Resource
import org.springframework.http.HttpMethod
import org.springframework.scheduling.annotation.Async
import org.springframework.util.Assert
import org.springframework.util.StopWatch
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pool.ButtedThreadPoolExecutor
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.tryExec
import tech.tiangong.butted.enums.LogTypeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.publish.OpenaiTaskEventPublisher
import tech.tiangong.butted.exception.RemoteException
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.BaseTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskStatusEnum
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazTaskOutputVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelResponse
import tech.tiangong.butted.openai.lazada.remote.LazadaApi
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository
import tech.tiangong.butted.openai.vo.query.base.TaskCommonQuery
import tech.tiangong.butted.redis.RedissonHelper
import tech.tiangong.butted.rest.RestApiLog
import tech.tiangong.butted.rest.TaskRestApiHandle
import tech.tiangong.butted.rest.TaskRestApiLog
import java.lang.reflect.ParameterizedType
import java.time.LocalDateTime
import java.util.concurrent.Future
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

/**
 * LazTaskBaseSupportImpl
 */
@Slf4j
abstract class LazTaskBaseSupportImpl<T : BaseTask, O : LazTaskOutputVo>(
    val config: OpenAiTaskConfig,
    val manualBaseRepository: ManualBaseRepository<*, T>
) : LazTaskBaseSupport<T> {
    @Resource
    lateinit var taskRestApiHandle: TaskRestApiHandle<*>
    protected val executor = ButtedThreadPoolExecutor.build(8, "laz-task-process")

    private var outputType: Class<O>? = null
    lateinit var modeEnum: LazTaskModeEnum

    abstract fun buildLazModelReq(task: T): Any
    abstract fun obtainThirdTaskId(task: T): String


    /**
     * 新增任务
     * @param task 任务实体
     */
    open fun createTask(task: T): String {
        val taskApiLog = TaskRestApiLog(restApiLog = RestApiLog()).apply {
            this.beforeExecute()
            this.url(modeEnum.apiName)
            this.method(HttpMethod.POST.name())
        }
        taskApiLog.taskId = task.taskId?.toString()
        taskApiLog.taskMode = modeEnum.name
        taskApiLog.logType = LogTypeEnum.CREATE_AI_TASK.code
        return try {
            val req = buildLazModelReq(task)
            taskApiLog.requestBody(req.toJson())
            val stopWatch = StopWatch().apply { this.start() }
            try {
                taskRestApiHandle.beforeExecute(taskApiLog)
            } catch (e: Exception) {
                log.error { "RestApiHandle beforeExecute error: ${e.stackTraceToString()}" }
            }
            LazadaApi.createTask(
                modeEnum.apiName, req
            ).let { resp ->
                //记录请求结束时间
                stopWatch.stop()
                taskApiLog.responseStatus(200)
                taskApiLog.costTime(stopWatch.lastTaskInfo().timeMillis)
                taskApiLog.responseBody(resp.toJson())
                if (resp.failed()) {
                    taskApiLog.responseStatus(500)
                    log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                    throw RemoteException(resp.message.blankDefault("创建Lazada[${modeEnum.name}]任务失败"))
                }
                taskApiLog.afterExecute()
                resp.data!!.taskId!!
            }
        } catch (e: Exception) {
            taskApiLog.exception(e)
            throw e
        } finally {
            logTask(taskApiLog)
        }
    }

    /**
     * 执行任务
     * @param thirdTaskId Laz任务ID
     */
    open fun fetchTask(thirdTaskId: String): TaskModelResponse<O> {
        val taskApiLog = TaskRestApiLog(restApiLog = RestApiLog()).apply {
            this.beforeExecute()
            this.url(modeEnum.apiName)
            this.method(HttpMethod.GET.name())
        }
        taskApiLog.taskId = thirdTaskId
        taskApiLog.taskMode = modeEnum.name
        taskApiLog.logType = LogTypeEnum.FETCH_AI_TASK.code
        return try {
            taskApiLog.requestBody(thirdTaskId)
            val stopWatch = StopWatch().apply { this.start() }
            try {
                taskRestApiHandle.beforeExecute(taskApiLog)
            } catch (e: Exception) {
                log.error { "RestApiHandle beforeExecute error: ${e.stackTraceToString()}" }
            }
            val response = LazadaApi.query(
                thirdTaskId,
                getOutputType()
            )
            stopWatch.stop()
            taskApiLog.responseStatus(200)
            taskApiLog.costTime(stopWatch.lastTaskInfo().timeMillis)
            taskApiLog.responseBody(response.toJson())
            if (response.failed()) {
                taskApiLog.responseStatus(500)
                taskApiLog.message(response.message)
            }
            taskApiLog.afterExecute()
            response
        } catch (e: Exception) {
            log.error { "${modeEnum.name}任务,拉取失败：${e.stackTraceToString()}" }
            taskApiLog.exception(e)
            throw e
        } finally {
            logTask(taskApiLog)
        }

    }

    /**
     * 取消Laz任务
     * @param thirdTaskId Laz任务ID
     */
    open fun cancelTask(thirdTaskId: String): Int {
        return cancelTask(listOf(thirdTaskId))
    }

    /**
     * 取消Laz任务
     * @param thirdTaskIds Laz任务ID列表
     */
    open fun cancelTask(thirdTaskIds: List<String>): Int {
        LazadaApi.cancel(
            thirdTaskIds
        ).let { resp ->
            if (resp.failed()) {
                log.error { "${modeEnum.name}任务[${thirdTaskIds}],取消失败：${resp.message}" }
                throw RemoteException(resp.message.blankDefault("取消Lazada[${modeEnum.name}]任务失败"))
            }
            resp.data?.canceledTaskCount ?: 0
        }

        val taskApiLog = TaskRestApiLog(restApiLog = RestApiLog()).apply {
            this.beforeExecute()
            this.url(modeEnum.apiName)
            this.method(HttpMethod.POST.name())
        }
        taskApiLog.taskId = thirdTaskIds.firstOrNull()?.toString()
        taskApiLog.taskMode = modeEnum.name
        taskApiLog.logType = LogTypeEnum.CANCEL_AI_TASK.code
        return try {
            taskApiLog.requestBody(thirdTaskIds.toJson())
            val stopWatch = StopWatch().apply { this.start() }
            try {
                taskRestApiHandle.beforeExecute(taskApiLog)
            } catch (e: Exception) {
                log.error { "RestApiHandle beforeExecute error: ${e.stackTraceToString()}" }
            }
            LazadaApi.cancel(
                thirdTaskIds
            ).let { resp ->
                //记录请求结束时间
                stopWatch.stop()
                taskApiLog.responseStatus(200)
                taskApiLog.costTime(stopWatch.lastTaskInfo().timeMillis)
                taskApiLog.responseBody(resp.toJson())
                if (resp.failed()) {
                    taskApiLog.responseStatus(500)
                    log.error { "${modeEnum.name}任务[${thirdTaskIds}],取消失败：${resp.message}" }
                    throw RemoteException(resp.message.blankDefault("取消Lazada任务失败"))
                }
                taskApiLog.afterExecute()
                resp.data?.canceledTaskCount ?: 0
            }
        } catch (e: Exception) {
            taskApiLog.exception(e)
            throw e
        } finally {
            logTask(taskApiLog)
        }
    }


    open fun logTask(taskApiLog: TaskRestApiLog) {
        tryExec {
            log.info { taskApiLog.toString() }
            //异步执行
            executor.execute {
                try {
                    taskRestApiHandle.afterExecute(taskApiLog)
                } catch (e: Exception) {
                    log.error { "RestApiHandle afterExecute error: ${e.stackTraceToString()}" }
                }
            }
        }
    }


    @Async
    override fun pull(): Future<Int> {
        return batchProcessing(TaskActionEnum.PULL) {
            val pullCount = loopDoHandle("循环拉取", TaskActionEnum.PULL) {
                handlePull(it)
            }
            pullCount
        }
    }

    override fun pull(taskIds: List<Long>) {
        taskIds.forEach { handlePull(it) }
    }

    open fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "拉取${modeEnum.name}任务失败：${e.stackTraceToString()}" }
            return false
        }
    }

    open fun doHandlePull(taskId: Long) {
        val task = obtainById(taskId)
        doHandlePull(task)
    }

    open fun doHandlePull(task: T) {
        //任务已结束
        if (!TaskStatusEnum.processing(task.taskStatus!!)) {
            return
        }
        //检查超时；暂无超时
        task.addPullTimes()
        val response = fetchTask(obtainThirdTaskId(task))
        routeResult(task, response)
    }


    private fun errorMsg(response: TaskModelResponse<O>): String {
        var errorMsg = response.data?.failMessage()
        if (errorMsg.isBlank()) {
            errorMsg = response.message
        }
        return errorMsg.blankDefault("拉取[${modeEnum.name}]模型任务信息失败")
    }

    open fun routeResult(task: T, response: TaskModelResponse<O>) {
        if (TaskStatusEnum.finished(task.taskStatus)) {
            return
        }
        task.message = response.message
        if (response.failed()) {
            task.taskStatus = TaskStatusEnum.FAILED.code
            task.message = errorMsg(response)
            handlePullResult(task, TaskStatusEnum.FAILED, null, false, task.message)
            return
        }
        val dataVo = response.data
        if (dataVo == null) {
            log.warn { "拉取${modeEnum.name}任务[${task.taskId}],结果为空：${response.toJson()}" }
            return
        }
        /**
         * 任务状态：
         * waiting： 等待中
         * running: 运⾏中
         * cancelled：取消中
         * fail: 失败
         * success: 成功
         */
        when (dataVo.status.blankDefault(LazTaskStatusEnum.WAITING.code)) {
            LazTaskStatusEnum.WAITING.code -> {
                task.taskStatus = TaskStatusEnum.QUEUEING.code
                handlePullResult(task, TaskStatusEnum.QUEUEING, null, false, response.message)
            }

            LazTaskStatusEnum.RUNNING.code -> {
                task.startAiTime()
                task.taskStatus = TaskStatusEnum.GENERATING.code
                handlePullResult(task, TaskStatusEnum.GENERATING, null, false, response.message)
            }

            LazTaskStatusEnum.CANCELLED.code -> {
                //如果是处理中即更改为取消状态
                if (TaskStatusEnum.processing(task.taskStatus)) {
                    task.suspend()
                }
                task.stopAiEndTime()
                handlePullResult(task, TaskStatusEnum.GENERATING, null, false, response.message)
            }

            LazTaskStatusEnum.SUCCESS.code -> {
                task.taskProgress = 100
                task.stopAiEndTime()
                task.taskStatus = TaskStatusEnum.COMPLETED.code
                handlePullResult(
                    task,
                    TaskStatusEnum.COMPLETED,
                    dataVo,
                    true,
                    response.message
                )
            }

            LazTaskStatusEnum.FAIL.code -> {
                task.taskStatus = TaskStatusEnum.FAILED.code
                task.message = errorMsg(response)
                handlePullResult(task, TaskStatusEnum.FAILED, null, false, task.message)
            }
        }
    }


    abstract fun handlePullResult(
        task: T,
        changeStateEnum: TaskStatusEnum,
        output: O?,
        completed: Boolean,
        message: String?
    )


    //给子类重写，调用
    open fun handleException(task: T, message: String?, statusEnum: TaskStatusEnum? = TaskStatusEnum.FAILED) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        task.message = message
        task.stopAiEndTime()
        task.taskStatus = failStatus.code
    }

    fun <T> loopDoHandle(tag: String, taskAction: TaskActionEnum, handle: (taskId: Long) -> T): Int {
        val handleCount = AtomicInteger(0)
        val curPageNum = AtomicInteger(0)
        var hasNext: Boolean
        val stop = AtomicBoolean(false)
        do {
            val page: IPage<out BaseTaskVo> = loopPage(taskAction, curPageNum.incrementAndGet())
            val list: List<BaseTaskVo>? = page.records
            if (list.isNotEmpty()) {
                for (taskVo in list!!) {
                    try {
                        if (stop.get()) {
                            break
                        }
                        handle(taskVo.taskId!!)
                        handleCount.incrementAndGet()
                        try {
                            if (taskAction == TaskActionEnum.PULL) {
                                stop.set(stopLoopHandlePull(taskVo.taskId!!, handleCount.get()))
                            }
                        } catch (e: Exception) {
                            log.error { "检查StopLoopHandlePull失败：${e.stackTraceToString()}" }
                        }
                    } catch (e: Exception) {
                        log.error { "$tag【${taskVo.taskId}】${modeEnum.name}任务失败：${e.stackTraceToString()}" }
                    }
                }
            }
            //是否还有下一页
            hasNext = list.isNotEmpty() && page.pages > curPageNum.get()
        } while (hasNext)
        println("【$tag】${modeEnum.name}任务：共[${handleCount.get()}]条 --【${LocalDateTime.now()}】")
        return handleCount.get()
    }

    open fun stopLoopHandlePull(taskId: Long, handleCount: Int): Boolean {
        return handleCount > Random.nextLong(2000, 10000)
    }

    fun emptyPage(): IPage<out BaseTaskVo> = Page.of(0, 0, 0)

    abstract fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo>

    fun <Q : TaskCommonQuery> Q.adaptActionQuery(taskAction: TaskActionEnum): Boolean {
        return when (taskAction) {
            TaskActionEnum.PULL -> {
                this.pullQuery()
                true
            }

            TaskActionEnum.SYNC -> {
                this.syncQuery(config.maxSyncFailTimes, config.lastSyncHours)
                true
            }

            else -> {
                false
            }
        }
    }

    open fun userSuspendTask(task: T) {
        lockExec(task.taskId) {
            task.suspend()
            manualBaseRepository.updateById(task)
        }
    }


    override fun obtainById(taskId: Long): T {
        return manualBaseRepository.obtainById(taskId, "[${modeEnum.name}]任务不存在！")
    }


    fun <R> lockExec(lockId: Long?, fn: () -> R): R {
        return lockExec(lockId?.toString(), fn)
    }

    fun <R> checkLockExec(lockId: Long?, needLock: Boolean, fn: () -> R): R {
        if (!needLock) {
            return fn()
        }
        return lockExec(lockId?.toString(), fn)
    }

    fun <R> lockExec(lockKey: String?, fn: () -> R): R {
        try {
            return RedissonHelper.lock("${config.handleLockKey}:$lockKey", config.lockWait, config.lockLease) {
                fn()
            }
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            throw RuntimeException(e)
        }
    }


    /**
     * 任务类型
     */
    override fun taskMode(): String {
        return modeEnum.name
    }

    fun publishEvent(event: OpenaiTaskEvent<*>) {
        OpenaiTaskEventPublisher.publish(event)
    }

    @Suppress("UNCHECKED_CAST")
    open fun getOutputType(): Class<O> {
        if (outputType != null) {
            return outputType!!
        }
        val aClass = this.javaClass
        val genericSuperclass = aClass.genericSuperclass
        val parameterizedType = genericSuperclass as ParameterizedType
        val actualTypeArguments = parameterizedType.actualTypeArguments
        Assert.isTrue(actualTypeArguments.size == 2, "Not Found OutputType!")
        outputType = actualTypeArguments[1] as Class<O>
        return outputType!!
    }
}


