package tech.tiangong.butted.openai.lazada.aimodel.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serializable


/**
 * AI修手
 */
data class FixHandModelReq(
    /**
     * 原图Url
     */
    @field:NotBlank(message = "原图不能为空")
    var rawImageUrl: String? = null,
    /**
     * 默认False，是否参考原图⼿部细节
     */
    var baseRef: Boolean? = null,
    /**
     * ⽣成图⽚数量[1，4] ，默认是1
     */
    @field:NotNull(message = "生成图片数量不能为空")
    var batchSize: Int? = 1,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
