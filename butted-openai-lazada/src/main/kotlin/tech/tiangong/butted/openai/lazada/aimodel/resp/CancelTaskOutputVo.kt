package tech.tiangong.butted.openai.lazada.aimodel.resp

import tech.tiangong.butted.openai.lazada.aimodel.resp.common.LazResultVo
import tech.tiangong.butted.openai.lazada.aimodel.resp.common.TaskModelOutputVo

/**
 * 取消任务输出VO
 */
class CancelTaskOutputVo(
    /**
     * canceled task count
     */
    var canceledTaskCount: Int? = null
) : LazResultVo(), TaskModelOutputVo {

    companion object {
        private const val serialVersionUID = 1L
    }
}

