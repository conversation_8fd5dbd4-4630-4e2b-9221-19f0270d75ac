package tech.tiangong.butted.openai.lazada.config.properties

import org.springframework.beans.factory.InitializingBean
import org.springframework.boot.context.properties.ConfigurationProperties


/**
 * Lazada配置
 */
@ConfigurationProperties(prefix = "task.lazada")
data class LazadaProperties(
    /**
     * host
     */
    val host: String = "https://api.lazada.sg",
    /**
     * 地址
     */
    val endpoint: String = "https://api.lazada.sg/rest",
    /**
     * 应用key
     */
    val appKey: String = "129213",
    /**
     * 密钥sk，联系接口人获取
     */
    val appSecret: String = "jbaQluChoB2ZhQO4TNjKhU5sXRrUnzao",

    /**
     * 连接超时(毫秒)
     */
    val connectTimeout: Int = 15000,
    /**
     * 读取超时(毫秒)
     */
    val readTimeout: Int = 60000,

    ) : InitializingBean {
    fun buildPath(path: String): String {
        return "$host$path"
    }

    override fun afterPropertiesSet() {
        println("LazadaProperties：$this")
    }
}