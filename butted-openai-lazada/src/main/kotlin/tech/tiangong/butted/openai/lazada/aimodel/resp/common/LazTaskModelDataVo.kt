package tech.tiangong.butted.openai.lazada.aimodel.resp.common

import java.io.Serializable

/**
 * lazada模型返回报文
 */
data class LazTaskModelDataVo<T>(
    /**
     * 任务状态：
     * waiting： 等待中
     * running: 运⾏中
     * cancelled：取消中
     * fail: 失败
     * success: 成功
     */
    var status: String? = null,
    /**
     * 失败的原因（英语）
     */
    val failMessage: String? = null,
    /**
     * 响应数据
     * 成功时的数据，对于换⾐、换背景、换脸这⼏个
     * API来说，data都是List<String>，图⽚的Url
     */
    var data: T? = null,

    ) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): Boolean {
        return "success" == status
    }

    /**
     * 失败的响应
     */
    fun failed(): Boolean = !succeed()

    companion object {
        private const val serialVersionUID: Long = 1L

    }

}