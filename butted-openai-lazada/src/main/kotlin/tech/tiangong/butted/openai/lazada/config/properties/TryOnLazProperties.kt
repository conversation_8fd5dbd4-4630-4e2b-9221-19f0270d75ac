package tech.tiangong.butted.openai.lazada.config.properties

import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import org.springframework.boot.context.properties.ConfigurationProperties


/**
 * 虚拟换衣任务配置
 */
@ConfigurationProperties(prefix = "task.lazada.handle.try-on")
data class TryOnLazProperties(
    override var handleLockKey: String = "${Constant.NAMESPACE}:task:handle:lazada:try-on:",
    /**
     * 等待处理锁时间(秒)默认30秒
     */
    override var lockWait: Long = 30L,
    /**
     * 释放处理锁时间(秒)默认60秒
     */
    override var lockLease: Long = 60L,
    /**
     * 超时时间(秒)默认30分钟
     */
    override var timeout: Long = 1800L,


    ) : OpenAiTaskConfig()
