package tech.tiangong.butted.openai.lazada.aimodel.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serializable


/**
 * AI换背景
 */
data class ShiftSceneModelReq(
    /**
     * 输⼊的商品图Url
     */
    @field:NotBlank(message = "输⼊的商品图Url不能为空")
    var product_image_url: String? = null,
    /**
     * 背景图code，和算法约定好的字符串
     */
    @field:NotBlank(message = "背景图code不能为空")
    var background_code: String? = null,
    /**
     * 默认1:1
     */
    var ratio: String? = "",
    /**
     * ⽣成图⽚数量[1，4] ，默认是1
     */
    @field:NotNull(message = "生成图片数量不能为空")
    var batch_size: Int? = 1,

    ) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
