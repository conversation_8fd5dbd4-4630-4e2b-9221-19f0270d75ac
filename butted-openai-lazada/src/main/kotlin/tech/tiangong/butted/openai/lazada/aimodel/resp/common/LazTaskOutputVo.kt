package tech.tiangong.butted.openai.lazada.aimodel.resp.common

/**
 * 图片列表输出VO
 */
open class LazTaskOutputVo(
    /**
     * 任务状态：
     * waiting： 等待中
     * running: 运⾏中
     * cancelled：取消中
     * fail: 失败
     * success: 成功
     */
    var status: String? = null,

) : LazResultVo(), TaskModelOutputVo {

    override fun toString(): String {
        return "LazTaskOutputVo(status=$status, supper=${super.toString()})"
    }

    companion object {
        private const val serialVersionUID = 1L
    }
}


//fun main(args: Array<String>) {
//    val json = "[\"https://example.com/image1.jpg\",\"https://example.com/image2.jpg\"]"
//
//    val parseJson = json.parseJson(ImageListOutputVo::class.java)
//    println(parseJson)
//    parseJson.forEach {
//        println(it)
//    }
//
//}