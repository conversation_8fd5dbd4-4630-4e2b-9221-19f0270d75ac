package tech.tiangong.butted.openai.lazada.remote

import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpHeaders
import team.aikero.blade.core.enums.NetworkCode
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.enums.LogTypeEnum
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.lazada.aimodel.LazTaskModeEnum
import tech.tiangong.butted.openai.vo.req.base.BusTaskCallbackBaseReq
import tech.tiangong.butted.rest.RestApi
import tech.tiangong.butted.rest.RestApiLogFlag
import java.net.URLEncoder


@Slf4j
object LazCallbackApi {
    fun <T : SyncTask> callback(task: T, modeEnum: LazTaskModeEnum): DataResponse<*> {
        return callback(task, modeEnum, null)
    }

    fun <T : SyncTask> callback(task: T, modeEnum: LazTaskModeEnum, responseText: String?): DataResponse<*> {
        if (task.callback.isBlank()) {
            log.error { "task:{task.taskId} callback is blank" }
            return failed("", NetworkCode.SERVER_ERROR.code, "callback is null")
        }
        return callback(task, modeEnum, task.callback!!, responseText)
    }


    fun <T : SyncTask> callback(
        task: T,
        modeEnum: LazTaskModeEnum,
        callback: String,
        responseText: String?
    ): DataResponse<*> {
        try {
            RestApiLogFlag.flagTaskId(task.taskId)
            RestApiLogFlag.flagTaskMode(modeEnum.name)
            RestApiLogFlag.flagLogType(LogTypeEnum.CALLBACK_BUSINESS)
            val request = BusTaskCallbackBaseReq(
                modelType = modeEnum.name,
                busId = task.busId,
                taskId = task.taskId,
                taskStatus = task.taskStatus,
                taskProgress = task.taskProgress,
                message = task.message,
                responseText = responseText
            )
            val headers = HttpHeaders()
            headers.set("Tenant-Id", task.tenantId.toString())
            headers.set("User-Id", task.creatorId.toString())
            headers.set("User-Name", URLEncoder.encode(task.creatorName ?: "任务", Charsets.UTF_8))
            val responseType = object : ParameterizedTypeReference<DataResponse<*>>() {}
            val result = RestApi.postJson(
                url = callback,
                headers = headers,
                requestBody = request,
                responseType = responseType
            )
            return result
        } finally {
            RestApiLogFlag.clean()
        }
    }


}

