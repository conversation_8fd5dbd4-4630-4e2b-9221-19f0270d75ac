plugins {
    alias(commonLibs.plugins.google.ksp)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
}

dependencies {
    implementation(projects.buttedCommon)
    implementation(projects.buttedOpenai)
    implementation(commonLibs.hutool.core)
    implementation(commonLibs.hutool.extra)
    implementation(commonLibs.okhttp)
    implementation(commonLibs.blade.tenant.spring.boot.starter)
    implementation(commonLibs.blade.web.cloud.spring.boot.starter)
    implementation(commonLibs.blade.data.mybatis.plus.spring.boot.starter)
    implementation(commonLibs.blade.feign.spring.boot.starter)

    //implementation(files("libs/lazop-api-sdk-1.2.2.jar"))
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))


}

