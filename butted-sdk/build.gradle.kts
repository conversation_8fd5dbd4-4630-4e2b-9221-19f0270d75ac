plugins {
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
    alias(commonLibs.plugins.api.version.generator)
}

dependencies {
    api(projects.buttedCommon)
    compileOnly(commonLibs.jakarta.servlet.api)
    implementation(commonLibs.jakarta.validation.api)
    implementation(commonLibs.blade.common)
    implementation(springCloudLibs.spring.springCloudStarterOpenfeign)
    implementation(springCloudLibs.spring.springCloudOpenfeignCore)
}
