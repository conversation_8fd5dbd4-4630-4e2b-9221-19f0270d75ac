package tech.tiangong.butted.openai.picwish.aimodel.resp

import java.io.Serializable

/**
 * {
 *   "status": 200,
 *   "data": {
 *     "class_masks": {
 *       "acc": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageAcc.png?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=jBfuXfzgOForhf1E%2Bo4hgctwcnY%3D",
 *       "body": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageBody.png?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=RsSI7drRu%2BnsWzrRzQh/EK1rJyc%3D",
 *       "clothes": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageClothes.png?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=eCPgJ4uv/vhy4rv3t3Kkz6O3cto%3D",
 *       "others": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageOthers.png?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=GBeqtQKOYBWllQpMRUvMg2SJvxE%3D",
 *       "shoes": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageShoes.png?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=pcUT39EFQCgn56w%2BWI6EGB19hdA%3D"
 *     },
 *     "clothes_masks": "https://picwishzb.oss-cn-zhangjiakou.aliyuncs.com/tasks/output/visual_person_segmentation/981b11aa-5c64-4183-9251-62d6090bee10-imageAllClothes.zip?Expires=1728462687&OSSAccessKeyId=LTAI5tGjJnh66c1txANiRBQN&Signature=ln/jJ%2BPHhKoTIALSnurN9TR5X58%3D",
 *     "completed_at": 1728459087,
 *     "created_at": 1728459077,
 *     "download_time": 218,
 *     "err_info": "",
 *     "output_type": 3,
 *     "processed_at": 1728459077,
 *     "progress": 100,
 *     "return_type": 1,
 *     "state": 1,
 *     "state_detail": "Complete",
 *     "task_id": "e782a205-2d4a-40fd-8365-6e1fabf1258b",
 *     "time_elapsed": 0
 *   }
 * }
 */
class SegmentationTaskModelVo(
    /**
     * 任务ID
     */
    var task_id: String? = null,

    /**
     * 处理状态 state：
     *     -7: 无效文件（比如文件损坏、格式不对）
     *     -5: 超出大小（最大15M限制）
     *     -3: 下载失败（检查文件URL是否可用）
     *     -2: 上传失败
     *     -1: 失败
     *     1: 完成
     */
    var state: Int? = null,


    /**
     * clothes_masks，压缩包或图片格式
     */
    var clothes_masks: String? = null,

    /**
     * 进度：修复成功，值会返回100，修复失败为0
     */
    var progress: Int? = null,

    /**
     * class_masks数据
     */
    var class_masks: ClassMaskModelVo? = null,


    /**
     * 返回类型
     * 1: 仅返回同语义灰度图合并结果【默认】：相同语义合并的灰度图数据。
     * 2: 仅返回同语义全部细分灰度图：指定语义的全部灰度图将打包成一个ZIP文件返回，目前仅支持衣服
     * 3: 两者都返回
     */
    var return_type: Int? = null,

    /**
     * 任务创建时间戳
     */
    var created_at: Long? = null,
    /**
     * 任务处理时间戳
     */
    var processed_at: Long? = null,
    /**
     * 任务完成时间戳
     */
    var completed_at: Long? = null,

    ) : Serializable {

    companion object {
        private const val serialVersionUID = 1L
    }
}