package tech.tiangong.butted.openai.picwish.service.impl

import entity.SegmentationTaskMark
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.core.toolkit.decodeUrl
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.enums.SegmentationTaskMarkTypeEnum
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.entity.SegmentationTask
import tech.tiangong.butted.openai.picwish.aimodel.resp.ClassMaskModelVo
import tech.tiangong.butted.openai.picwish.aimodel.resp.SegmentationTaskModelVo
import tech.tiangong.butted.openai.picwish.remote.RestPicWishApi
import tech.tiangong.butted.openai.repository.SegmentationTaskMarkRepository
import tech.tiangong.butted.openai.repository.SegmentationTaskRepository
import tech.tiangong.butted.openai.service.SegmentationTaskMarkService
import tech.tiangong.butted.oss.AliYunOss
import tech.tiangong.butted.oss.upload
import tech.tiangong.butted.util.ZipUtil
import java.time.LocalDateTime

/**
 * 佐糖【图片分割】任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class SegmentationTaskMarkServiceImpl(
    val segmentationTaskRepository: SegmentationTaskRepository,
    val segmentationTaskMarkRepository: SegmentationTaskMarkRepository,
    val transactionalManager: TransactionalManager,
) : SegmentationTaskMarkService {

    @Async
    override fun doSegmentation(task: SegmentationTask) {
        try {
            val response = RestPicWishApi.createSegmentationTask(task.taskId!!, task.refImgUrl!!)
            task.aiEndTime = LocalDateTime.now()
            if (response.failed() || response.data == null || response.data.state != 1) {
                log.error { "segmentation task create failed: ${task.toJson()}, msg: ${response.message}" }
                task.message = response.message?.take(200)
                task.taskStatus = TaskStatusEnum.FAILED.code
                transactionalManager.exec {
                    segmentationTaskRepository.updateById(task)
                }
                return
            }
            val markList = dealResponseMask(response.data, task.taskId!!)
            //插入识别图片信息
            if (markList.isEmpty()) {
                throw ButtedException("未识别到Mark图")
            }
            transactionalManager.exec {
                segmentationTaskMarkRepository.saveBatch(markList)
                task.taskStatus = TaskStatusEnum.COMPLETED.code
                task.taskProgress = 100
                segmentationTaskRepository.updateById(task)
            }
        } catch (e: Exception) {
            log.error { "dealSegmentationTask失败：${e.stackTraceToString()}" }
            transactionalManager.exec {
                task.message = e.message!!.take(200)
                task.taskStatus = TaskStatusEnum.FAILED.code
                segmentationTaskRepository.updateById(task)
            }
        }
    }

    private fun dealResponseMask(
        modelVo: SegmentationTaskModelVo,
        taskId: Long
    ): List<SegmentationTaskMark> {
        val markList = mutableListOf<SegmentationTaskMark>()
        markList.addAll(
            generateClothesMasks(modelVo.clothes_masks, taskId)
        )
        markList.addAll(
            generateClassMasks(modelVo.class_masks, taskId)
        )
        return markList
    }

    private fun generateClothesMasks(
        clothesMaskUrl: String?,
        taskId: Long
    ): List<SegmentationTaskMark> {
        if (clothesMaskUrl.isBlank()) {
            return listOf()
        }
        val clothesUrlList = mutableListOf<String>()
        // 解码 Unicode 转义序列
        val clothesUrl = clothesMaskUrl!!.decodeUrl()
        //如果是压缩包的形式，需解析压缩包信息
        if (clothesUrl.contains(".zip")) {
            clothesUrlList.addAll(
                ZipUtil.unzipAndUpload(clothesUrl) { name, byte ->
                    AliYunOss.upload(byte, ContentType.IMAGE_PNG)
                }
            )
        } else {
            clothesUrlList.add(
                clothesUrl.upload(ContentType.IMAGE_PNG)
            )
        }
        return clothesUrlList.map {
            buildSegmentationTaskMark(
                SegmentationTaskMarkTypeEnum.CLOTHES_MASK.code,
                taskId,
                it
            )
        }
    }


    private fun generateClassMasks(
        classMaskModel: ClassMaskModelVo?,
        taskId: Long
    ): List<SegmentationTaskMark> {
        if (classMaskModel == null) {
            return listOf()
        }

        // 解码 Unicode 转义序列
        val markList = mutableListOf<SegmentationTaskMark>()
        classMaskModel.clothes?.also { clothes ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.CLOTHES.code,
                    taskId,
                    clothes.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.acc?.also { acc ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.ACC.code,
                    taskId,
                    acc.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.body?.also { body ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.BODY.code,
                    taskId,
                    body.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.face?.also { face ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.FACE.code,
                    taskId,
                    face.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.hair?.also { hair ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.HAIR.code,
                    taskId,
                    hair.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.head?.also { head ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.HEAD.code,
                    taskId,
                    head.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        classMaskModel.others?.also { others ->
            markList.add(
                buildSegmentationTaskMark(
                    SegmentationTaskMarkTypeEnum.OTHERS.code,
                    taskId,
                    others.decodeUrl().upload(ContentType.IMAGE_PNG)
                )
            )
        }
        return markList
    }

    private fun buildSegmentationTaskMark(
        maskType: Int,
        taskId: Long,
        ossUrl: String
    ): SegmentationTaskMark {
        val mark = SegmentationTaskMark()
        mark.maskId = IdHelper.getId()
        mark.taskId = taskId
        mark.maskType = maskType
        mark.tenantId = CurrentUserHolder.get().tenantId
        mark.path = ossUrl
        return mark
    }


    /* private fun uploadToOss2(sourceUrl: String, fileName: String): String {
         val url = URI.create(sourceUrl).toURL()
         val inputStream = url.openStream()
         val file = MultipartFileUtil.toMultipartFile(inputStream, "${fileName}.png", "image/png")
         return uploadToOss2(file)
     }

     private fun uploadToOss2(file: MultipartFile): String {
         val requestDto = UploaderRequestDto(data = file.inputStream.readBytes())
         val result = uploaderOssClient.upload(requestDto)
         if (result.ossUrl.isBlank()) {
             log.error { "上传图片到OSS失败: ${result.toJson()}" }
             throw ButtedException("上传图片到OSS失败: ${result.errMsg}")
         }
         return result.ossUrl!!

         *//*val uploadResponse = fileManageFeign.uploadFile(listOf(file), null)
        if (uploadResponse.code != "200" || uploadResponse.data.isEmpty()) {
            log.error { "上传图片到OSS失败: ${uploadResponse.toJson()}" }
            throw ButtedException("上传图片到OSS失败: ${uploadResponse.message}")
        }
        return uploadResponse.data.first().url*//*
    }*/

}
