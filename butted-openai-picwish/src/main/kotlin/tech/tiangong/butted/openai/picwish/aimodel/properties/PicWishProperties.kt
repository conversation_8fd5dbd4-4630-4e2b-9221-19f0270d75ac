package tech.tiangong.butted.openai.picwish.aimodel.properties

import org.springframework.boot.context.properties.ConfigurationProperties


/**
 * 佐糖配置
 */
@ConfigurationProperties(prefix = "task.picwish")
data class PicWishProperties(
    /**
     * 地址
     */
    val url: String = "https://watermark.market.alicloudapi.com",
    /**
     * appCode
     */
    val appCode: String = "83af580accbf443ab0e52952a5876adf",


    /**
     * 服装分割API地址
     */
    val segmentationUrl: String = "https://techsz.aoscdn.com/api",

    /**
     * 服装分割API X-API-KEY
     */
    val segmentationApiKey: String = "wx3plpy3lj46wb8nt",


//    /**
//     * AppKey
//     */
//    val appKey: String = "204690968",
//    /**
//     * AppSecret
//     */
//    val appSecret: String = "ZSJeynZGqqr85vPM3soMJ9D9tU1w1dsX",


)