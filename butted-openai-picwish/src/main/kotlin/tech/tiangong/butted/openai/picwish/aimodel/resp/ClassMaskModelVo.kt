package tech.tiangong.butted.openai.picwish.aimodel.resp

/**
 * 佐糖【图片分割】class_masks Vo
 *
 * <AUTHOR>
 */
data class ClassMaskModelVo(


    /**
     * acc
     */
    var acc: String? = null,


    /**
     * body
     */
    var body: String? = null,



    /**
     * clothes
     */
    var clothes: String? = null,



    /**
     * face
     */
    var face: String? = null,



    /**
     * hair
     */
    var hair: String? = null,



    /**
     * head
     */
    var head: String? = null,

    /**
     * others
     */
    var others: String? = null,

    ) {
    companion object {
        private const val serialVersionUID = 1L
    }
}
