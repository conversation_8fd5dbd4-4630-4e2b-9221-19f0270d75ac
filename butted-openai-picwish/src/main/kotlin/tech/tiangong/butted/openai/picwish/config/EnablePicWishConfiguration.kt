package tech.tiangong.butted.openai.picwish.config

import tech.tiangong.butted.openai.picwish.aimodel.properties.PicWishProperties
import tech.tiangong.butted.openai.picwish.aimodel.properties.WatermarkProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration


/**
 * 配置文件Enable
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(
    value = [
        PicWishProperties::class, WatermarkProperties::class
    ]
)
class EnablePicWishConfiguration {

}