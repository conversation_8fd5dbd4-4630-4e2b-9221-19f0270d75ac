package tech.tiangong.butted.openai.picwish.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.WatermarkTask
import tech.tiangong.butted.openai.vo.req.WatermarkTaskReq

@Slf4j
object WatermarkConvert : BaseConvert {

    fun buildWatermarkTask(req: WatermarkTaskReq): WatermarkTask {
        return WatermarkTask().apply {
            this.initBase()
            this.refImgUrl = req.refImgUrl
            this.maskUrl = req.maskUrl
        }
    }


}



