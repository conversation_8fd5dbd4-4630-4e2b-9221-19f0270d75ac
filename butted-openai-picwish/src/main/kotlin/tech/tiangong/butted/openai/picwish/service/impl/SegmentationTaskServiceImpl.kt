package tech.tiangong.butted.openai.picwish.service.impl

import cn.hutool.crypto.digest.DigestUtil
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.enums.SegmentationTaskMarkTypeEnum
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.picwish.convert.SegmentationTaskConvert
import tech.tiangong.butted.openai.repository.SegmentationTaskMarkRepository
import tech.tiangong.butted.openai.repository.SegmentationTaskRepository
import tech.tiangong.butted.openai.service.SegmentationTaskMarkService
import tech.tiangong.butted.openai.service.SegmentationTaskService
import tech.tiangong.butted.openai.vo.req.SegmentationTaskReq
import tech.tiangong.butted.openai.vo.resp.ClassMaskVo
import tech.tiangong.butted.openai.vo.resp.ClothesMaskVo
import tech.tiangong.butted.openai.vo.resp.SegmentationTaskVo
import tech.tiangong.butted.rest.RestApi
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 佐糖【图片分割】任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class SegmentationTaskServiceImpl(
    val segmentationTaskRepository: SegmentationTaskRepository,
    val segmentationTaskMarkRepository: SegmentationTaskMarkRepository,
    val transactionalManager: TransactionalManager,
    val segmentationTaskMarkService: SegmentationTaskMarkService
) : SegmentationTaskService {

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: SegmentationTaskReq): Long {
        val refImgByte: ByteArray = RestApi.noLogResBody {
            RestApi.get(req.refImgUrl!!, ByteArray::class.java)
        }
        val refImgSha256 = DigestUtil.sha256Hex(refImgByte)!!
        var task = segmentationTaskRepository.getByImageShaAndType(refImgSha256, req.taskType)
        // 状态如果是失败：重新走佐糖做分割
        if (task != null && !TaskStatusEnum.failedOrCanceled(task.taskStatus)) {
            return task.taskId!!
        }
        if (task == null) {
            task = SegmentationTaskConvert.buildSegmentationTask(req)
            task.refImgSha = refImgSha256
            segmentationTaskRepository.save(task)
        } else {
            SegmentationTaskConvert.rebuildSegmentationTask(task, req).also {
                segmentationTaskRepository.updateById(it)
            }
        }
        transactionalManager.afterCommit {
            segmentationTaskMarkService.doSegmentation(task)
        }
        return task.taskId!!
    }

    override fun detail(taskId: Long): SegmentationTaskVo {
        val task = segmentationTaskRepository.getById(taskId) ?: throw ButtedException("任务id信息不存在:${taskId}")
        val taskList = segmentationTaskMarkRepository.getByTaskId(taskId)
        val clothes = taskList.firstOrNull { it.maskType == SegmentationTaskMarkTypeEnum.CLOTHES.code }
        val clothesMaskList = taskList.filter { it.maskType == SegmentationTaskMarkTypeEnum.CLOTHES_MASK.code }
        val segmentationTaskVo = SegmentationTaskVo()
        val classMaskVo = ClassMaskVo()
        if (null != clothes) {
            classMaskVo.clothes = clothes.path
            segmentationTaskVo.classMask = classMaskVo
        }
        segmentationTaskVo.clothesMaskList = clothesMaskList.map { ClothesMaskVo(path = it.path) }
        segmentationTaskVo.taskId = task.taskId!!
        segmentationTaskVo.taskStatus = task.taskStatus
        return segmentationTaskVo
    }


}
