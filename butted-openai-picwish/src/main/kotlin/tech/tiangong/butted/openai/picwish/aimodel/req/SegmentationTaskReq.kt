package tech.tiangong.butted.openai.picwish.aimodel.req

import java.io.Serializable

/**
 * 佐糖【图片分割】Api 请求参数
 *
 * <AUTHOR>
 */
 class SegmentationTaskApiReq(

    /**
     * image_url, 图片下载地址
     *
     */
    var  image_url: String? = null,


    /**
     * sync,是否同步返回
     * 1：同步 0：异步【默认】
     * 同步需要传递1，不传默认为异步请求
     */
    var sync: Int? = null,



    /**
     * detail_mode,分割模式
     * 1: 细粒度常规【默认】
     * 2: 细粒度极高无需衣服全部细分灰度图，建议选细粒度常规
     */
    var detail_mode: Int? = null,



    /**
     * output_type,返回内容
     * 1: 仅返回同语义灰度图合并结果【默认】：相同语义合并的灰度图数据。
     * 2: 仅返回同语义全部细分灰度图：指定语义的全部灰度图将打包成一个ZIP文件返回，目前仅支持衣服
     * 3: 两者都返回：
     */
    var output_type: Int? = null,



    /**
     * quality,边缘质量
     * 1: 常规分割质量【默认】●2: 高精度质量
     */
    var quality: Int? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
