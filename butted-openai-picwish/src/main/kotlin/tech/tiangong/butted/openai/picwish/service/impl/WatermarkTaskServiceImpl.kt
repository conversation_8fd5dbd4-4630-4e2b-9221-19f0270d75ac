package tech.tiangong.butted.openai.picwish.service.impl

import cn.hutool.core.codec.Base64Decoder
import cn.hutool.crypto.digest.DigestUtil
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.openai.picwish.aimodel.properties.WatermarkProperties
import tech.tiangong.butted.openai.picwish.convert.WatermarkConvert
import tech.tiangong.butted.openai.picwish.remote.RestPicWishApi
import tech.tiangong.butted.openai.repository.WatermarkTaskRepository
import tech.tiangong.butted.openai.service.WatermarkTaskService
import tech.tiangong.butted.openai.vo.req.WatermarkTaskReq
import tech.tiangong.butted.openai.vo.resp.WatermarkTaskVo
import tech.tiangong.butted.oss.AliYunOss
import tech.tiangong.butted.rest.RestApi


/**
 * @description:
 * @author: chazz
 * @since: 2024年09月13日20:20:23
 * @version: 1.0
 **/
@Slf4j
@Service
class WatermarkTaskServiceImpl(
    val config: WatermarkProperties,
    val watermarkTaskRepository: WatermarkTaskRepository,
) : WatermarkTaskService {
    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: WatermarkTaskReq): Long {
        val refImgByte: ByteArray = RestApi.noLogResBody {
            RestApi.get(req.refImgUrl!!, ByteArray::class.java)
        }
        val markByte: ByteArray = RestApi.noLogResBody {
            RestApi.get(req.maskUrl!!, ByteArray::class.java)
        }
        val refImgSha256 = DigestUtil.sha256Hex(refImgByte)!!
        val markSha256 = DigestUtil.sha256Hex(markByte)!!
        val task = watermarkTaskRepository.getByImageSha(req.refImgUrl!!, req.maskUrl!!)
        if (task != null) {
            return task.taskId!!
        }
        return WatermarkConvert.buildWatermarkTask(req).let {
            it.startAiTime()
            val response = RestPicWishApi.createWatermarkTask(it.taskId!!, it.refImgUrl!!, it.maskUrl!!)
            if (response.failed() || response.data == null) {
                log.error { "watermark task create failed: ${it.toJson()}, msg: ${response.message}" }
                throw ButtedException("watermark task create failed: ${response.message}")
            }
            val data = response.data
            if (data.state != 1) {
                log.error { "watermark task create failed: ${it.toJson()}, state:${data.state}" }
                throw ButtedException("watermark task create failed, state:${data.state}")
            }
            it.thirdTaskId = data.task_id
            it.taskStatus = TaskStatusEnum.COMPLETED.code
            it.taskProgress = 100
            it.stopAiEndTime()
            val imageByte = Base64Decoder.decode(data.image)
            /*val file = MultipartFileUtil.toMultipartFile(imageByte, "${it.taskId}.jpg","multipart/form-data")
            val uploadResponse = fileManageFeign.uploadFile(listOf(file), null)
            if (uploadResponse.code != "200" || uploadResponse.data.isEmpty()) {
                log.error { "上传去水印图片到OSS失败: ${uploadResponse.toJson()}" }
                throw ButtedException("上传去水印图片到OSS失败: ${uploadResponse.message}")
            }
            val uploadDto = uploadResponse.data.first()*/
            it.resImgs = AliYunOss.upload(imageByte, ContentType.IMAGE_JPEG)
            it.refImgSha = refImgSha256
            it.maskSha = markSha256
            watermarkTaskRepository.save(it)
            it.taskId!!
        }
    }

    override fun detail(taskId: Long): WatermarkTaskVo? {
        val task = watermarkTaskRepository.getById(taskId)
        return task?.copy(WatermarkTaskVo::class)?.apply {
            resImg = task.resImgs?.split(Constant.COMMA)?.firstOrNull()
        }
    }


}


