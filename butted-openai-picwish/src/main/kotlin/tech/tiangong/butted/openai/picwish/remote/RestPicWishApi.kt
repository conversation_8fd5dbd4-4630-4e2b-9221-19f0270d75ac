package tech.tiangong.butted.openai.picwish.remote

import cn.hutool.extra.spring.SpringUtil
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpHeaders
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.enums.LogTypeEnum
import tech.tiangong.butted.openai.picwish.aimodel.properties.PicWishProperties
import tech.tiangong.butted.openai.picwish.aimodel.req.SegmentationTaskApiReq
import tech.tiangong.butted.openai.picwish.aimodel.resp.SegmentationTaskModelVo
import tech.tiangong.butted.openai.picwish.aimodel.resp.WatermarkModelVo
import tech.tiangong.butted.openai.picwish.aimodel.resp.common.TaskModelResponse
import tech.tiangong.butted.openai.picwish.constant.WishConstant
import tech.tiangong.butted.rest.RestApi
import tech.tiangong.butted.rest.RestApiLogFlag


@Slf4j
object RestPicWishApi {
    private val picWishProperties: PicWishProperties by lazy {
        SpringUtil.getBean(
            PicWishProperties::class.java
        )
    }

    /**
     * 创建水印任务
     *      String host = "https://watermark.market.alicloudapi.com";
     * 	    String path = "/tasks/visual/watermark";
     * 	    String method = "POST";
     * 	    String appcode = "你自己的AppCode";
     * 	    Map<String, String> headers = new HashMap<String, String>();
     * 	    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
     * 	    headers.put("Authorization", "APPCODE " + appcode);
     * 	    headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
     * 	    Map<String, String> bodys = new HashMap<String, String>();
     * 	    bodys.put("image_url", "https://qncdn.aoscdn.com/projects/picwish/img/demo/watermark.jpg");
     * 	    bodys.put("mask_url", "https://qncdn.aoscdn.com/projects/picwish/img/demo/watermark-mask.png");
     */
    fun createWatermarkTask(
        taskId: Long,
        imageUrl: String,
        maskUrl: String
    ): TaskModelResponse<WatermarkModelVo> {
        try {
            RestApiLogFlag.flagTaskId(taskId)
            RestApiLogFlag.flagTaskMode("PICWISH_WATERMARK")
            RestApiLogFlag.flagLogType(LogTypeEnum.CREATE_AI_TASK)
            val headers = HttpHeaders()
            headers.add("Authorization", "APPCODE ${picWishProperties.appCode}")
            //提交参数设置
            val request: MultiValueMap<String, String> = LinkedMultiValueMap()
            request.add("return_type", "2")
            request.add("image_url", imageUrl)
            request.add("mask_url", maskUrl)
            val responseType = object :
                ParameterizedTypeReference<TaskModelResponse<WatermarkModelVo>>() {}
            val result = RestApi.post(
                url = "${picWishProperties.url}${WishConstant.PATH_WATERMARK}",
                headers = headers,
                requestForm = request,
                responseType = responseType
            )
            return result
        } finally {
            RestApiLogFlag.clean()
        }

    }


    /**
     * 	服装分割API
     * 	    String host = "https://picwishper.market.alicloudapi.com";
     * 	    String path = "/tasks/visual/r-person-segmentation";
     * 	    String method = "POST";
     * 	    String appcode = "你自己的AppCode";
     * 	    Map<String, String> headers = new HashMap<String, String>();
     * 	    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
     * 	    headers.put("Authorization", "APPCODE " + appcode);
     * 	    //根据API的要求，定义相对应的Content-Type
     * 	    headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
     * 	    Map<String, String> querys = new HashMap<String, String>();
     * 	    Map<String, String> bodys = new HashMap<String, String>();
     * 	    bodys.put("image_url", "https://picwishszpub.oss-cn-shenzhen.aliyuncs.com/05tjemx4jzfm/test.jpg");
     * 	    bodys.put("detail_mode", "1");
     * 	    bodys.put("quality", "1");
     * 	    bodys.put("output_type", "1");
     *
     */
    fun createSegmentationTask(
        taskId: Long,
        refImgUrl: String
    ): TaskModelResponse<SegmentationTaskModelVo> {
        try {
            RestApiLogFlag.flagTaskId(taskId)
            RestApiLogFlag.flagTaskMode("PICWISH_SEGMENTATION")
            RestApiLogFlag.flagLogType(LogTypeEnum.CREATE_AI_TASK)
            val headers = HttpHeaders()
            headers.add("X-API-KEY", picWishProperties.segmentationApiKey)
            val request = SegmentationTaskApiReq().apply {
                this.image_url = refImgUrl
                this.detail_mode = 2
                this.quality = 2
                this.output_type = 3
                this.sync = 1
            }
            val responseType = object :
                ParameterizedTypeReference<TaskModelResponse<SegmentationTaskModelVo>>() {}

            return RestApi.postJson(
                url = "${picWishProperties.segmentationUrl}${WishConstant.PATH_SEGMENTATION}",
                headers = headers,
                requestBody = request,
                responseType = responseType
            )
        } finally {
            RestApiLogFlag.clean()
        }
    }

}

