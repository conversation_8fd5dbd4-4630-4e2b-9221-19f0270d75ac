package tech.tiangong.butted.openai.picwish.constant

object WishConstant {

    const val PIC_WISH_HOST: String = "https://watermark.market.alicloudapi.com"
    const val PATH_WATERMARK: String = "/tasks/visual/watermark"
    const val PATH_SEGMENTATION: String = "/tasks/visual/r-person-segmentation"
//    String host = "https://watermark.market.alicloudapi.com";
//    String path = "/tasks/visual/watermark";
//    String method = "POST";
//    String appcode = "你自己的AppCode";
//    Map<String, String> headers = new HashMap<String, String>();
//    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
//    headers.put("Authorization", "APPCODE " + appcode);
//    headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
//    Map<String, String> querys = new HashMap<String, String>();
//    Map<String, String> bodys = new HashMap<String, String>();
//    bodys.put("image_url", "https://qncdn.aoscdn.com/projects/picwish/img/demo/watermark.jpg");
//    bodys.put("mask_url", "https://qncdn.aoscdn.com/projects/picwish/img/demo/watermark-mask.png");


}