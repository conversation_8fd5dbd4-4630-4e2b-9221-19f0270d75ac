package tech.tiangong.butted.openai.picwish.aimodel.resp.common

import java.io.Serializable

/**
 * 佐糖模型返回报文
 * // 请求成功示例
 * {
 *   "status": 200,
 *   "message": "success",
 *   "data": {
 *     "task_id": "94806a3f-1173-4afb-9bcc-3ff19c0981f4",
 *     "image": "图片下载地址或者base64数据",
 *     "return_type": "2",
 *     "progress": 100,
 *     "state": 1
 *   }
 * }
 */
data class TaskModelResponse<T>(
    /**
     * 状态码:
     * 200 请求成功
     * 400 客⼾端参数传递错误，请检查参数是否缺失或值是否正确
     * 401 认证失败，请检查AppCode是否正确或者服务是否开通
     * 404 请求的URL或资源不存在，请检查URL是否正确，task id是否设置正确
     * 413 上传的⽂件超出⼤⼩限制，请检查⽂件⼤⼩，参⻅各个服务的最⼤⽂件限制
     * 429 请求频率超出QPS限制（默认QPS为2），请放缓请求速率，或者联系商务提升QPS限制
     * 500 服务端异常，请反馈给商务或技术对接⼈员
     */
    val status: Int,
    /**
     * 提示信息
     */
    val message: String? = null,
    /**
     * 响应数据
     */
    val data: T? = null,
) : Serializable {

    /**
     * 成功的响应
     */
    fun succeed(): Boolean = status == 200

    /**
     * 失败的响应
     */
    fun failed(): Boolean = status != 200

    companion object {
        private const val serialVersionUID: Long = 1L
    }

}