package tech.tiangong.butted.openai.picwish.aimodel.properties

import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import org.springframework.boot.context.properties.ConfigurationProperties


/**
 * Watermark任务配置
 */
@ConfigurationProperties(prefix = "task.picwihs.handle.watermark")
data class WatermarkProperties(
    override var handleLockKey: String = "butted:task:handle:clip:",
    /**
     * 等待处理锁时间(秒)默认30秒
     */
    override var lockWait: Long = 30L,
    /**
     * 释放处理锁时间(秒)默认60秒
     */
    override var lockLease: Long = 60L,
    /**
     * 超时时间(秒)默认2分钟
     */
    override var timeout: Long = 120,
    /**
     * 推送超时时间(秒)默认2分钟
     */
    override var pushTimeout: Long = 120,
    /**
     * 拉取任务时间间隔(秒)默认5秒拉一次
     */
    override var pullInterval: Long = 5,

    ) : OpenAiTaskConfig()
