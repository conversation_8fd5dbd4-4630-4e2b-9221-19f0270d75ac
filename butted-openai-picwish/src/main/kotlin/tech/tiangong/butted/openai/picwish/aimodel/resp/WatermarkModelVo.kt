package tech.tiangong.butted.openai.picwish.aimodel.resp

import java.io.Serializable

/**
 * 面料标签模型Vo
 * {
 *     "task_id": "94806a3f-1173-4afb-9bcc-3ff19c0981f4",
 *     "image": "图片下载地址或者base64数据",
 *     "return_type": "2",
 *     "progress": 100,
 *     "state": 1
 *   }
 *
 */
class WatermarkModelVo(
    /**
     * 任务ID
     */
    var task_id: String? = null,
    /**
     * 处理状态：
     * state<0（处理失败）
     *   ◦ -8: 修复超时，最⻓处理时间30秒
     *   ◦ -7: ⽆效图⽚⽂件（⽐如格式不对、分辨率太⼤等）
     *   ◦ -5: image_url图⽚超出⼤⼩（15MB）
     *   ◦ -3: image_url下载失败（检查⽂件URL是否可⽤）
     *   ◦ -2:上传失败
     *   ◦ -1: 修复失败
     * state=1 （完成）
     */
    var state: Int? = null,
    /**
     * 进度：修复成功，值会返回100，修复失败为0
     */
    var progress: Int? = null,
    /**
     * 返回类型
     * 1 = 返回图片的下载URL。
     * 2 = 以base64字符串形式返回图片
     */
    var return_type: Int? = null,
    /**
     * 图片下载地址或者base64数据
     */
    var image: String? = null,
    /**
     * 任务创建时间戳
     */
    var created_at: Long? = null,
    /**
     * 任务处理时间戳
     */
    var processed_at: Long? = null,
    /**
     * 任务完成时间戳
     */
    var completed_at: Long? = null,

    ) : Serializable {

    companion object {
        private const val serialVersionUID = 1L
    }
}