package tech.tiangong.butted.openai.picwish.convert

import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.SegmentationTask
import tech.tiangong.butted.openai.vo.req.SegmentationTaskReq
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import java.time.LocalDateTime

@Slf4j
object SegmentationTaskConvert : BaseConvert {
    fun buildSegmentationTask(req: SegmentationTaskReq): SegmentationTask {
        return SegmentationTask().apply {
            this.taskId = IdHelper.getId()
            this.taskStatus = TaskStatusEnum.QUEUEING.code
            this.taskType = req.taskType
            this.tenantId = CurrentUserHolder.get().tenantId
            this.refImgUrl = req.refImgUrl

            this.aiStartTime = LocalDateTime.now()
            this.sync = 1
            this.detailMode = 2
            this.quality = 2
            this.outputType = 3
            this.taskProgress = 0
        }
    }

    fun rebuildSegmentationTask(task: SegmentationTask, req: SegmentationTaskReq): SegmentationTask {
        task.taskStatus = TaskStatusEnum.QUEUEING.code
        task.createdTime = LocalDateTime.now()
        task.revisedTime = LocalDateTime.now()
        task.refImgUrl = req.refImgUrl
        task.taskProgress = 0
        return task
    }


}



