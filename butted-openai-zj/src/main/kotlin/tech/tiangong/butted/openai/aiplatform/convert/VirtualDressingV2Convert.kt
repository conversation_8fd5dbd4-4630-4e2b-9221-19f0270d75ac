package tech.tiangong.butted.openai.aiplatform.convert

import cn.hutool.json.JSONObject
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.req.VirtualDressingV2TaskReq
import tech.tiangong.butted.common.vo.VirtualDressingV2TaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.openai.aiplatform.aimodel.req.VirtualDressingV2ModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.VirtualDressingV2Vo
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.VirtualDressingV2Task
import tech.tiangong.butted.oss.upload

object VirtualDressingV2Convert: BaseConvert {
    @JvmStatic
    fun convert(task: VirtualDressingV2Task): VirtualDressingV2ModelReq = VirtualDressingV2ModelReq(
    ).apply {
        this.tryOn = VirtualDressingV2ModelReq.TryOn().apply {
            this.imageClothUrl = task.imageClothUrl
            this.imageModelUrl = task.imageModelUrl
            this.clothType = task.clothType
            this.modelBatchInfo = task.modelBatch?.parseJsonList(VirtualDressingV2TaskReq.ModelBatch::class.java)?.map{
                VirtualDressingV2ModelReq.ModelBatch().apply {
                    this.modelName = it.modelName
                    this.tryOnBatchSize = it.tryOnBatchSize
                }
            }
            this.kontext = task.kontextBatchSize?.let {
                VirtualDressingV2ModelReq.Kontext().apply {
                    this.kontextBatchSize = it
                }
            }
            this.midJourney = task.moodboardId?.let {
                VirtualDressingV2ModelReq.MidJourney().apply {
                    this.moodboardId = it
                }
            }
            this.imageClothUrl = task.imageClothUrl
        }
    }

    @JvmStatic
    fun convert(taskInfo: VirtualDressingV2TaskReq.TaskInfo): VirtualDressingV2Task = VirtualDressingV2Task()
        .apply {
            this.initBase()
            this.busId = taskInfo.busId ?: 0
            this.busCode = taskInfo.busCode ?: ""
            this.imageClothUrl = taskInfo.imageClothUrl
            this.imageModelUrl = taskInfo.imageModelUrl
            this.clothType = taskInfo.clothType
            this.modelBatch = taskInfo.modelBatchInfo?.toJson()
            this.moodboardId = taskInfo.midJourney?.moodboardId
            this.kontextBatchSize = taskInfo.kontext?.kontextBatchSize
            this.cleanPushed()
        }
    @JvmStatic
    fun toVO(task: VirtualDressingV2Task): VirtualDressingV2TaskVo {
        val vo = task.copy(VirtualDressingV2TaskVo::class)
        task.comfyuiHistoryResult?.let {
            val historyDto = it.parseJson(VirtualDressingV2Vo.VirtualDressingComfyuiHistoryDto::class.java)
            //转成阿里云oss存储 裂变的先不转
            vo.youchuanMjUrls = historyDto.youchuanMjUrls?.parseJsonList(String::class.java)
            vo.chaojiTryOnUrls = historyDto.chaojiTryOnUrls?.parseJsonList(String::class.java)
            vo.chaojiTryOnKontextUrls = historyDto.chaojiTryOnKontextUrls?.parseJsonList(String::class.java)
            vo.huiwaTryOnUrls = historyDto.huiwaTryOnUrls?.parseJsonList(String::class.java)
            vo.huiwaTryOnKontextUrls = historyDto.huiwaTryOnKontextUrls?.parseJsonList(String::class.java)
            vo.lazadaTryOnUrls = historyDto.lazadaTryOnUrls?.parseJsonList(String::class.java)
            vo.lazadaTryOnKontextUrls = historyDto.lazadaTryOnKontextUrls?.parseJsonList(String::class.java)
        }
        return vo
    }
}