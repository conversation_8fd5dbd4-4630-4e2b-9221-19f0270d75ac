package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.ImageGenTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.ImageGenTaskCreateVo
import tech.tiangong.butted.common.vo.ImageGenTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ImageGenProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.ImageGenModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.ImageGenOutputVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.ImageGenConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.ImageGenTask
import tech.tiangong.butted.openai.repository.ImageGenTaskRepository
import tech.tiangong.butted.openai.service.ImageGenTaskService
import tech.tiangong.butted.openai.vo.query.ImageGenTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * image_gen Service
 *
 * <AUTHOR>
 * @date       ：2024/11/22 14:54
 * @version    :1.0
 */
@Slf4j
@Service
class ImageGenTaskServiceImpl(
    config: ImageGenProperties,
    private val imageGenTaskRepository: ImageGenTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<ImageGenTask, ImageGenOutputVo>(
    config,
    imageGenTaskRepository
), ImageGenTaskService {
    init {
        modeEnum = AiTaskModeEnum.IMAGE_GEN_SDXL
    }

    override fun batchCreate(req: CompanyUserBatchReq<ImageGenTaskReq>): List<ImageGenTaskCreateVo> {
        val check = req.data.filterNot { it.check() }.size
        if (check > 0) {
            throw TaskException("参数非法", modeEnum)
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val data = req.data.map {
                ImageGenConvert.convert(it)
                    .apply {
                        this.callback = req.callback
                    }
            }
            batchCreateHandle.create(
                data,
                { imageGenTaskRepository.saveBatch(data, data.size) },
                { doPush(it) }) {
                imageGenTaskRepository.updateBatchById(data, data.size)
            }
            data.map { ImageGenTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<ImageGenTaskVo> {
        val data = this.imageGenTaskRepository.listByIds(ids)
        return if (data.isNotEmpty()) {
            data.map { ImageGenConvert.toVO(it) }
        } else {
            listOf()
        }
    }

    override fun listByBusIds(ids: List<Long>): List<ImageGenTaskVo> {
        val data = this.imageGenTaskRepository.listByBusIds(ids)
        return if (data.isNotEmpty()) {
            data.map { ImageGenConvert.toVO(it) }
        } else {
            listOf()
        }
    }

    override fun buildTaskModelParams(task: ImageGenTask): ImageGenModelReq =
        ImageGenConvert.convert(task)

    override fun handlePullResult(
        task: ImageGenTask,
        changeStateEnum: TaskStatusEnum,
        output: ImageGenOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }


    override fun handleTimeoutResult(task: ImageGenTask, canceled: Boolean) {
        log.info { "image_gen超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(
        taskAction: TaskActionEnum,
        curPageNum: Int
    ): IPage<out BaseTaskVo> {
        return ImageGenTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                imageGenTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    override fun doSync(task: ImageGenTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    private fun manualUpdateAiTaskInfo(task: ImageGenTask) {
        task.refreshRevisedTime()
        withTranExec {
            imageGenTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }
}