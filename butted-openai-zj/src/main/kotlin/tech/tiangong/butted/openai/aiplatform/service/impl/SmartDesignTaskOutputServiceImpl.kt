package tech.tiangong.butted.openai.aiplatform.service.impl

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.TypeReference
import com.alibaba.fastjson2.parseObject
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.VectorTaskTestReq
import tech.tiangong.butted.common.req.VectorTaskTestVo
import tech.tiangong.butted.common.vo.ResGroupRepairImgVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.enums.FaceRepairEnum
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.event.FaceRepairTaskEvent
import tech.tiangong.butted.event.LimbRepairTaskEvent
import tech.tiangong.butted.event.RepairStateEvent
import tech.tiangong.butted.event.publish.OpenaiTaskEventPublisher
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.convert.SmartDesignConvert
import tech.tiangong.butted.openai.entity.FaceRepairTask
import tech.tiangong.butted.openai.entity.SmartDesignTask
import tech.tiangong.butted.openai.entity.SmartDesignTaskOutput
import tech.tiangong.butted.openai.repository.SmartDesignTaskOutputRepository
import tech.tiangong.butted.openai.repository.SmartDesignTaskRepository
import tech.tiangong.butted.openai.service.FaceRepairTaskService
import tech.tiangong.butted.openai.service.LimbRepairTaskService
import tech.tiangong.butted.openai.service.SmartDesignTaskOutputService
import tech.tiangong.butted.openai.vo.query.SmartDesignTaskQuery
import tech.tiangong.butted.openai.vo.req.SmartDesignTaskOutputReq
import tech.tiangong.butted.redis.RedisTemplateHelper
import tech.tiangong.butted.redis.RedissonHelper
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicInteger

/**
 * 智能设计开款生成图服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class SmartDesignTaskOutputServiceImpl(
    val smartDesignTaskRepository: SmartDesignTaskRepository,
    val smartDesignTaskOutputRepository: SmartDesignTaskOutputRepository,
    val faceRepairTaskService: FaceRepairTaskService,
    val transactionalManager: TransactionalManager,
    val limbRepairTaskService: LimbRepairTaskService,
    val redis: RedisTemplateHelper
) : SmartDesignTaskOutputService {

    @Transactional(rollbackFor = [Exception::class])
    override fun manualSave(req: SmartDesignTaskOutputReq) {
        RedissonHelper.lockExec("SmartDesignTaskOutput", req.smartDesignTaskId) {
            val outputs = smartDesignTaskOutputRepository.listByTaskIdAndGroupNum(req.smartDesignTaskId)
            if (outputs.isNotEmpty() || req.resImgGroupList.isEmpty()) {
                return@lockExec
            }
            val groupList = SmartDesignConvert.buildTaskOutputGroupList(req)
            redis.setValue("SmartDesignTaskOutput-req:" + req.smartDesignTaskId, groupList.toJson(), 1800)
            groupList.forEach { groups ->
                tryRepairLimb(groups, false)
            }
            smartDesignTaskOutputRepository.saveBatchManualFill(groupList.flatten())

        }

    }

    override fun findByTaskId(smartDesignTaskId: Long?): List<SmartDesignTaskOutput> {
        return smartDesignTaskOutputRepository.listByTaskId(smartDesignTaskId)
    }


    override fun findTaskResGroupRepairImgList(taskId: Long): List<ResGroupRepairImgVo> {
        return smartDesignTaskOutputRepository.listByTaskId(taskId).let {
            SmartDesignConvert.packResGroupRepairImgVo(it)
        }
    }

    override fun findComposeTaskResGroupRepairImgList(taskIdList: List<Long>): List<ResGroupRepairImgVo> {
        if (taskIdList.isEmpty()) {
            return listOf()
        }
        val pictureList = smartDesignTaskOutputRepository.listByTaskIds(taskIdList)
        val pictureGroup = pictureList.groupBy { it.taskId }
        val mergeList = mutableListOf<ResGroupRepairImgVo>()
        val primaryId = taskIdList.first()
        val incGroupNum = AtomicInteger(0)
        pictureGroup[primaryId]?.also { pl ->
            with(SmartDesignConvert.packResGroupRepairImgVo(pl)) {
                this.forEach {
                    it.groupNum = incGroupNum.incrementAndGet()
                    mergeList.add(it)
                }
            }
        }
        pictureGroup.forEach { (taskId, pl) ->
            if (taskId != primaryId) {
                with(SmartDesignConvert.packResGroupRepairImgVo(pl)) {
                    this.forEach {
                        it.groupNum = incGroupNum.incrementAndGet()
                        mergeList.add(it)
                    }
                }
            }
        }
        return mergeList

    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenLimbRepairTaskEvent(event: LimbRepairTaskEvent) {
        val task = event.getTask()
        if (!SourceEnum.fromASmartDesign(task.source)) {
            return
        }
        RedissonHelper.lockExec("LimbRepairTask", task.taskId) {
            val statusEnum = event.taskStatus
            //按照批次
            val groups = smartDesignTaskOutputRepository.listByBatchId(task.sourceId!!)
            if (groups.isEmpty()) {
                return@lockExec
            }
            val repairEnum = when (statusEnum) {
                TaskStatusEnum.QUEUEING, TaskStatusEnum.GENERATING -> {
                    FaceRepairEnum.REPAIRING
                }

                TaskStatusEnum.COMPLETED -> {
                    completedFillLimbRepair(groups, task)
                }

                else -> {
                    FaceRepairEnum.FAILED
                }
            }
            groups.forEach { op ->
                if (repairEnum != FaceRepairEnum.COMPLETED) {
                    op.message = task.message
                }
                op.repairState = repairEnum.code
                op.revisedTime = LocalDateTime.now()
            }
            //val taskRedis=redis.getValue("SmartDesignTaskServiceImpl-SmartDesignTask:" + groups.first().taskId)
            val redisTask = smartDesignTaskRepository.getById(groups.first().taskId)
            //val redisTask = JSON.parseObject(taskRedis, object : TypeReference<SmartDesignTask>() {})
            println("=======================================taskRedis:"+redisTask.needFaceRepair())
            if (redisTask.needFaceRepair()) {
                if (repairEnum == FaceRepairEnum.COMPLETED) {
                    log.info { "listenLimbRepairTaskEvent task img: ${task.resImgs}" }
                    val resImgsArray = task.resImgs?.split(",") ?: emptyList()
                    groups.forEachIndexed { index, group ->
                        if (index < resImgsArray.size) {
                            group.resImg = resImgsArray[index]
                        } else {
                            // 处理索引超出范围的情况，例如设置默认值或跳过
                            group.resImg = null // 或者设置一个默认值
                        }
                    }
                    tryRepair(groups, false)
                } else if (repairEnum == FaceRepairEnum.FAILED) {
                    val v = redis.getValue("SmartDesignTaskOutput-req:" + groups.first().taskId)
                    log.info { "Retrieved JSON string: $v" }
                    val groupList = JSON.parseObject(v, object : TypeReference<List<List<SmartDesignTaskOutput>>>() {})
                    val g = groupList.flatMap { it.filter { it.batchId == task.sourceId } }
                    tryRepair(g, false)
                }
            } else {
                transactionalManager.exec {
                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                    transactionalManager.afterCommit {
                        checkLimbRepairProgress(groups.first().taskId!!, true)
                    }
                }
            }
        }

    }


    fun testTryRepair(req: VectorTaskTestReq) : VectorTaskTestVo {
        // 记录批次ID与任务ID的映射关系
        val batchTaskMap = mutableMapOf<Long, MutableSet<Long>>()
        // 记录已调用过testR的批次ID（避免重复调用）
        val invokedBatchIds = mutableSetOf<Long>()

        if (req.batchId != null) {
            // 情况1：直接传入batchId
            val batchId = req.batchId!!
            // 查询该批次对应的所有任务ID
            val outputs = smartDesignTaskOutputRepository.listByBatchId(batchId)
            val taskIds = outputs.mapNotNull { it.taskId }.toMutableSet()
            batchTaskMap[batchId] = taskIds

            // 调用testR
            testR(batchId)
            invokedBatchIds.add(batchId)
        } else {
            // 情况2：传入taskIds列表
            req.taskIds.forEach { taskId ->
                val outs = smartDesignTaskOutputRepository.listByTaskId(taskId)
                outs.forEach { output ->
                    val batchId = output.batchId ?: return@forEach

                    // 构建批次ID与任务ID的映射关系
                    val taskSet = batchTaskMap.getOrPut(batchId) { mutableSetOf() }
                    taskSet.add(taskId)

                    // 如果该批次ID还没调用过testR，则调用
                    if (batchId !in invokedBatchIds) {
                        testR(batchId)
                        invokedBatchIds.add(batchId)
                    }
                }
            }
        }
        return VectorTaskTestVo(
            invokedBatchIds = invokedBatchIds.toSet(),
            batchTaskMap = batchTaskMap.mapValues { it.value.toSet() }
        )

    }


    fun testR(batchId : Long){
        //按照批次
        val groups = smartDesignTaskOutputRepository.listByBatchId(batchId)
        log.info { "groups的信息 == " + groups.toJson() }
        val repair = limbRepairTaskService.getBySourceId(batchId)
        log.info { "repair的信息 == " + groups.toJson() }
        if (repair == null) {
            return
        }
        val images = repair.resImgs?.split(",") ?: emptyList()
        groups.forEachIndexed { index, group ->
            if (index < images.size) {
                group.resImg = images[index]
            } else {
                // 处理索引超出范围的情况，例如设置默认值或跳过
                group.resImg = null // 或者设置一个默认值
            }
        }
        log.info { "groups修改之后的信息 == " + groups.toJson() }
        tryRepair(groups, false)
    }


    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenFaceRepairTaskEvent(event: FaceRepairTaskEvent) {
        val task = event.getTask()
        if (!SourceEnum.fromASmartDesign(task.source)) {
            return
        }
        RedissonHelper.lockExec("FaceRepairTask", task.taskId) {
            val statusEnum = event.taskStatus
            val groups = smartDesignTaskOutputRepository.listByBatchId(task.sourceId!!)
            if (groups.isEmpty()) {
                return@lockExec
            }
            val repairEnum = when (statusEnum) {
                TaskStatusEnum.QUEUEING, TaskStatusEnum.GENERATING -> {
                    FaceRepairEnum.REPAIRING
                }

                TaskStatusEnum.COMPLETED -> {
                    completedFillRepair(groups, task)
                }

                else -> {
                    FaceRepairEnum.FAILED
                }
            }

            groups.forEach { op ->
                if (repairEnum != FaceRepairEnum.COMPLETED) {
                    op.message = task.message
                }
                op.repairState = repairEnum.code
                op.revisedTime = LocalDateTime.now()
            }
            transactionalManager.exec {
                smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                transactionalManager.afterCommit {
                    checkRepairProgress(groups.first().taskId!!, true)
                }
            }
        }
    }

    override fun checkRepairProgress(smartDesignTaskId: Long): FaceRepairEnum? {
        return checkRepairProgress(smartDesignTaskId, false)
    }

    private fun checkLimbRepairProgress(smartDesignTaskId: Long, publishEvent: Boolean): FaceRepairEnum? {
        return RedissonHelper.lockExec("SmartDesignTaskOutput", smartDesignTaskId) {
            val outputs = smartDesignTaskOutputRepository.listByTaskIdAndGroupNum(smartDesignTaskId)
            if (outputs.isEmpty() || !outputs.first().needFaceRepair()) {
                return@lockExec null
            }
            val groupRepairMap = outputs.groupBy { it.groupNum }
            val repairing = AtomicInteger()
            val completed = AtomicInteger()
            val failed = AtomicInteger()
            val total = groupRepairMap.size
            groupRepairMap.forEach { (_, gl) ->
                val repairState = groupRepairState(gl)
                when (repairState) {
                    FaceRepairEnum.YES -> {
                        if (tryRepair(gl, true) == FaceRepairEnum.FAILED) {
                            failed.incrementAndGet()
                        } else {
                            repairing.incrementAndGet()
                        }
                    }

                    FaceRepairEnum.REPAIRING -> {
                        //拉取修复任务
                        val repairTask = limbRepairTaskService.getBySourceId(gl.first().batchId!!)
                        if (repairTask == null) {
                            repairing.incrementAndGet()
                        } else {
                            val taskStatus = repairTask.taskStatus
                            if (TaskStatusEnum.processing(taskStatus)) {
                                repairing.incrementAndGet()
                            } else if (TaskStatusEnum.completed(taskStatus)) {
                                when (completedFillLimbRepair(gl, repairTask)) {
                                    FaceRepairEnum.COMPLETED -> {
                                        completed.incrementAndGet()
                                    }

                                    else -> {
                                        failed.incrementAndGet()
                                    }
                                }
                                transactionalManager.exec {
                                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(gl)
                                }
                            } else {//失败
                                failed.incrementAndGet()
                                transactionalManager.exec {
                                    failedFillLimbRepair(gl, repairTask)
                                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(gl)
                                }
                            }
                        }
                    }

                    FaceRepairEnum.COMPLETED -> {
                        completed.incrementAndGet()
                    }

                    else -> {
                        failed.incrementAndGet()
                    }
                }
            }

            val repairEnum = when (total) {
                completed.get() -> {
                    FaceRepairEnum.COMPLETED
                }

                failed.get() -> {
                    FaceRepairEnum.FAILED
                }

                (completed.get() + failed.get()) -> {
                    FaceRepairEnum.COMPLETED_PARTIAL
                }

                repairing.get() -> {
                    FaceRepairEnum.REPAIRING
                }

                else -> {
                    FaceRepairEnum.REPAIRING_PART
                }
            }
            if (publishEvent) {
                OpenaiTaskEventPublisher.publish(
                    RepairStateEvent(
                        taskId = smartDesignTaskId,
                        sourceEnum = SourceEnum.A_SMART_DESIGN,
                        repairEnum = repairEnum
                    )
                )
            }
            repairEnum
        }


    }

    private fun checkRepairProgress(smartDesignTaskId: Long, publishEvent: Boolean): FaceRepairEnum? {
        return RedissonHelper.lockExec("SmartDesignTaskOutput", smartDesignTaskId) {
            val outputs = smartDesignTaskOutputRepository.listByTaskIdAndGroupNum(smartDesignTaskId)
            if (outputs.isEmpty() || !outputs.first().needFaceRepair()) {
                return@lockExec null
            }
            val groupRepairMap = outputs.groupBy { it.groupNum }
            val repairing = AtomicInteger()
            val completed = AtomicInteger()
            val failed = AtomicInteger()
            val total = groupRepairMap.size
            groupRepairMap.forEach { (_, gl) ->
                val repairState = groupRepairState(gl)
                when (repairState) {
                    FaceRepairEnum.YES -> {
                        if (tryRepair(gl, true) == FaceRepairEnum.FAILED) {
                            failed.incrementAndGet()
                        } else {
                            repairing.incrementAndGet()
                        }
                    }

                    FaceRepairEnum.REPAIRING -> {
                        //拉取修复任务
                        val repairTask = faceRepairTaskService.getBySourceId(gl.first().batchId!!)
                        if (repairTask == null) {
                            repairing.incrementAndGet()
                        } else {
                            val taskStatus = repairTask.taskStatus
                            if (TaskStatusEnum.processing(taskStatus)) {
                                repairing.incrementAndGet()
                            } else if (TaskStatusEnum.completed(taskStatus)) {
                                when (completedFillRepair(gl, repairTask)) {
                                    FaceRepairEnum.COMPLETED -> {
                                        completed.incrementAndGet()
                                    }

                                    else -> {
                                        failed.incrementAndGet()
                                    }
                                }
                                transactionalManager.exec {
                                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(gl)
                                }
                            } else {//失败
                                failed.incrementAndGet()
                                transactionalManager.exec {
                                    failedFillRepair(gl, repairTask)
                                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(gl)
                                }
                            }
                        }
                    }

                    FaceRepairEnum.COMPLETED -> {
                        completed.incrementAndGet()
                    }

                    else -> {
                        failed.incrementAndGet()
                    }
                }
            }

            val repairEnum = when (total) {
                completed.get() -> {
                    FaceRepairEnum.COMPLETED
                }

                failed.get() -> {
                    FaceRepairEnum.FAILED
                }

                (completed.get() + failed.get()) -> {
                    FaceRepairEnum.COMPLETED_PARTIAL
                }

                repairing.get() -> {
                    FaceRepairEnum.REPAIRING
                }

                else -> {
                    FaceRepairEnum.REPAIRING_PART
                }
            }
            if (publishEvent) {
                OpenaiTaskEventPublisher.publish(
                    RepairStateEvent(
                        taskId = smartDesignTaskId,
                        sourceEnum = SourceEnum.A_SMART_DESIGN,
                        repairEnum = repairEnum
                    )
                )
            }
            repairEnum
        }


    }

    private fun completedFillLimbRepair(
        groups: List<SmartDesignTaskOutput>,
        repairTask: tech.tiangong.butted.openai.entity.LimbRepairTask
    ): FaceRepairEnum {
        val groupOutputList = groups.sortedBy { it.serialNum ?: 0 }
        val resImgList = repairTask.resImgs?.split(Constant.COMMA) ?: listOf()
        val repairEnum = if (resImgList.size == groupOutputList.size) {
            FaceRepairEnum.COMPLETED
        } else {
            FaceRepairEnum.FAILED
        }
        groupOutputList.forEachIndexed { index, o ->
            o.message = repairTask.message
            if (repairEnum == FaceRepairEnum.COMPLETED) {
                o.repairImg = resImgList[index]
            } else {
                o.message = "修复返回图片数量不一致"
            }
            o.repairState = repairEnum.code
        }
        return repairEnum
    }

    private fun completedFillRepair(groups: List<SmartDesignTaskOutput>, repairTask: FaceRepairTask): FaceRepairEnum {
        val groupOutputList = groups.sortedBy { it.serialNum ?: 0 }
        val resImgList = repairTask.resImgs?.split(Constant.COMMA) ?: listOf()
        val repairEnum = if (resImgList.size == groupOutputList.size) {
            FaceRepairEnum.COMPLETED
        } else {
            FaceRepairEnum.FAILED
        }
        groupOutputList.forEachIndexed { index, o ->
            o.message = repairTask.message
            if (repairEnum == FaceRepairEnum.COMPLETED) {
                o.repairImg = resImgList[index]
            } else {
                o.message = "修复返回图片数量不一致"
            }
            o.repairState = repairEnum.code
        }
        return repairEnum
    }

    private fun failedFillRepair(groups: List<SmartDesignTaskOutput>, repairTask: FaceRepairTask) {
        groups.forEach { o ->
            o.message = repairTask.message
            o.repairState = FaceRepairEnum.FAILED.code
        }
    }

    private fun failedFillLimbRepair(
        groups: List<SmartDesignTaskOutput>,
        repairTask: tech.tiangong.butted.openai.entity.LimbRepairTask
    ) {
        groups.forEach { o ->
            o.message = repairTask.message
            o.repairState = FaceRepairEnum.FAILED.code
        }
    }


    private fun groupRepairState(groups: List<SmartDesignTaskOutput>): FaceRepairEnum {
        val output = groups.first()
        return when (output.repairState) {
            FaceRepairEnum.YES.code -> FaceRepairEnum.YES
            FaceRepairEnum.REPAIRING.code -> FaceRepairEnum.REPAIRING
            FaceRepairEnum.COMPLETED.code -> FaceRepairEnum.COMPLETED
            FaceRepairEnum.FAILED.code -> FaceRepairEnum.FAILED
            else -> throw RuntimeException("修复状态错误[${output.repairState}]")
        }

    }

    private fun tryRepairLimb(groups: List<SmartDesignTaskOutput>, doUpdate: Boolean): FaceRepairEnum {
        try {
            val result = limbRepairTaskService.manualCreate(SmartDesignConvert.packLimbRepairTaskReq(groups))
            if (result.successful()) {
                return FaceRepairEnum.REPAIRING
            }
            groups.forEach { o ->
                o.repairState = FaceRepairEnum.FAILED.code
                o.message = "修手脚任务失败：${result.message}"
            }
            if (doUpdate) {
                transactionalManager.exec {
                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                }
            }
            return FaceRepairEnum.FAILED
        } catch (e: Exception) {
            log.error { "limb repair task create error:${e.stackTraceToString()}" }
            groups.forEach {
                it.repairState = FaceRepairEnum.FAILED.code
                it.message = "创建修手脚任务失败"
            }
            if (doUpdate) {
                transactionalManager.exec {
                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                }
            }
            return FaceRepairEnum.FAILED
        }
    }


    private fun tryRepair(groups: List<SmartDesignTaskOutput>, doUpdate: Boolean): FaceRepairEnum {
        try {
            val result = faceRepairTaskService.manualCreate(SmartDesignConvert.packFaceRepairTaskReq(groups))
            if (result.successful()) {
                return FaceRepairEnum.REPAIRING
            }
            groups.forEach { o ->
                o.repairState = FaceRepairEnum.FAILED.code
                o.message = "修脸任务失败：${result.message}"
            }
            if (doUpdate) {
                transactionalManager.exec {
                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                }
            }
            return FaceRepairEnum.FAILED
        } catch (e: Exception) {
            log.error { "face repair task create error:${e.stackTraceToString()}" }
            groups.forEach {
                it.repairState = FaceRepairEnum.FAILED.code
                it.message = "创建修脸任务失败"
            }
            if (doUpdate) {
                transactionalManager.exec {
                    smartDesignTaskOutputRepository.updateBatchByIdManualFill(groups)
                }
            }
            return FaceRepairEnum.FAILED
        }
    }

    override fun initResImg(smartDesignTaskId: Long?): String {
        val query = SmartDesignTaskQuery().apply {
            this.taskId = smartDesignTaskId
            this.taskStatus = TaskStatusEnum.COMPLETED.code
        }
        val taskList = smartDesignTaskRepository.listEntityByQuery(query)
        taskList.forEach {
            try {
                transactionalManager.exec {
                    val resImgList = it.resImgs?.split(Constant.COMMA) ?: listOf()
                    val resImgGroupList = resImgList.chunked(4)
                    SmartDesignConvert.packSmartDesignTaskOutputReq(it, resImgGroupList).also {
                        manualSave(it)
                    }
                }
            } catch (e: Exception) {
                log.error { "同步[${it.taskId}]失败：${it.resImgs} \n ${e.stackTraceToString()}" }
            }
        }
        return "同步[${taskList.size}]条"

    }


}

