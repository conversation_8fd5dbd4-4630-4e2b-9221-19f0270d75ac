package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.FlowerPatternExtractTaskReq
import tech.tiangong.butted.common.vo.FlowerPatternExtractTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.FlowerPatternExtractTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.FlowerPatternExtractProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternExtractModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FlowerPatternExtractOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FlowerPatternExtractConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.FlowerPatternExtractTask
import tech.tiangong.butted.openai.repository.FlowerPatternExtractTaskRepository
import tech.tiangong.butted.openai.service.FlowerPatternExtractTaskService
import tech.tiangong.butted.openai.vo.query.FlowerPatternExtractTaskQuery

/**
 * 花型提取任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class FlowerPatternExtractTaskServiceImpl(
    config: FlowerPatternExtractProperties,
    val flowerPatternExtractTaskRepository: FlowerPatternExtractTaskRepository
) : TaskSyncSupportImpl<FlowerPatternExtractTask, FlowerPatternExtractOutputVo>(
    config,
    flowerPatternExtractTaskRepository
), FlowerPatternExtractTaskService {
    init {
        modeEnum = AiTaskModeEnum.FLOWER_PATTERN_EXTRACT
    }

    override fun detail(taskId: Long): FlowerPatternExtractTaskVo? {
        val task = flowerPatternExtractTaskRepository.getById(taskId)
        return task?.copy(FlowerPatternExtractTaskVo::class)?.apply {
            resImgList = task.resImgs?.split(Constant.COMMA)
            //oriPatchList = task.oriPatches?.split(Constant.COMMA)
            calculateDuration()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: FlowerPatternExtractTaskReq): Long {
        val task = FlowerPatternExtractConvert.buildFlowerPatternExtractTask(req)
        flowerPatternExtractTaskRepository.save(task)
        doPush(task)
        notifyTaskQueue(task)
        return task.taskId!!
    }

    override fun getByBusId(busId: Long): FlowerPatternExtractTaskVo? {
        val task = flowerPatternExtractTaskRepository.getByBusId(busId)
        return task.copy(FlowerPatternExtractTaskVo::class)?.apply {
            resImgList = task.resImgs?.split(Constant.COMMA)
            //oriPatchList = task.oriPatches?.split(Constant.COMMA)
            calculateDuration()
            if (TaskStatusEnum.failedOrCanceled(task.taskStatus)) {
                this.failTaskMode = AiTaskModeEnum.FLOWER_PATTERN_EXTRACT.fullMode()
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        flowerPatternExtractTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
                notifyTaskQueue(it)
            }
        }
    }

    override fun buildTaskModelParams(task: FlowerPatternExtractTask): FlowerPatternExtractModelReq {
        return FlowerPatternExtractConvert.packFlowerPatternExtractModelReq(task)
    }

    override fun handlePullResult(
        task: FlowerPatternExtractTask,
        changeStateEnum: TaskStatusEnum,
        output: FlowerPatternExtractOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
                //task.oriPatches = it.oriPatches?.joinToStr()
            }
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: FlowerPatternExtractTask, canceled: Boolean) {
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun doSync(task: FlowerPatternExtractTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return FlowerPatternExtractTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                flowerPatternExtractTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: FlowerPatternExtractTask) {
        task.refreshRevisedTime()
        withTranExec {
            flowerPatternExtractTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
            notifyTaskQueue(task)
        }
    }


    fun notifyTaskQueue(task: FlowerPatternExtractTask) {
        try {
            afterPublishEvent(FlowerPatternExtractTaskEvent(task, TaskStatusEnum.of(task.taskStatus!!)))
        } catch (e: Exception) {
            log.error { "NotifyTaskQueue error: ${e.message}" }
        }
    }


}
