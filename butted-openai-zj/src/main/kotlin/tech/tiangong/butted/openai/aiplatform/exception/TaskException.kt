package tech.tiangong.butted.openai.aiplatform.exception

import team.aikero.blade.core.exception.BusinessException
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.enums.AiTaskModeEnum

/**
 * TaskException
 */
open class TaskException(msg: String, val modeEnum: AiTaskModeEnum?, val taskStatus: TaskStatusEnum? = null) :
    BusinessException(msg) {

    companion object {
        private const val serialVersionUID: Long = 1L
    }
}