package tech.tiangong.butted.openai.aiplatform.service.base

import org.springframework.scheduling.annotation.Async
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelOutputVo
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository
import java.util.concurrent.Future

/**
 * TaskSyncSupportImpl
 */
@Slf4j
abstract class TaskSyncSupportImpl<T : SyncTask, O : TaskModelOutputVo>(
    config: OpenAiTaskConfig,
    manualBaseRepository: ManualBaseRepository<*, T>
) : TaskBaseSupportImpl<T, O>(
    config,
    manualBaseRepository
), ZjTaskSyncSupport<T> {
    @Async
    override fun sync(): Future<Int> {
        return batchProcessing(TaskActionEnum.SYNC) {
            val syncCount = loopDoHandle("循环同步", TaskActionEnum.SYNC) {
                handleSync(it)
            }
            syncCount
        }
    }

    override fun handleSync(taskId: Long) {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查是否同步给业务状态了
                doSync(task)
            }
        } catch (e: Exception) {
            log.error { "${modeEnum.name}任务同步失败：${e.stackTraceToString()}" }
        }
    }

    override fun userSuspendTask(task: T) {
        lockExec(task.taskId) {
            invokeCancelAiTask(task.taskId!!)
            task.synced().suspend()
            manualBaseRepository.updateById(task)
        }
    }

    /**
     * 事务提交后回调业务
     */
    open fun callbackBusiness(task: T) {
        super.callbackBusiness(task, modeEnum)
    }

    /**
     * 事务提交后回调业务
     */
    open fun checkCallbackBusiness(task: T) {
        afterCommitExec {
            if (Bool.YES.code == task.syncStatus) {
                return@afterCommitExec
            }
            if (task.notExistCallback() || !needAsync(task)) {
                task.synced()
                manualBaseRepository.updateByIdManualFill(task)
                return@afterCommitExec
            }
            super.doCallbackBusiness(task, modeEnum)
        }

    }

    /**
     * 事务提交后回调业务
     */
    open fun callbackAigcBusiness(task: T, aigcTaskType: String) {
        super.callbackAigcBusiness(task, modeEnum, aigcTaskType)
    }

    override fun handleSyncResponse(task: T, aiModeEnum: AiTaskModeEnum, response: DataResponse<*>) {
        super.handleSyncResponse(task, aiModeEnum, response)
        manualBaseRepository.updateByIdManualFill(task)
    }


}


