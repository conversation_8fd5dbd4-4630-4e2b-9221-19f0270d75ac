package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.CroppingTaskReq
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.CroppingTaskVo
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.CroppingProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.CroppingModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.CroppingOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.CroppingConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.CroppingTask
import tech.tiangong.butted.openai.repository.CroppingTaskRepository
import tech.tiangong.butted.openai.service.CroppingTaskService
import tech.tiangong.butted.openai.vo.query.CroppingTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * 裁剪任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class CroppingTaskServiceImpl(
    config: CroppingProperties,
    val croppingTaskRepository: CroppingTaskRepository
) : TaskSyncSupportImpl<CroppingTask, CroppingOutputVo>(
    config,
    croppingTaskRepository
), CroppingTaskService {
    init {
        modeEnum = AiTaskModeEnum.CROPPING
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: CroppingTaskReq): Long {
        return CroppingConvert.build(req).let {
            croppingTaskRepository.save(it)
            doPush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: CroppingTaskReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }

    override fun buildTaskModelParams(task: CroppingTask): CroppingModelReq {
        return CroppingConvert.packCroppingModelReq(task)
    }


    override fun getByTaskId(taskId: Long): CroppingTaskVo {
        val task = croppingTaskRepository.obtainById(taskId, "任务不存在")
        return CroppingConvert.packCroppingTaskVo(task)
    }

    override fun getByBusId(busId: Long): CroppingTaskVo {
        return croppingTaskRepository.getByBusId(busId).let {
            CroppingConvert.packCroppingTaskVo(it)
        }
    }

    override fun handlePullResult(
        task: CroppingTask,
        changeStateEnum: TaskStatusEnum,
        output: CroppingOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.getGenerateImageList().joinToStr()
            }
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleException(task: CroppingTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task)
    }


    override fun handleTimeoutResult(task: CroppingTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return CroppingTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                croppingTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        croppingTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    override fun doSync(task: CroppingTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = croppingTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步裁剪任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }


    fun manualUpdateAiTaskInfo(task: CroppingTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (task.notExistCallback()) {
            task.synced()
        }
        withTranExec {
            croppingTaskRepository.updateByIdManualFill(task)
            event?.also {
                afterCommitExec {
                    publishEvent(it)
                }
            }
            checkCallbackBusiness(task)
        }
    }

}
