package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.LogoGenTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.LogoGenTaskCreateVo
import tech.tiangong.butted.common.vo.LogoGenTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.LogoGenProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.LogoGenModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.LogoGenVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.LogoGenConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.LogoGenTask
import tech.tiangong.butted.openai.repository.LogoGenTaskRepository
import tech.tiangong.butted.openai.service.LogoGenTaskService
import tech.tiangong.butted.openai.vo.query.LogoGenTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * LogoGenTaskService
 *
 * <AUTHOR>
 * @date       ：2025/4/1 18:51
 * @version    :1.0
 */
@Slf4j
@Service
class LogoGenTaskServiceImpl(
    config: LogoGenProperties,
    private val transactionalManager: TransactionalManager,
    private val logoGenTaskRepository: LogoGenTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<LogoGenTask, LogoGenVo>(
    config,
    logoGenTaskRepository
), LogoGenTaskService {
    override fun buildTaskModelParams(task: LogoGenTask): LogoGenModelReq = LogoGenConvert.convert(task)

    override fun handlePullResult(
        task: LogoGenTask,
        changeStateEnum: TaskStatusEnum,
        output: LogoGenVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: LogoGenTask, canceled: Boolean) {
        log.info { "logo_gen任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> =
        LogoGenTaskQuery()
            .let {
                if (it.adaptActionQuery(taskAction)) {
                    it.pageNum = curPageNum
                    logoGenTaskRepository.findPage(it)
                } else {
                    emptyPage()
                }
            }

    override fun doSync(task: LogoGenTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: CompanyUserBatchReq<LogoGenTaskReq>): List<LogoGenTaskCreateVo> {
        val data = this.logoGenTaskRepository.listByBusIds(req.data.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { LogoGenTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val list = req.data.map {
                LogoGenConvert.convert(it).apply {
                    this.callback = req.callback
                }
            }
            batchCreateHandle.create(
                list,
                { logoGenTaskRepository.saveBatch(list, list.size) },
                { doPush(it) }) {
                logoGenTaskRepository.updateBatchById(list, list.size)
            }
            list.map { LogoGenTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<LogoGenTaskVo> =
        task2VO(this.logoGenTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<LogoGenTaskVo> =
        task2VO(this.logoGenTaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<LogoGenTask>) = if (data.isNotEmpty()) {
        data.map { LogoGenConvert.toVO(it) }
    } else {
        listOf()
    }

    private fun manualUpdateAiTaskInfo(task: LogoGenTask) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            logoGenTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    init {
        modeEnum = AiTaskModeEnum.LOGO_GEN
    }
}