package tech.tiangong.butted.openai.aiplatform.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.butted.openai.aiplatform.component.TopFamilyNames
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FabricRecommendSku
import tech.tiangong.butted.openai.entity.FabricRecommendTask

@Slf4j
object FabricRecommendSkuConvert : BaseConvert {

    fun buildFabricRecommendSku(
        task: FabricRecommendTask,
        topFamilyNames: TopFamilyNames
    ): FabricRecommendSku {
        return FabricRecommendSku().apply {
            this.recommendSkuId = IdHelper.getId()
            this.recommendTaskId = task.taskId
            if (topFamilyNames.selectedSku != null) {
                val selectedSku = topFamilyNames.selectedSku!!
                this.sourceCommodityId = selectedSku.sourceCommodityId
                this.commodityId = selectedSku.commodityId
                this.commodityCode = selectedSku.commodityCode
                this.commodityName = selectedSku.commodityName
                this.commodityPicture = selectedSku.taoTianPictureList?.firstOrNull()?.pictureUrl
                this.colorPicture = selectedSku.colorPictureList?.firstOrNull()?.pictureUrl
                this.skuId = selectedSku.skuId
                this.skuCode = selectedSku.skuCode
                this.colorCode = selectedSku.colorCode
                this.rgb = selectedSku.rgb
                this.familyFabricCategory = topFamilyNames.selectedFamilyName
            } else {
                this.commodityName = null
                this.familyFabricCategory = topFamilyNames.handledFamilyNames.firstOrNull()
            }
            this.initBaseUserContent(task.tenantId!!, task.creatorId!!, task.creatorName)
        }
    }

}

