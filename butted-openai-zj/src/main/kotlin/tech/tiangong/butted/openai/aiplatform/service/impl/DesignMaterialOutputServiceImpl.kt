package tech.tiangong.butted.openai.aiplatform.service.impl

import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.enums.FaceRepairEnum
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.event.FaceRepairTaskEvent
import tech.tiangong.butted.event.RepairStateEvent
import tech.tiangong.butted.event.publish.OpenaiTaskEventPublisher
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.convert.DesignMaterialConvert
import tech.tiangong.butted.openai.entity.DesignMaterialOutput
import tech.tiangong.butted.openai.entity.FaceRepairTask
import tech.tiangong.butted.openai.repository.DesignMaterialOutputRepository
import tech.tiangong.butted.openai.repository.DesignMaterialTaskRepository
import tech.tiangong.butted.openai.service.DesignMaterialOutputService
import tech.tiangong.butted.openai.service.FaceRepairTaskService
import tech.tiangong.butted.openai.vo.req.DesignMaterialOutputReq
import tech.tiangong.butted.redis.RedissonHelper
import java.util.concurrent.atomic.AtomicInteger


/**
 * AI素材生成图服务
 * 与SmartDesignTaskOutputServiceImpl很多重复代码，待优化
 * <AUTHOR>
 */
@Slf4j
@Service
class DesignMaterialOutputServiceImpl(
    val designMaterialOutputRepository: DesignMaterialOutputRepository,
    val designMaterialTaskRepository: DesignMaterialTaskRepository,
    val faceRepairTaskService: FaceRepairTaskService,
) : DesignMaterialOutputService {


    @Transactional(rollbackFor = [Exception::class])
    override fun manualSave(req: DesignMaterialOutputReq) {
        RedissonHelper.lockExec("DesignMaterialOutput", req.taskId) {
            val outputs = designMaterialOutputRepository.listByTaskId(req.taskId)
            if (outputs.isNotEmpty() || req.resImgGroupList.isEmpty()) {
                return@lockExec
            }
            val groupList = DesignMaterialConvert.buildOutputGroupList(req)
            if (req.doFaceRepair) {
                groupList.forEach { groups ->
                    tryRepair(groups, false)
                }
            }
            designMaterialOutputRepository.saveBatch(groupList.flatten())
        }

    }

    private fun tryRepair(groups: List<DesignMaterialOutput>, doUpdate: Boolean): FaceRepairEnum {
        try {
            val task = designMaterialTaskRepository.obtainById(groups.first().taskId!!)
            val result = faceRepairTaskService.manualCreate(DesignMaterialConvert.packFaceRepairTaskReq(groups, task))
            if (result.successful()) {
                return FaceRepairEnum.REPAIRING
            }
            groups.forEach { o ->
                o.repairState = FaceRepairEnum.FAILED.code
                o.message = "修脸任务失败：${result.message}"
            }
            if (doUpdate) {
                withTranExec {
                    designMaterialOutputRepository.updateBatchById(groups)
                }
            }
            return FaceRepairEnum.FAILED
        } catch (e: Exception) {
            log.error { "face repair task create error:${e.stackTraceToString()}" }
            groups.forEach {
                it.repairState = FaceRepairEnum.FAILED.code
                it.message = "创建修脸任务失败"
            }
            if (doUpdate) {
                withTranExec {
                    designMaterialOutputRepository.updateBatchById(groups)
                }
            }
            return FaceRepairEnum.FAILED
        }
    }


    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenFaceRepairTaskEvent(event: FaceRepairTaskEvent) {
        val task = event.getTask()
        if (!SourceEnum.fromADesignMaterial(task.source)) {
            return
        }
        RedissonHelper.lockExec("FaceRepairTask", task.taskId) {
            val statusEnum = event.taskStatus
            val groups = designMaterialOutputRepository.listByBatchId(task.sourceId!!)
            if (groups.isEmpty()) {
                return@lockExec
            }
            val repairEnum = when (statusEnum) {
                TaskStatusEnum.QUEUEING, TaskStatusEnum.GENERATING -> {
                    FaceRepairEnum.REPAIRING
                }

                TaskStatusEnum.COMPLETED -> {
                    completedFillRepair(groups, task)
                }

                else -> {
                    FaceRepairEnum.FAILED
                }
            }

            groups.forEach { op ->
                if (repairEnum != FaceRepairEnum.COMPLETED) {
                    op.message = task.message
                }
                op.repairState = repairEnum.code
            }
            withTranExec {
                designMaterialOutputRepository.updateBatchById(groups)
                afterCommitExec {
                    checkRepairProgress(groups.first().taskId!!, true)
                }
            }
        }
    }

    override fun checkRepairProgress(designMaterialTaskId: Long): FaceRepairEnum? {
        return checkRepairProgress(designMaterialTaskId, false)
    }

    private fun checkRepairProgress(designMaterialTaskId: Long, publishEvent: Boolean): FaceRepairEnum? {
        return RedissonHelper.lockExec("DesignMaterialOutput", designMaterialTaskId) {
            val outputs = designMaterialOutputRepository.listByTaskId(designMaterialTaskId)
            if (outputs.isEmpty() || !outputs.first().needFaceRepair()) {
                return@lockExec null
            }
            val groupRepairMap = outputs.groupBy { it.groupNum }
            val repairing = AtomicInteger()
            val completed = AtomicInteger()
            val failed = AtomicInteger()
            val total = groupRepairMap.size
            groupRepairMap.forEach { (_, gl) ->
                val repairState = groupRepairState(gl)
                when (repairState) {
                    FaceRepairEnum.YES -> {
                        if (tryRepair(gl, true) == FaceRepairEnum.FAILED) {
                            failed.incrementAndGet()
                        } else {
                            repairing.incrementAndGet()
                        }
                    }

                    FaceRepairEnum.REPAIRING -> {
                        //拉取修复任务
                        val repairTask = faceRepairTaskService.getBySourceId(gl.first().batchId!!)
                        if (repairTask == null) {
                            repairing.incrementAndGet()
                        } else {
                            val taskStatus = repairTask.taskStatus
                            if (TaskStatusEnum.processing(taskStatus)) {
                                repairing.incrementAndGet()
                            } else if (TaskStatusEnum.completed(taskStatus)) {
                                when (completedFillRepair(gl, repairTask)) {
                                    FaceRepairEnum.COMPLETED -> {
                                        completed.incrementAndGet()
                                    }

                                    else -> {
                                        failed.incrementAndGet()
                                    }
                                }
                                withTranExec {
                                    designMaterialOutputRepository.updateBatchById(gl)
                                }
                            } else {//失败
                                failed.incrementAndGet()
                                withTranExec {
                                    failedFillRepair(gl, repairTask)
                                    designMaterialOutputRepository.updateBatchById(gl)
                                }
                            }
                        }
                    }

                    FaceRepairEnum.COMPLETED -> {
                        completed.incrementAndGet()
                    }

                    else -> {
                        failed.incrementAndGet()
                    }
                }
            }

            val repairEnum = when (total) {
                completed.get() -> {
                    FaceRepairEnum.COMPLETED
                }

                failed.get() -> {
                    FaceRepairEnum.FAILED
                }

                (completed.get() + failed.get()) -> {
                    FaceRepairEnum.COMPLETED_PARTIAL
                }

                repairing.get() -> {
                    FaceRepairEnum.REPAIRING
                }

                else -> {
                    FaceRepairEnum.REPAIRING_PART
                }
            }
            if (publishEvent) {
                OpenaiTaskEventPublisher.publish(
                    RepairStateEvent(taskId = designMaterialTaskId, sourceEnum = SourceEnum.A_DESIGN_MATERIAL, repairEnum = repairEnum)
                )
            }
            repairEnum
        }


    }


    private fun completedFillRepair(groups: List<DesignMaterialOutput>, repairTask: FaceRepairTask): FaceRepairEnum {
        val groupOutputList = groups.sortedBy { it.serialNum ?: 0 }
        val resImgList = repairTask.resImgs?.split(Constant.COMMA) ?: listOf()
        val repairEnum = if (resImgList.size == groupOutputList.size) {
            FaceRepairEnum.COMPLETED
        } else {
            FaceRepairEnum.FAILED
        }
        groupOutputList.forEachIndexed { index, o ->
            o.message = repairTask.message
            if (repairEnum == FaceRepairEnum.COMPLETED) {
                o.repairImg = resImgList[index]
            } else {
                o.message = "修复返回图片数量不一致"
            }
            o.repairState = repairEnum.code
        }
        return repairEnum
    }

    private fun failedFillRepair(groups: List<DesignMaterialOutput>, repairTask: FaceRepairTask) {
        groups.forEach { o ->
            o.message = repairTask.message
            o.repairState = FaceRepairEnum.FAILED.code
        }
    }


    private fun groupRepairState(groups: List<DesignMaterialOutput>): FaceRepairEnum {
        val output = groups.first()
        return when (output.repairState) {
            FaceRepairEnum.YES.code -> FaceRepairEnum.YES
            FaceRepairEnum.REPAIRING.code -> FaceRepairEnum.REPAIRING
            FaceRepairEnum.COMPLETED.code -> FaceRepairEnum.COMPLETED
            FaceRepairEnum.FAILED.code -> FaceRepairEnum.FAILED
            else -> throw RuntimeException("修复状态错误[${output.repairState}]")
        }

    }


}
