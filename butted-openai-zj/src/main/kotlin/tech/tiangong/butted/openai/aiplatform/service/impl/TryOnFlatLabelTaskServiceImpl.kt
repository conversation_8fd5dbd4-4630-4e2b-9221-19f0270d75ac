package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.TryOnFlatLabelTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ClipLabelProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.TryOnFlatLabelOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.TryOnFlatLabelConvert
import tech.tiangong.butted.openai.aiplatform.remote.FmClientApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.TryOnFlatLabelTask
import tech.tiangong.butted.openai.repository.TryOnFlatLabelTaskRepository
import tech.tiangong.butted.openai.service.TryOnFlatLabelTaskService
import tech.tiangong.butted.openai.vo.query.TryOnFlatLabelTaskQuery
import tech.tiangong.butted.openai.vo.req.TryOnFlatLabelTaskReq
import tech.tiangong.butted.openai.vo.resp.TryOnFlatLabelTaskVo

/**
 * TryOn标签任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TryOnFlatLabelTaskServiceImpl(
    config: ClipLabelProperties,
    val tryOnFlatLabelTaskRepository: TryOnFlatLabelTaskRepository
) : TaskBaseSupportImpl<TryOnFlatLabelTask, TryOnFlatLabelOutputVo>(
    config,
    tryOnFlatLabelTaskRepository
), TryOnFlatLabelTaskService {
    init {
        modeEnum = AiTaskModeEnum.TRYON_FLAT_LABEL
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: TryOnFlatLabelTaskReq): Long {
        return TryOnFlatLabelConvert.buildTryOnFlatLabelTask(req).let {
            tryOnFlatLabelTaskRepository.saveManualFill(it)
            doPush(it)
        }

    }


    override fun detail(taskId: Long): TryOnFlatLabelTaskVo? {
        val task = tryOnFlatLabelTaskRepository.getById(taskId)
        return TryOnFlatLabelConvert.packTryOnFlatLabelTaskVo(task)
    }

    override fun getLastByParentTaskId(parentTaskId: Long): TryOnFlatLabelTaskVo? {
        val task = tryOnFlatLabelTaskRepository.getLastByParentTaskId(parentTaskId)
        return TryOnFlatLabelConvert.packTryOnFlatLabelTaskVo(task)
    }


    override fun buildTaskModelParams(task: TryOnFlatLabelTask): Any {
        return TryOnFlatLabelConvert.packTryOnFlatLabelModelReq(task)
    }

    override fun handlePullResult(
        task: TryOnFlatLabelTask,
        changeStateEnum: TaskStatusEnum,
        output: TryOnFlatLabelOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.predLabels = it.predLabels?.toJson()
                val predLabelDtoList = it.toPredLabelDtoList()
                val analysisClipLabel = TryOnFlatLabelConvert.analysisClipLabel(predLabelDtoList)
                task.category = analysisClipLabel.category
                if (task.category.isNotBlank()) {
                    try {
                        val fmLabelVo = FmClientApi.obtainLabelInfoByName(task.category!!)
                        task.categoryCode = fmLabelVo.categoryCode
                        task.extendActions = fmLabelVo.extendLabel
                        task.usableLabels =
                            TryOnFlatLabelConvert.checkAndFilterPredLabelJsonStr(
                                predLabelDtoList,
                                fmLabelVo.styleLabels,
                                fmLabelVo.themeLabels
                            )
                    } catch (e: Exception) {
                        handleException(task, e.message)
                        return
                    }
                }
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, TryOnFlatLabelTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: TryOnFlatLabelTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, TryOnFlatLabelTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }


    override fun handleException(task: TryOnFlatLabelTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task, TryOnFlatLabelTaskEvent(task, failStatus))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return TryOnFlatLabelTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                tryOnFlatLabelTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: TryOnFlatLabelTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            tryOnFlatLabelTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
