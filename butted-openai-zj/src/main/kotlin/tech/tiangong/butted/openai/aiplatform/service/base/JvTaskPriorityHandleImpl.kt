package tech.tiangong.butted.openai.aiplatform.service.base

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.AiTaskPriorityProperties
import tech.tiangong.butted.openai.entity.base.BaseTask

/**
 * @description:
 * @author: chazz
 * @since: 2025年03月20日17:01:56
 * @version: 1.0
 **/
@Slf4j
@Component
class JvTaskPriorityHandleImpl(
    val properties: AiTaskPriorityProperties
) : JvTaskPriorityHandle {
    override fun <T : BaseTask> modifyTaskPriority(task: T, taskPriority: Int?): Int? {
        properties.list.forEach {
            if (it.userId == task.creatorId) {
                log.info { "user[${task.creatorId}] modify taskPriority[${it.priority}]" }
                return it.priority
            }
        }
        return taskPriority

    }
}