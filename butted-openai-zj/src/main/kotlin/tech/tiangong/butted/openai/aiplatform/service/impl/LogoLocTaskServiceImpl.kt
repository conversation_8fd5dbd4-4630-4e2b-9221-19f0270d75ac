package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.LogoLocTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.LogoLocTaskCreateVo
import tech.tiangong.butted.common.vo.LogoLocTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.LogoLocProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.LogoLocModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.LogoLocVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.LogoLocConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.LogoLocTask
import tech.tiangong.butted.openai.repository.LogoLocTaskRepository
import tech.tiangong.butted.openai.service.LogoLocTaskService
import tech.tiangong.butted.openai.vo.query.LogoLocTaskQuery
import tech.tiangong.butted.util.UserInvoke
import java.math.BigDecimal

/**
 * 区域定位服务
 *
 * <AUTHOR>
 * @date       ：2025/1/7 10:06
 * @version    :1.0
 */
@Slf4j
@Service
class LogoLocTaskServiceImpl(
    config: LogoLocProperties,
    private val logoLocTaskRepository: LogoLocTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<LogoLocTask, LogoLocVo>(
    config,
    logoLocTaskRepository
), LogoLocTaskService {


    override fun batchCreate(req: CompanyUserBatchReq<LogoLocTaskReq>): List<LogoLocTaskCreateVo> {
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val data = req.data.map {
                LogoLocConvert.convert(it)
                    .apply {
                        this.callback = req.callback
                    }
            }
            batchCreateHandle.create(
                data,
                { logoLocTaskRepository.saveBatch(data, data.size) },
                { doPush(it) }) {
                logoLocTaskRepository.updateBatchById(data, data.size)
            }
            data.map { LogoLocTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<LogoLocTaskVo> =
        task2VO(this.logoLocTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<LogoLocTaskVo> =
        task2VO(this.logoLocTaskRepository.listByBusIds(ids))

    override fun doSync(task: LogoLocTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun handleTimeoutResult(task: LogoLocTask, canceled: Boolean) {
        log.info { "区域定位超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> = LogoLocTaskQuery()
        .let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                logoLocTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }


    override fun buildTaskModelParams(task: LogoLocTask): LogoLocModelReq = LogoLocConvert.convert(task)


    override fun handlePullResult(
        task: LogoLocTask,
        changeStateEnum: TaskStatusEnum,
        output: LogoLocVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                val mappingResult = it.mappingResult
                val customRegionResult = it.customRegionResult
                if (null != customRegionResult) {
                    task.customRegionResult = customRegionResult.toJson()
                    task.maskRegion = customRegionResult.logoRegion.ifEmpty { null } ?.toJson()
                    task.resImgs = customRegionResult.logoMaskUrl.ifEmpty { null } ?.joinToStr()
                }
                if (null != mappingResult) {
                    task.logoRegion = mappingResult.toJson()
                    if (task.maskRegion == null) {
                        task.maskRegion = mappingResult.logoRegion.toJson()
                    }
                    if (task.resImgs == null) {
                        task.resImgs = mappingResult.logoMaskUrl.joinToStr()
                    }
                }
                val tiltAngle = it.tiltAngle
                if (tiltAngle != null) {
                    task.rotationAngle = BigDecimal.valueOf(tiltAngle)
                }
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }


    override fun suspendByBusIds(req: CompanyUserBatchReq<Long>): Boolean {
        val lists = this.logoLocTaskRepository.listByBusIds(req.data)
        if (CollectionUtil.isEmpty(lists)) {
            return true
        }
        UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            lists.filter { TaskStatusEnum.processing(it.taskStatus) }.forEach {
                userSuspendTask(it)
            }
        }
        return true
    }

    private fun manualUpdateAiTaskInfo(task: LogoLocTask) {
        task.refreshRevisedTime()
        withTranExec {
            logoLocTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    private fun task2VO(data: List<LogoLocTask>) = if (data.isNotEmpty()) {
        data.map { LogoLocConvert.toVO(it) }
    } else {
        listOf()
    }

    init {
        modeEnum = AiTaskModeEnum.LOGO_LOC
    }
}