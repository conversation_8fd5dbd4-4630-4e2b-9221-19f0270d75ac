package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.QwenTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.QwenTaskCreateVo
import tech.tiangong.butted.common.vo.QwenTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.QwenProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.QwenModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.QwenOutputVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.QwenConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.QwenTask
import tech.tiangong.butted.openai.repository.QwenTaskRepository
import tech.tiangong.butted.openai.service.QwenTaskService
import tech.tiangong.butted.openai.vo.query.QwenTaskQuery
import tech.tiangong.butted.openai.vo.req.QwenTaskCreateReq
import tech.tiangong.butted.util.UserInvoke

/**
 * 通义千问Service
 *
 * <AUTHOR>
 * @date       ：2024/12/26 17:19
 * @version    :1.0
 */
@Slf4j
@Service
class QwenTaskServiceImpl(
    config: QwenProperties,
    private val qwenTaskRepository: QwenTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<QwenTask, QwenOutputVo>(
    config,
    qwenTaskRepository
), QwenTaskService {
    override fun batchCreate(req: CompanyUserBatchReq<QwenTaskReq>): List<QwenTaskCreateVo> {
        val check = req.data.filterNot { it.check() }.size
        if (check > 0) {
            throw TaskException("参数非法", modeEnum)
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val data = req.data.map {
                QwenConvert.convert(it)
                    .apply {
                        this.callback = req.callback
                    }
            }
            batchCreateHandle.create(
                data,
                { qwenTaskRepository.saveBatch(data, data.size) },
                { doPush(it) }) {
                qwenTaskRepository.updateBatchById(data, data.size)
            }
            data.map { QwenTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: QwenTaskCreateReq): Long {
        val task = QwenConvert.convert(QwenTaskReq(refImgUrl = req.imgUrl ?: ""))
        this.qwenTaskRepository.save(task)
        doPush(task)
        return task.taskId ?: 0
    }

    override fun listByIds(ids: List<Long>): List<QwenTaskVo> =
        task2VO(this.qwenTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<QwenTaskVo> =
        task2VO(this.qwenTaskRepository.listByBusIds(ids))

    override fun detail(id: Long): QwenTaskVo =
        this.listByIds(listOf(id)).firstOrNull() ?: throw TaskException("【$id】任务信息不存在", modeEnum)

    override fun doSync(task: QwenTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun buildTaskModelParams(task: QwenTask): QwenModelReq = QwenConvert.convert(task)

    override fun handlePullResult(
        task: QwenTask,
        changeStateEnum: TaskStatusEnum,
        output: QwenOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.responseText = it.responseText?.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: QwenTask, canceled: Boolean) {
        log.info { "qwen超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(
        taskAction: TaskActionEnum,
        curPageNum: Int
    ): IPage<out BaseTaskVo> = QwenTaskQuery().let {
        if (it.adaptActionQuery(taskAction)) {
            it.pageNum = curPageNum
            qwenTaskRepository.findPage(it)
        } else {
            emptyPage()
        }
    }

    fun manualUpdateAiTaskInfo(task: QwenTask) {
        task.refreshRevisedTime()
        withTranExec {
            qwenTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    private fun task2VO(data: List<QwenTask>) = if (data.isNotEmpty()) {
        data.map { QwenConvert.toVO(it) }
    } else {
        listOf()
    }

    init {
        modeEnum = AiTaskModeEnum.QWEN
    }
}