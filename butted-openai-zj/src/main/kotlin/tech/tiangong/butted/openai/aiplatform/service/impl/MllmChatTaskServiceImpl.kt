package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.MllmChatTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.MllmChatTaskCreateVo
import tech.tiangong.butted.common.vo.MllmChatTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.MllmChatProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.MllmChatModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.MllmChatVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.MllmChatConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.MllmChatTask
import tech.tiangong.butted.openai.repository.MllmChatTaskRepository
import tech.tiangong.butted.openai.service.MllmChatTaskService
import tech.tiangong.butted.openai.vo.query.MllmChatTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * MllmChatTaskService
 *
 * <AUTHOR>
 * @date       ：2025/4/1 15:35
 * @version    :1.0
 */
@Slf4j
@Service
class MllmChatTaskServiceImpl(
    config: MllmChatProperties,
    private val transactionalManager: TransactionalManager,
    private val mllmChatTaskRepository: MllmChatTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<MllmChatTask, MllmChatVo>(
    config,
    mllmChatTaskRepository
), MllmChatTaskService {
    override fun buildTaskModelParams(task: MllmChatTask): MllmChatModelReq =
        MllmChatConvert.convert(task)

    override fun handlePullResult(
        task: MllmChatTask,
        changeStateEnum: TaskStatusEnum,
        output: MllmChatVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                task.responseText = it.responseText.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: MllmChatTask, canceled: Boolean) {
        log.info { "mllm_chat任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> =
        MllmChatTaskQuery()
            .let {
                if (it.adaptActionQuery(taskAction)) {
                    it.pageNum = curPageNum
                    mllmChatTaskRepository.findPage(it)
                } else {
                    emptyPage()
                }
            }

    override fun doSync(task: MllmChatTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: CompanyUserBatchReq<MllmChatTaskReq>): List<MllmChatTaskCreateVo> {
        val data = this.mllmChatTaskRepository.listByBusIds(req.data.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { MllmChatTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val list = req.data.map {
                MllmChatConvert.convert(it).apply {
                    this.callback = req.callback
                }
            }
            batchCreateHandle.create(
                list,
                { mllmChatTaskRepository.saveBatch(list, list.size) },
                { doPush(it) }) {
                mllmChatTaskRepository.updateBatchById(list, list.size)
            }
            list.map { MllmChatTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<MllmChatTaskVo> =
        task2VO(this.mllmChatTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<MllmChatTaskVo> =
        task2VO(this.mllmChatTaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<MllmChatTask>) = if (data.isNotEmpty()) {
        data.map { MllmChatConvert.toVO(it) }
    } else {
        listOf()
    }

    private fun manualUpdateAiTaskInfo(task: MllmChatTask) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            mllmChatTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    override fun stopLoopHandlePull(taskId: Long, handleCount: Int): Boolean {
        if (handleCount > 10) {
            val task = mllmChatTaskRepository.obtainById(taskId)
            return task.taskStatus == TaskStatusEnum.QUEUEING.code
        }
        return super.stopLoopHandlePull(taskId, handleCount)
    }

    init {
        modeEnum = AiTaskModeEnum.MLLM_CHAT
    }
}