package tech.tiangong.butted.openai.aiplatform.component

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.entity.base.SyncTask

/**
 * 批量创建
 *
 * <AUTHOR>
 * @date       ：2025/5/16 14:57
 * @version    :1.0
 */
@Slf4j
@Component
class BatchCreateHandle {
    fun <T : SyncTask> create(
        data: List<T>,
        create: (List<T>) -> Unit,
        push: (T) -> Unit,
        update: (List<T>) -> Unit
    ) {
        withTranExec {
            create(data)
        }
        data.forEach {
            try {
                push(it)
                it.firstPushed()
            } catch (e: Exception) {
                log.error { e }
                it.pushFailed()
                it.message = e.message
                it.taskStatus = TaskStatusEnum.FAILED.code
            }
            it.waitSync()
        }
        withTranExec {
            update(data)
        }
    }
}