package tech.tiangong.butted.openai.aiplatform.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.openai.aiplatform.aimodel.req.HighDefinitionModelReq
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.HighDefinitionTask
import tech.tiangong.butted.openai.vo.req.HighDefinitionTaskReq
import tech.tiangong.butted.oss.toFullImgPath

@Slf4j
object HighDefinitionConvert : BaseConvert {

    fun build(req: HighDefinitionTaskReq): HighDefinitionTask {
        return HighDefinitionTask().apply {
            this.initBase()
            this.busId = req.originTaskId
            this.taskMode = req.taskMode
            this.pictureId = req.pictureId
            this.pictureUrl = req.pictureUrl.toFullImgPath()
        }
    }

    fun packHighDefinitionModelReq(task: HighDefinitionTask): HighDefinitionModelReq {
        return HighDefinitionModelReq(
            taskId = task.taskId,
            refImgUrl = task.pictureUrl,
            //scale = 4
        )
    }
}

