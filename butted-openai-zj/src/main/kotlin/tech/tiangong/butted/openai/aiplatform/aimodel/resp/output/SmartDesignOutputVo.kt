package tech.tiangong.butted.openai.aiplatform.aimodel.resp.output

import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelOutputVo


/**
 * 智能开款模型Vo
 */
class SmartDesignOutputVo(
    var resImgs: List<List<String>>? = listOf(),
    /**
     * 生图提示词
     */
    var prompts: String? = null,
    /**
     * 模型版本
     */
    var modelVersion: String? = null,
) : TaskModelOutputVo() {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

}
