package tech.tiangong.butted.openai.aiplatform.convert

import tech.tiangong.butted.common.dto.RectangleCoordinateDto
import tech.tiangong.butted.common.req.FlowerPatternExtractTaskReq
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternExtractModelReq
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FlowerPatternExtractTask
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson

@Slf4j
object FlowerPatternExtractConvert : BaseConvert {

    fun buildFlowerPatternExtractTask(req: FlowerPatternExtractTaskReq): FlowerPatternExtractTask {
        return FlowerPatternExtractTask().apply {
            this.taskId = req.busId
            this.busId = req.busId
            this.busCode = req.busCode
            this.initBase()
            this.refImgUrl = req.refImgUrl.toFullImgPath()
            this.extractRegion = req.extractRegion!!
            this.genCount = req.count
            this.enableSearch = Bool.NO.code
            this.taskAttribute = req.taskAttribute
        }
    }

    fun packFlowerPatternExtractModelReq(task: FlowerPatternExtractTask): FlowerPatternExtractModelReq {
        return FlowerPatternExtractModelReq(
            taskId = task.taskId,
            refImgUrl = task.refImgUrl,
            count = task.genCount,
            enableSearch = task.enableSearch,
            //提取区域：upper_body-上半身；lower_body-下半身；full_cloth-全身
            region = when(task.extractRegion){
                1-> "upper_body"
                2-> "lower_body"
                3-> "full_cloth"
                else -> {
                    throw IllegalArgumentException("extractRegion is not valid")
                }
            }
        )
    }

}

