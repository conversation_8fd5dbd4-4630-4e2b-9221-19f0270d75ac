package tech.tiangong.butted.openai.aiplatform.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.req.BatchRecommendFabricReq
import tech.tiangong.butted.common.req.ReRecommendFabricReq
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.enums.StyleTypeEnum
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FabricRecommendModelReq
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FabricRecommendTask
import tech.tiangong.butted.openai.entity.SmartDesignTask
import tech.tiangong.butted.openai.vo.req.FabricRecommendTaskReq

@Slf4j
object FabricRecommendConvert : BaseConvert {

    fun build(req: FabricRecommendTaskReq): FabricRecommendTask {
        return FabricRecommendTask().apply {
            this.taskId = req.busId
            this.busId = req.busId
            this.busCode = req.busCode
            this.parentTaskId = req.parentTaskId
            this.source = req.sourceEnum.fullSource()
            this.initBase()
            this.inputImage = req.inputImage.toFullImgPath()
            this.category = req.category
            this.flowered = req.flowered ?: StyleTypeEnum.PURE_COLOR.code
            this.taskAttribute = req.taskAttribute
            this.callback = req.callback
        }
    }

    /**
     * 重新推荐面料
     */
    fun build(smartDesignTask: SmartDesignTask, req: ReRecommendFabricReq, source: SourceEnum): FabricRecommendTask {
        return FabricRecommendTask().apply {
            this.taskId = req.busId
            this.busId = req.busId
            this.busCode = req.busCode
            this.parentTaskId = smartDesignTask.taskId
            this.source = source.fullSource()
            this.initBase()
            this.inputImage = smartDesignTask.refImgUrl.toFullImgPath()
            this.category = smartDesignTask.category
            this.flowered = smartDesignTask.styleType ?: StyleTypeEnum.PURE_COLOR.code
            this.taskAttribute = req.taskAttribute
            this.callback = req.callback
        }
    }

    fun batchBuild(
        smartDesignTask: SmartDesignTask,
        req: BatchRecommendFabricReq,
        source: SourceEnum
    ): List<FabricRecommendTask> {
        return req.recommendList!!.map {
            FabricRecommendTask().apply {
                this.taskId = it.busId
                this.busId = it.busId
                this.busCode = it.busCode
                this.parentTaskId = smartDesignTask.taskId
                this.source = source.fullSource()
                this.initBase()
                this.inputImage = it.inputImage.toFullImgPath()
                this.category = smartDesignTask.category
                this.flowered = smartDesignTask.styleType ?: StyleTypeEnum.PURE_COLOR.code
                this.taskAttribute = req.taskAttribute
                this.callback = req.callback
            }
        }
    }

    fun manualBatchBuild(
        smartDesignTask: SmartDesignTask,
        req: BatchRecommendFabricReq,
        source: SourceEnum
    ): List<FabricRecommendTask> {
        return req.recommendList!!.map {
            FabricRecommendTask().apply {
                this.taskId = it.busId
                this.busId = it.busId
                this.busCode = it.busCode
                this.parentTaskId = smartDesignTask.taskId
                this.source = source.fullSource()
                this.manualInitBaseTask(req)
                this.inputImage = it.inputImage.toFullImgPath()
                this.category = smartDesignTask.category
                this.flowered = smartDesignTask.styleType ?: StyleTypeEnum.PURE_COLOR.code
                this.taskAttribute = req.taskAttribute
                this.callback = req.callback
            }
        }
    }

    fun packFabricRecommendModelReq(task: FabricRecommendTask): FabricRecommendModelReq {
        return FabricRecommendModelReq(
            inputImage = task.inputImage,
            category = task.category
        ).apply {
            task.flowered?.also {
                this.pure_color = it == 0
            }
        }
    }

}

