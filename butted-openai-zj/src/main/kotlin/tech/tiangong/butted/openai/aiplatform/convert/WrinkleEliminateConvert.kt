package tech.tiangong.butted.openai.aiplatform.convert

import tech.tiangong.butted.openai.aiplatform.aimodel.req.WrinkleEliminateModelReq
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.WrinkleEliminateTask
import tech.tiangong.butted.openai.vo.req.WrinkleEliminateTaskReq
import team.aikero.blade.logging.core.annotation.Slf4j

@Slf4j
object WrinkleEliminateConvert : BaseConvert {

    fun buildWrinkleEliminateTask(req: WrinkleEliminateTaskReq): WrinkleEliminateTask {
        return WrinkleEliminateTask().apply {
            this.initBase()
            this.refImgUrl = req.refImgUrl.toFullImgPath()
            this.maskUrl = req.maskUrl.toFullImgPath()
        }
    }

    fun packWrinkleEliminateModelReq(task: WrinkleEliminateTask): WrinkleEliminateModelReq {
        return WrinkleEliminateModelReq(
            taskId = task.taskId,
            refImgUrl = task.refImgUrl,
            maskUrl = task.maskUrl
        )
    }

}

