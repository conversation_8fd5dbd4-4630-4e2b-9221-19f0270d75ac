package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.PredLabelVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.FlowerPatternLabelTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.FlowerPatternLabelProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternLabelModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FlowerPatternLabelOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FlowerPatternLabelConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.FlowerPatternLabelTask
import tech.tiangong.butted.openai.repository.FlowerPatternLabelTaskRepository
import tech.tiangong.butted.openai.service.FlowerPatternLabelTaskService
import tech.tiangong.butted.openai.vo.query.FlowerPatternLabelTaskQuery
import tech.tiangong.butted.openai.vo.req.FlowerPatternLabelTaskReq
import tech.tiangong.butted.openai.vo.resp.FlowerPatternLabelTaskVo

/**
 * 花型特征标签任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class FlowerPatternLabelTaskServiceImpl(
    config: FlowerPatternLabelProperties,
    val flowerPatternLabelTaskRepository: FlowerPatternLabelTaskRepository
) : TaskBaseSupportImpl<FlowerPatternLabelTask, FlowerPatternLabelOutputVo>(
    config,
    flowerPatternLabelTaskRepository
), FlowerPatternLabelTaskService {
    init {
        modeEnum = AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL
    }


    override fun detail(taskId: Long): FlowerPatternLabelTaskVo? {
        val task = flowerPatternLabelTaskRepository.getById(taskId)
        return task?.copy(FlowerPatternLabelTaskVo::class)?.apply {
            predLabelList = task.usableLabels?.parseJsonList(PredLabelVo::class.java)
        }
    }

    override fun getByParentTaskId(parentTaskId: Long): FlowerPatternLabelTaskVo? {
        val task = flowerPatternLabelTaskRepository.getByParentTaskId(parentTaskId)
        return task?.copy(FlowerPatternLabelTaskVo::class)?.apply {
            predLabelList = task.usableLabels?.parseJsonList(PredLabelVo::class.java)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByParentTaskId(parentTaskId: Long) {
        flowerPatternLabelTaskRepository.getByParentTaskId(parentTaskId)?.also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: FlowerPatternLabelTaskReq): Long {
        return FlowerPatternLabelConvert.buildFlowerPatternLabelTask(req).let {
            flowerPatternLabelTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun buildTaskModelParams(task: FlowerPatternLabelTask): FlowerPatternLabelModelReq {
        return FlowerPatternLabelConvert.packFlowerPatternLabelModelReq(task)
    }


    override fun handlePullResult(
        task: FlowerPatternLabelTask,
        changeStateEnum: TaskStatusEnum,
        output: FlowerPatternLabelOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.predLabels = it.predLabels?.toJson()
                task.usableLabels = it.toPredLabelDtoListJsonStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, FlowerPatternLabelTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: FlowerPatternLabelTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, FlowerPatternLabelTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return FlowerPatternLabelTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                flowerPatternLabelTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: FlowerPatternLabelTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            flowerPatternLabelTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
