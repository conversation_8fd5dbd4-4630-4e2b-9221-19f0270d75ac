package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.FluxOutpaintTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.FluxOutpaintTaskCreateVo
import tech.tiangong.butted.common.vo.FluxOutpaintTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.QwenProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FluxOutpaintModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FluxOutpaintVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.FluxOutpaintConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.FluxOutpaintTask
import tech.tiangong.butted.openai.repository.FluxOutpaintTaskRepository
import tech.tiangong.butted.openai.service.FluxOutpaintTaskService
import tech.tiangong.butted.openai.vo.query.FluxOutpaintTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * 扩图模型任务Service
 *
 * <AUTHOR>
 * @date       ：2024/12/30 10:34
 * @version    :1.0
 */
@Slf4j
@Service
class FluxOutpaintTaskServiceImpl(
    config: QwenProperties,
    private val fluxOutpaintTaskRepository: FluxOutpaintTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<FluxOutpaintTask, FluxOutpaintVo>(
    config,
    fluxOutpaintTaskRepository
), FluxOutpaintTaskService {
    override fun buildTaskModelParams(task: FluxOutpaintTask): FluxOutpaintModelReq = FluxOutpaintConvert.convert(task)

    override fun doSync(task: FluxOutpaintTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun handlePullResult(
        task: FluxOutpaintTask,
        changeStateEnum: TaskStatusEnum,
        output: FluxOutpaintVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: FluxOutpaintTask, canceled: Boolean) {
        log.info { "扩图任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }


    override fun batchCreate(req: CompanyUserBatchReq<FluxOutpaintTaskReq>): List<FluxOutpaintTaskCreateVo> {
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val data = req.data.map {
                FluxOutpaintConvert.convert(it)
                    .apply {
                        this.callback = req.callback
                    }
            }
            batchCreateHandle.create(
                data,
                { fluxOutpaintTaskRepository.saveBatch(data, data.size) },
                { doPush(it) }) {
                fluxOutpaintTaskRepository.updateBatchById(data, data.size)
            }
            data.map { FluxOutpaintTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<FluxOutpaintTaskVo> =
        task2VO(this.fluxOutpaintTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<FluxOutpaintTaskVo> =
        task2VO(this.fluxOutpaintTaskRepository.listByBusIds(ids))

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> =
        FluxOutpaintTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                fluxOutpaintTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }


    private fun manualUpdateAiTaskInfo(task: FluxOutpaintTask) {
        task.refreshRevisedTime()
        withTranExec {
            fluxOutpaintTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    private fun task2VO(data: List<FluxOutpaintTask>) = if (data.isNotEmpty()) {
        data.map { FluxOutpaintConvert.toVO(it) }
    } else {
        listOf()
    }

    init {
        modeEnum = AiTaskModeEnum.FLUXOUTPAINT
    }
}