package tech.tiangong.butted.openai.aiplatform.component


import com.zjkj.supplychain.commodity.client.beans.vo.response.TaoTianCommodityRespVo

/**
 * SPU（款式）识别家族处理器
 */
class TopFamilyNameHandle(top5FamilyNamesAll: List<List<String?>?>?) {
    /**
     * 家族列表
     */
    private var top5FamilyNamesDtoList: MutableList<TopFamilyNames> = mutableListOf()

    /**
     * 已选中的SKU列表
     */
    private var selectedSkuIdList: MutableList<Long> = mutableListOf()

    /**
     * 家族个数
     */
    private var maxTop = 5

    init {
        this.maxTop = 5
        this.selectedSkuIdList = mutableListOf()
        var topFive = (top5FamilyNamesAll ?: listOf()).toMutableList()
        if (topFive.size > 5) {
            topFive = topFive.subList(0, 5)
        }
        this.top5FamilyNamesDtoList = topFive.mapIndexed { index, fnl ->
            TopFamilyNames(index, fnl)
        }.toMutableList()
    }


    /**
     * 判断是否还有下一轮,有一个家族标签有下一轮，则返回true
     *
     * @return
     * <AUTHOR>
     * @since 2024年07月24日10:39:31
     */
    fun checkNextRound(): Boolean {
        for (topN in 0 until this.maxTop) {
            getTopNFamilyNamesDto(topN)?.also {
                val checkNext = it.checkNext()
                if (checkNext) {
                    return true
                }
            }
        }
        return false
    }


    private fun getTopNFamilyNamesDto(topN: Int): TopFamilyNames? {
        return top5FamilyNamesDtoList.getOrNull(topN)
    }

    /**
     * 每轮，每个家族随机选取4个标签（一次最多拿20个）
     *
     * @return
     * <AUTHOR>
     * @since 2024年07月24日10:03:32
     */
    fun nextHandleFamilyNamesList(): List<String> {
        val nextHandleNamesList: MutableList<String> = ArrayList()
        val perNext = 4
        for (topN in 0 until this.maxTop) {
            getTopNFamilyNamesDto(topN)?.also {
                if (it.checkNext()) {
                    nextHandleNamesList.addAll(it.next(perNext))
                }
            }
        }
        return nextHandleNamesList
    }


    val allFamilyNamesList: List<String>
        get() {
            val allFamilyNamesList = mutableListOf<String>()
            for (topN in 0 until this.maxTop) {
                getTopNFamilyNamesDto(topN)?.also {
                    allFamilyNamesList.addAll(it.topFamilyNames)
                }
            }
            return allFamilyNamesList
        }


    /**
     * 选中SKU
     */
    fun doSelected(fabricLabelMap: MutableMap<String?, TaoTianCommodityRespVo?>?) {
        if (fabricLabelMap.isNullOrEmpty()) {
            return
        }
        for (topFamilyNamesDto in this.top5FamilyNamesDtoList) {
            topFamilyNamesDto.doSelected(fabricLabelMap)?.let {
                selectedSkuIdList.add(it)
            }
        }
    }

    val availableFamilyNamesList: List<TopFamilyNames>
        get() = this.top5FamilyNamesDtoList.filter { it.isSelected || it.handledFamilyNames.isNotEmpty() }
}


