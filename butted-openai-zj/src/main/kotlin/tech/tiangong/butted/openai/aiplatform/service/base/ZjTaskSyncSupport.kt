package tech.tiangong.butted.openai.aiplatform.service.base

import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.success
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.jdbc.TransactionalManagerUtil
import tech.tiangong.butted.openai.aiplatform.remote.RestAiPlatformApi
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.service.base.TaskSyncSupport

/**
 * ZjTaskSyncSupport
 */
interface ZjTaskSyncSupport<T : SyncTask> : TaskSyncSupport<T>, ZjTaskBaseSupport<T> {

    fun handleSync(taskId: Long)
    fun doSync(task: T)
    override fun sync(taskIds: List<Long>) {
        taskIds.forEach {
            handleSync(it)
        }
    }

    /**
     * 事务提交后回调业务
     */
    fun callbackBusiness(task: T, modeEnum: AiTaskModeEnum) {
        TransactionalManagerUtil.afterCommit {
            doCallbackBusiness(task, modeEnum)
        }
    }

    /**
     * 事务提交后回调业务
     */
    fun doCallbackBusiness(task: T, modeEnum: AiTaskModeEnum) {
        try {
            if (!needAsync(task)) {
                handleSyncResponse(task, modeEnum, ok(null))
            } else {
                RestAiPlatformApi.callback(task, modeEnum).run {
                    handleSyncResponse(task, modeEnum, this)
                }
            }
        } catch (e: Exception) {
            log.error { "\"同步[${modeEnum.name}]任务失败：\n${e.stackTraceToString()}" }
        }
    }

    /**
     * 事务提交后回调业务
     */
    fun callbackAigcBusiness(task: T, modeEnum: AiTaskModeEnum, aigcTaskType: String) {
        try {
            TransactionalManagerUtil.afterCommit {
                if (!needAsync(task)) {
                    handleSyncResponse(task, modeEnum, ok(null))
                } else {
                    RestAiPlatformApi.callbackAigc(task, aigcTaskType).run {
                        handleSyncResponse(task, modeEnum, this)
                    }
                }
            }
        } catch (e: Exception) {
            log.error { "\"同步[${modeEnum.name}]任务失败：\n${e.stackTraceToString()}" }
        }
    }

    //子类可以覆盖此方法，TO更新持久化任务同步状态到数据库
    //参考：
    //TaskSyncPkgSupportImpl.handleSyncResponse()
    //TaskSyncSupportImpl.handleSyncResponse()
    fun handleSyncResponse(task: T, aiModeEnum: AiTaskModeEnum, response: DataResponse<*>) {
        fillSyncStatus(task, aiModeEnum, response)
    }


    fun fillSyncStatus(task: T, aiModeEnum: AiTaskModeEnum, response: DataResponse<*>) {
        if (response.success()) {
            log.info { "同步[${aiModeEnum.name}]任务成功,${task.taskId}" }
            task.syncSuccess()
        } else {
            log.info { "同步[${aiModeEnum.name}]任务失败,${task.taskId}" }
            task.syncFail()
            task.message = response.message.blankDefault("同步[${aiModeEnum.name}]任务失败,${task.taskId}")
        }
    }

}