package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.VectorTaskReq
import tech.tiangong.butted.common.vo.VectorTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.pollForResult
import tech.tiangong.butted.core.toolkit.toJson
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.VectorProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.VectorModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.VectorOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.VectorConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.VectorTask
import tech.tiangong.butted.openai.repository.VectorTaskRepository
import tech.tiangong.butted.openai.service.VectorTaskService
import tech.tiangong.butted.openai.vo.query.VectorTaskQuery
import java.util.concurrent.ConcurrentHashMap

/**
 * 图片向量任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class VectorTaskServiceImpl(
    config: VectorProperties,
    val vectorTaskRepository: VectorTaskRepository
) : TaskSyncSupportImpl<VectorTask, VectorOutputVo>(
    config,
    vectorTaskRepository
), VectorTaskService {
    init {
        modeEnum = AiTaskModeEnum.VECTOR
    }

    private val taskPoolMap: ConcurrentHashMap<String, Long> = ConcurrentHashMap()

    override fun obtainVector(req: VectorTaskReq): List<Float> {
        if (taskPoolMap.contains(req.busId)) {
            throw ButtedException("当前业务ID正在执行中，请稍后")
        }
        if (taskPoolMap.size > 50) {
            throw ButtedException("当前正在执行的任务过多，请稍后重试")
        }
        val task = VectorConvert.manualBuild(req)
        try {
            taskPoolMap[req.busId!!.toString()] = System.currentTimeMillis()
            doPush(task)
            val output = try {
                pollForResult(1000L * 60, 1000L) {
                    try {
                        getOutputResult(task)
                    } catch (e: Exception) {
                        log.error { "拉取图片向量失败：${e.stackTraceToString()}" }
                        if (e is TaskException) {
                            throw e
                        } else {
                            null
                        }
                    }
                }
            } catch (e: Throwable) {
                log.error { "获取图片向量失败：${e.stackTraceToString()}" }
                if (e is TaskException) {
                    val statusEnum = e.taskStatus ?: TaskStatusEnum.FAILED
                    task.taskStatus = statusEnum.code
                    task.message = e.message
                } else {
                    task.taskStatus = TaskStatusEnum.FAILED.code
                    task.message = e.message
                }
                null
            }
            val vectors = if (output == null || output.input_imgvector.isEmpty()) {
                if (!TaskStatusEnum.failedOrCanceled(task.taskStatus)) {
                    task.taskStatus = TaskStatusEnum.FAILED.code
                    task.message = "图片向量结果为空"
                }
                listOf()
            } else {
                task.taskStatus = TaskStatusEnum.COMPLETED.code
                task.vector = output.input_imgvector.toJson()
                output.input_imgvector!!
            }
            executor.execute {
                vectorTaskRepository.saveManualFill(task)
            }
            return vectors
        } finally {
            clearTaskKey(task.busId!!.toString())
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: VectorTaskReq): Long {
        return VectorConvert.build(req).let {
            vectorTaskRepository.save(it)
            doPush(it)
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: VectorTaskReq): Long {
        return VectorConvert.manualBuild(req).let {
            vectorTaskRepository.save(it)
            doPush(it)
        }
    }


    override fun buildTaskModelParams(task: VectorTask): VectorModelReq {
        return VectorConvert.packVectorModelReq(task)
    }


    override fun getByTaskId(taskId: Long): VectorTaskVo {
        val task = vectorTaskRepository.obtainById(taskId, "任务不存在")
        return VectorConvert.packVectorTaskVo(task)
    }

    override fun getByBusId(busId: Long): VectorTaskVo {
        return vectorTaskRepository.getByBusId(busId).let {
            VectorConvert.packVectorTaskVo(it)
        }
    }

    override fun handlePullResult(
        task: VectorTask,
        changeStateEnum: TaskStatusEnum,
        output: VectorOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            if (output == null || output.input_imgvector.isEmpty()) {
                handleException(task, "图片向量结果为空", TaskStatusEnum.FAILED)
                return
            }
            task.vector = output.input_imgvector.toJson()
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleException(task: VectorTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task)
    }


    override fun handleTimeoutResult(task: VectorTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return VectorTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                vectorTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        vectorTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    override fun doSync(task: VectorTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }


    fun manualUpdateAiTaskInfo(task: VectorTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (task.notExistCallback()) {
            task.synced()
        }
        withTranExec {
            vectorTaskRepository.updateByIdManualFill(task)
            event?.also {
                afterCommitExec {
                    publishEvent(it)
                }
            }
            checkCallbackBusiness(task)
        }
    }


    private fun clearTaskKey(key: String) {
        try {
            taskPoolMap.remove(key)
            taskPoolMap.forEach { (key, mills) ->
                if (System.currentTimeMillis() - mills > 1000L * 60) {
                    taskPoolMap.remove(key)
                }
            }
        } catch (e: Exception) {
            log.error { "clearTaskKey error:${e.stackTraceToString()}" }
        }
    }

}
