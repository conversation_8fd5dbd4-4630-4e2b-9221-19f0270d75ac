package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import com.zjkj.supplychain.base.open.sdk.client.commodity.TaoTianCommodityClient
import com.zjkj.supplychain.commodity.client.beans.vo.request.TaoTianCommoditySearchReqVo
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.dto.FabricLabelDto
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.BatchRecommendFabricReq
import tech.tiangong.butted.common.req.ReRecommendFabricReq
import tech.tiangong.butted.common.vo.FabricLabelVo
import tech.tiangong.butted.common.vo.FabricRecommendLabelVo
import tech.tiangong.butted.common.vo.FabricRecommendTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.FabricRecommendTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.enums.AieCodeEnum
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.FabricRecommendProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FabricRecommendModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.FabricRecommendLabelModelVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FabricRecommendOutputVo
import tech.tiangong.butted.openai.aiplatform.component.TopFamilyNameHandle
import tech.tiangong.butted.openai.aiplatform.component.TopFamilyNames
import tech.tiangong.butted.openai.aiplatform.convert.FabricRecommendConvert
import tech.tiangong.butted.openai.aiplatform.convert.FabricRecommendSkuConvert
import tech.tiangong.butted.openai.aiplatform.remote.FmClientApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.FabricRecommendTask
import tech.tiangong.butted.openai.repository.FabricRecommendSkuRepository
import tech.tiangong.butted.openai.repository.FabricRecommendTaskRepository
import tech.tiangong.butted.openai.repository.SmartDesignTaskRepository
import tech.tiangong.butted.openai.service.FabricRecommendTaskService
import tech.tiangong.butted.openai.vo.query.FabricRecommendTaskQuery
import tech.tiangong.butted.openai.vo.req.FabricRecommendTaskReq
import java.math.BigDecimal

/**
 * 面料推荐任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class FabricRecommendTaskServiceImpl(
    config: FabricRecommendProperties,
    val taoTianCommodityClient: TaoTianCommodityClient,
    val smartDesignTaskRepository: SmartDesignTaskRepository,
    val fabricRecommendTaskRepository: FabricRecommendTaskRepository,
    val fabricRecommendSkuRepository: FabricRecommendSkuRepository
) : TaskSyncSupportImpl<FabricRecommendTask, FabricRecommendOutputVo>(
    config,
    fabricRecommendTaskRepository
), FabricRecommendTaskService {

    init {
        modeEnum = AiTaskModeEnum.FABRIC_RECOMMEND
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: FabricRecommendTaskReq): Long {
        return FabricRecommendConvert.build(req).let {
            fabricRecommendTaskRepository.save(it)
            doPush(it)
        }
    }


    /**
     * 重新推荐面料
     * @param req FabricReRecommendReq
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun reRecommendFabric(req: ReRecommendFabricReq, source: SourceEnum): Long {
        val smartDesignTask = smartDesignTaskRepository.getByBusId(req.smartDevelopId!!)
        return FabricRecommendConvert.build(smartDesignTask, req, source).let {
            fabricRecommendTaskRepository.save(it)
            doPush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun batchRecommendFabric(req: BatchRecommendFabricReq, source: SourceEnum): List<Long> {
        val smartDesignTask = smartDesignTaskRepository.getByBusId(req.smartDevelopId!!)
        val recommendTaskList = FabricRecommendConvert.batchBuild(smartDesignTask, req, source)
        fabricRecommendTaskRepository.saveBatch(recommendTaskList)
        recommendTaskList.forEach {
            doPush(it)
        }
        return recommendTaskList.map { it.taskId!! }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualBatchRecommendFabric(req: BatchRecommendFabricReq, source: SourceEnum): List<Long> {
        val smartDesignTask = smartDesignTaskRepository.getByBusId(req.smartDevelopId!!)
        val recommendTaskList = FabricRecommendConvert.manualBatchBuild(smartDesignTask, req, source)
        fabricRecommendTaskRepository.saveBatchManualFill(recommendTaskList)
        recommendTaskList.forEach {
            doPush(it)
        }
        return recommendTaskList.map { it.taskId!! }
    }

    override fun detail(taskId: Long): FabricRecommendTaskVo? {
        return packFabricRecommendTaskVo(fabricRecommendTaskRepository.getById(taskId))
    }

    override fun getByParentTaskId(parentTaskId: Long): FabricRecommendTaskVo? {
        return packFabricRecommendTaskVo(fabricRecommendTaskRepository.getByParentTaskId(parentTaskId))
    }

    override fun getByBusId(busId: Long): FabricRecommendTaskVo {
        return packFabricRecommendTaskVo(fabricRecommendTaskRepository.getByBusId(busId))!!
    }

    override fun getSmartDesignFabricRecommendTaskVo(parentTaskId: Long, category: String): FabricRecommendTaskVo? {
        return packFabricRecommendTaskVo(
            getSmartDesignFabricRecommendTask(
                parentTaskId,
                category
            )
        )
    }

    override fun getSmartDesignFabricRecommendTask(parentTaskId: Long, category: String): FabricRecommendTask? {
        return fabricRecommendTaskRepository.getSmartDesignFabricRecommendTask(parentTaskId, category)
    }

    private fun packFabricRecommendTaskVo(task: FabricRecommendTask?): FabricRecommendTaskVo? {
        return task?.copy(FabricRecommendTaskVo::class)?.apply {
            this.fabricLabel = task.selectedLabelInfo?.parseJson(FabricLabelVo::class.java)
            task.predLabels?.also {
                this.predLabelInfo = it.parseJson(FabricRecommendLabelVo::class.java)
            }
            if (TaskStatusEnum.completed(task.taskStatus)) {
                this.recommendFabricList = fabricRecommendSkuRepository.listByRecommendTaskId(task.taskId)
            }
        }
    }

    override fun buildTaskModelParams(task: FabricRecommendTask): FabricRecommendModelReq {
        return FabricRecommendConvert.packFabricRecommendModelReq(task)
    }


    override fun handlePullResult(
        task: FabricRecommendTask,
        changeStateEnum: TaskStatusEnum,
        output: FabricRecommendOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        var realStateEnum: TaskStatusEnum = changeStateEnum
        if (completed) {
            if (output?.predLabels == null) {
                realStateEnum = TaskStatusEnum.FAILED
                task.message = "面料推荐结果为空"
            } else {
                output.predLabels!!.apply {
                    if (successful()) {
                        try {
                            handleRecommendResult(task, this)
                        } catch (e: Exception) {
                            log.error { "分析面料推荐结果失败：${e.stackTraceToString()}" }
                            task.message = e.message ?: "分析面料推荐结果失败"
                            realStateEnum = TaskStatusEnum.FAILED
                        }
                    } else {
                        val aieEnum: AieCodeEnum? = AieCodeEnum.fromCode(this.code_e)
                        task.message = aieEnum?.desc ?: "面料识别失败"
                        realStateEnum = TaskStatusEnum.FAILED
                    }
                }
            }
        }

        if (task.taskStatus != realStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = realStateEnum.code
        manualUpdateAiTaskInfo(task, FabricRecommendTaskEvent(task, realStateEnum))
    }

    override fun handleTimeoutResult(task: FabricRecommendTask, canceled: Boolean) {
        task.waitSync()
        manualUpdateAiTaskInfo(task, FabricRecommendTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return FabricRecommendTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                fabricRecommendTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun handleRecommendResult(task: FabricRecommendTask, predLabels: FabricRecommendLabelModelVo) {
        task.predLabels = predLabels.toJson()
        //识别颜色LAB
        var labValue: List<BigDecimal>? = predLabels.lab_value
        val topNFamilyNames = predLabels.topN_familyNames ?: mutableListOf()
        val topNScore = predLabels.topN_score ?: mutableListOf()
        val topNFamilyNamesAll = predLabels.topN_familyNames_all
        val fabricLabel = FabricLabelDto().apply {
            this.marketName = topNFamilyNames.firstOrNull()
            this.marketNameEn = predLabels.topN_prompts?.firstOrNull()
        }
        FmClientApi.findClosestColorLabel(labValue)?.also {
            fabricLabel.colorCode = it.colorCode
            fabricLabel.colorHue = it.hue
        }
        task.selectedLabelInfo = fabricLabel.checkToJson()
        if (labValue.isEmpty()) {
            //默认LAB
            labValue = listOf(BigDecimal("94.82"), BigDecimal("0.37"), BigDecimal("5.13"))
        }
        val topFamilyNameHandle = TopFamilyNameHandle(topNFamilyNamesAll)
        if (topFamilyNameHandle.allFamilyNamesList.isEmpty()) {
            return
        }
        do {
            var nextHandleNamesList: List<String> = topFamilyNameHandle.nextHandleFamilyNamesList()
            if (nextHandleNamesList.isNotEmpty()) {
                //替换-为/；中台需要/分割
                nextHandleNamesList = nextHandleNamesList.map {
                    it.replace(Constant.DASHED, Constant.SLASH)
                }
                val searchReqVo = TaoTianCommoditySearchReqVo()
                searchReqVo.setFabrics(nextHandleNamesList)
                searchReqVo.setColor(labValue)
                //searchReqVo.setColor(List.of(new BigDecimal("41"), new BigDecimal("13"), new BigDecimal("14")));
                //searchReqVo.setPrice(priceInterval)
                log.info { "根据条件查询面料：${searchReqVo.toJson()}" }
                val cResponse = taoTianCommodityClient.findByCondition(searchReqVo)
                log.info { "根据条件查询面料结果：${cResponse.toJson()}" }
                if (cResponse.code != "200") {
                    throw ButtedException("查询面料失败：${cResponse.message}")
                }

                val data =
                    cResponse.data?.filter { it.fabric != null && it.taoTianCommodityVoList.isNotEmpty() } ?: listOf()
                val fabricLabelMap = data.associateBy { it.fabric }
                topFamilyNameHandle.doSelected(fabricLabelMap.toMutableMap())
            }
        } while (topFamilyNameHandle.checkNextRound())

        val availableFamilyNamesList: List<TopFamilyNames> = topFamilyNameHandle.availableFamilyNamesList
        if (availableFamilyNamesList.isEmpty()) {
            return
        }
        val skuList = availableFamilyNamesList.map {
            FabricRecommendSkuConvert.buildFabricRecommendSku(task, it).apply {
                this.familyName = topNFamilyNames.getOrNull(it.topIndex)
                this.familyScore = topNScore.getOrNull(it.topIndex)
            }
        }
        withTranExec {
            fabricRecommendSkuRepository.saveBatchManualFill(skuList)
        }
    }

    override fun doSync(task: FabricRecommendTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    fun manualUpdateAiTaskInfo(task: FabricRecommendTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (task.needAsync()) {
            //只有S_S_RECOMMEND_FABRIC需要同步
            if (!SourceEnum.needSync(task.source)) {
                task.synced()
            }
        }
        withTranExec {
            fabricRecommendTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
            callbackBusiness(task)
        }
    }

}
