package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.WrinkleMarkProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.WrinkleMarkModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.WrinkleMarkOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.WrinkleMarkConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.WrinkleMarkTask
import tech.tiangong.butted.openai.repository.WrinkleMarkTaskRepository
import tech.tiangong.butted.openai.service.WrinkleMarkTaskService
import tech.tiangong.butted.openai.vo.query.WrinkleMarkTaskQuery
import tech.tiangong.butted.openai.vo.req.WrinkleMarkTaskReq
import tech.tiangong.butted.openai.vo.resp.WrinkleMarkTaskVo

/**
 * 褶皱mark任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class WrinkleMarkTaskServiceImpl(
    config: WrinkleMarkProperties,
    val wrinkleMarkTaskRepository: WrinkleMarkTaskRepository
) : TaskBaseSupportImpl<WrinkleMarkTask, WrinkleMarkOutputVo>(
    config,
    wrinkleMarkTaskRepository
), WrinkleMarkTaskService {
    init {
        modeEnum = AiTaskModeEnum.WRINKLE_MARK
    }

    override fun detail(taskId: Long): WrinkleMarkTaskVo {
        val task = wrinkleMarkTaskRepository.obtainById(taskId)
        return task.copy(WrinkleMarkTaskVo::class).apply {
            resImg = task.resImgs?.splitAndFirst()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: WrinkleMarkTaskReq): Long {
        return WrinkleMarkConvert.buildWrinkleMarkTask(req).let {
            wrinkleMarkTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun buildTaskModelParams(task: WrinkleMarkTask): WrinkleMarkModelReq {
        return WrinkleMarkConvert.packWrinkleMarkModelReq(task)
    }

    override fun handlePullResult(
        task: WrinkleMarkTask,
        changeStateEnum: TaskStatusEnum,
        output: WrinkleMarkOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: WrinkleMarkTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return WrinkleMarkTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                wrinkleMarkTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: WrinkleMarkTask) {
        task.refreshRevisedTime()
        withTranExec {
            wrinkleMarkTaskRepository.updateByIdManualFill(task)
        }
    }
}
