package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pojo.CreatedResult
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.FaceRepairTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.FaceRepairProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FaceRepairOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FaceRepairConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.FaceRepairTask
import tech.tiangong.butted.openai.repository.FaceRepairTaskRepository
import tech.tiangong.butted.openai.service.FaceRepairTaskService
import tech.tiangong.butted.openai.vo.query.FaceRepairTaskQuery
import tech.tiangong.butted.openai.vo.req.FaceRepairTaskReq

/**
 * 脸部修复任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class FaceRepairTaskServiceImpl(
    config: FaceRepairProperties,
    val faceRepairTaskRepository: FaceRepairTaskRepository,
) : TaskBaseSupportImpl<FaceRepairTask, FaceRepairOutputVo>(
    config,
    faceRepairTaskRepository
), FaceRepairTaskService {
    init {
        modeEnum = AiTaskModeEnum.FACE_REPAIR
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: FaceRepairTaskReq): CreatedResult {
        log.info { "manualCreate.req:${req.inputImgList}." }
        return try {
            FaceRepairConvert.buildFaceRepairTask(req).let {
                faceRepairTaskRepository.saveManualFill(it)
                CreatedResult.success(doPush(it))
            }
        } catch (e: Exception) {
            val stackTraceToString = e.stackTraceToString()
            log.error { "创建修脸任务失败：${stackTraceToString}" }
            CreatedResult.fail(stackTraceToString)
        }
    }

    override fun buildTaskModelParams(task: FaceRepairTask): Any {
        return FaceRepairConvert.packFaceRepairModelReq(task)
    }


    override fun getBySourceId(sourceId: Long): FaceRepairTask? {
        return faceRepairTaskRepository.getBySourceId(sourceId)
    }

    override fun handlePullResult(
        task: FaceRepairTask,
        changeStateEnum: TaskStatusEnum,
        output: FaceRepairOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, FaceRepairTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: FaceRepairTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, FaceRepairTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return FaceRepairTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                faceRepairTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: FaceRepairTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            faceRepairTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }

}
