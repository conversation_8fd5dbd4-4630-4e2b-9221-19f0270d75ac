package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pojo.CreatedResult
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.LimbRepairTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.LimbRepairProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.LimbRepairModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.LimbRepairOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FaceRepairConvert.manualInitBaseTask
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.FaceRepairTask
import tech.tiangong.butted.openai.entity.LimbRepairTask
import tech.tiangong.butted.openai.repository.LimbRepairTaskRepository
import tech.tiangong.butted.openai.service.LimbRepairTaskService
import tech.tiangong.butted.openai.vo.query.LimbRepairTaskQuery
import tech.tiangong.butted.openai.vo.req.LimbRepairTaskReq

/**
 * @author: xieyuxiang
 * @Date 2025/5/22
 */
@Slf4j
@Service
class LimbRepairTaskServiceImpl(
    config: LimbRepairProperties,
    val limbRepairTaskRepository: LimbRepairTaskRepository,
) : TaskBaseSupportImpl<LimbRepairTask, LimbRepairOutputVo>(
    config,
    limbRepairTaskRepository
), LimbRepairTaskService {
    init {
        modeEnum = AiTaskModeEnum.LIMB_REPAIR
    }

    /**
     * 手脚修复
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: LimbRepairTaskReq): CreatedResult {
        return try {
            buildLimbRepairTask(req).let {
                limbRepairTaskRepository.saveManualFill(it)
                CreatedResult.success(doPush(it))
            }
        } catch (e: Exception) {
            val stackTraceToString = e.stackTraceToString()
            log.error { "创建修手脚任务失败：${stackTraceToString}" }
            CreatedResult.fail(stackTraceToString)
        }
    }

    override fun doPushTest(taskId: Long) {
        val task = limbRepairTaskRepository.getById(taskId)
        doPush(task)
    }


    override fun getBySourceId(sourceId: Long): LimbRepairTask? {
        return limbRepairTaskRepository.getBySourceId(sourceId)
    }
    override fun buildTaskModelParams(task: LimbRepairTask): Any {
        return  packLimbRepairModelReq(task)
    }

    override fun handlePullResult(
        task: LimbRepairTask,
        changeStateEnum: TaskStatusEnum,
        output: LimbRepairOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, LimbRepairTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: LimbRepairTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, LimbRepairTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return LimbRepairTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                limbRepairTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    private fun buildLimbRepairTask(req: LimbRepairTaskReq): LimbRepairTask {
        return LimbRepairTask().apply {
            this.taskId = IdHelper.getId()
            this.source = req.source.fullSource()
            this.sourceId = req.sourceId
            this.parentTaskId = req.parentTaskId
            this.manualInitBaseTask(req)
            this.inputImgs = req.inputImgList.joinToStr()
            this.firstPushed()
            this.taskAttribute = req.taskAttribute
        }
    }
    private fun packLimbRepairModelReq(task: LimbRepairTask): LimbRepairModelReq {
        return LimbRepairModelReq(
            inputImage = task.inputImgs!!.split(","),
            taskId = task.taskId!!
        )
    }
    private fun manualUpdateAiTaskInfo(task: LimbRepairTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            limbRepairTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }


}
