package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.DesignMaterialTaskVo
import tech.tiangong.butted.common.vo.PredLabelVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.MarketCategoryTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.MarketCategoryProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternLabelModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.req.MarketCategoryModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FlowerPatternLabelOutputVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.MarketCategoryTaskOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FlowerPatternLabelConvert
import tech.tiangong.butted.openai.aiplatform.convert.MarketCategoryConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.MarketCategoryTask
import tech.tiangong.butted.openai.repository.MarketCategoryTaskRepository
import tech.tiangong.butted.openai.service.MarketCategoryTaskService
import tech.tiangong.butted.openai.vo.query.MarketCategoryTaskQuery
import tech.tiangong.butted.openai.vo.req.MarketCategoryTaskBatchCreateReq
import tech.tiangong.butted.openai.vo.req.MarketCategoryTaskQueryReq
import tech.tiangong.butted.openai.vo.req.MarketCategoryTaskReq
import tech.tiangong.butted.openai.vo.resp.MarketCategoryTaskBatchCreateVo
import tech.tiangong.butted.openai.vo.resp.MarketCategoryTaskVo
import tech.tiangong.butted.openai.vo.resp.MarketPredLabelsVo

/**
 * 花型特征标签任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class MarketCategoryTaskServiceImpl(
    config: MarketCategoryProperties,
    val marketCategoryTaskRepository: MarketCategoryTaskRepository
) : TaskBaseSupportImpl<MarketCategoryTask, MarketCategoryTaskOutputVo>(
    config,
    marketCategoryTaskRepository
), MarketCategoryTaskService {
    init {
        modeEnum = AiTaskModeEnum.JV_CATEGORY_REC
    }


    override fun detail(taskId: Long): MarketCategoryTaskVo? {
        val task = marketCategoryTaskRepository.getById(taskId)
        if(task == null){
            return null
        }
        return task.copy(MarketCategoryTaskVo::class).apply{
            this.predLabelsInfo = task.predLabels?.parseJson(MarketPredLabelsVo::class.java)
        }
    }

    /**
     * 批量查询任务信息
     */
    override fun qeryByIds(req: MarketCategoryTaskQueryReq): List<MarketCategoryTaskVo>? {
        val taskList = marketCategoryTaskRepository.listByTaskIds(req.taskIds!!)
        val result = mutableListOf<MarketCategoryTaskVo>()
        taskList.forEach { task ->
            result.add(
                task.copy(MarketCategoryTaskVo::class).apply{
                this.predLabelsInfo = task.predLabels?.parseJson(MarketPredLabelsVo::class.java) }
            )
        }
        return result
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: MarketCategoryTaskReq): Long {
        return MarketCategoryConvert.buildMarketCategoryTask(req).let {
            marketCategoryTaskRepository.save(it)
            doPush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun batchCreate(req: MarketCategoryTaskBatchCreateReq): List<MarketCategoryTaskBatchCreateVo> {
        val result = mutableListOf<MarketCategoryTaskBatchCreateVo>()
        req.inputImage?.forEach {

            MarketCategoryConvert.buildMarketCategoryTask( MarketCategoryTaskReq(it,req.marketCode)).let {
                marketCategoryTaskRepository.save(it)
                doPush(it)
                result.add(MarketCategoryTaskBatchCreateVo(it.taskId, it.inputImage))
            }
        }
       return  result
    }

    override fun buildTaskModelParams(task: MarketCategoryTask): MarketCategoryModelReq {
        return MarketCategoryConvert.packMarketCategoryModelReq(task)
    }


    override fun handlePullResult(
        task: MarketCategoryTask,
        changeStateEnum: TaskStatusEnum,
        output: MarketCategoryTaskOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.predLabels = it.predLabels?.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, MarketCategoryTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: MarketCategoryTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, MarketCategoryTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return MarketCategoryTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                marketCategoryTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: MarketCategoryTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            marketCategoryTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
