package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.UltraHdTaskCreateReq
import tech.tiangong.butted.common.vo.UltraHdTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.exception.NotFoundException
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.UltraHdProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.UltraHdModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.UltraHdOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.UltraHdConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.UltraHdTask
import tech.tiangong.butted.openai.repository.UltraHdTaskRepository
import tech.tiangong.butted.openai.service.UltraHdTaskService
import tech.tiangong.butted.openai.vo.query.UltraHdTaskQuery
import tech.tiangong.butted.openai.vo.req.UltraHdTaskReq

/**
 * 4K高清服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class UltraHdTaskServiceImpl(
    config: UltraHdProperties,
    val ultraHdTaskRepository: UltraHdTaskRepository
) : TaskSyncSupportImpl<UltraHdTask, UltraHdOutputVo>(
    config,
    ultraHdTaskRepository
), UltraHdTaskService {
    init {
        modeEnum = AiTaskModeEnum.ULTRA_HD
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun obtain(req: UltraHdTaskReq): UltraHdTaskVo? {
        var task = ultraHdTaskRepository.getByPictureIdAndMode(req.pictureId!!, req.taskMode!!)
        if (task == null) {
            task = UltraHdConvert.build(req)
            ultraHdTaskRepository.save(task)
            doPush(task)
        }
        return task.copy(UltraHdTaskVo::class).apply {
            resImg = task.resImgs?.splitAndFirst()
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: UltraHdTaskCreateReq): Long {
        var task = ultraHdTaskRepository.getByBusId(req.busId!!)
        if (task == null) {
            task = UltraHdConvert.build(req)
            ultraHdTaskRepository.saveManualFill(task)
            doPush(task)
        }
        return task.taskId!!
    }

    override fun getByBusId(busId: Long): UltraHdTaskVo {
        val task = ultraHdTaskRepository.getByBusId(busId) ?: throw NotFoundException("4K高清任务不存在")
        return task.copy(UltraHdTaskVo::class).apply {
            resImg = task.resImgs?.splitAndFirst()
        }
    }

    override fun getByTaskId(taskId: Long): UltraHdTaskVo {
        val task = ultraHdTaskRepository.obtainById(taskId, "4K高清任务不存在")
        return task.copy(UltraHdTaskVo::class).apply {
            resImg = task.resImgs?.splitAndFirst()
        }
    }

    override fun buildTaskModelParams(task: UltraHdTask): UltraHdModelReq {
        return UltraHdConvert.packUltraHdModelReq(task)
    }

    override fun handlePullResult(
        task: UltraHdTask,
        changeStateEnum: TaskStatusEnum,
        output: UltraHdOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: UltraHdTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return UltraHdTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                ultraHdTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    override fun doSync(task: UltraHdTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    fun manualUpdateAiTaskInfo(task: UltraHdTask) {
        task.refreshRevisedTime()
        if (task.notExistCallback()) {
            task.synced()
        }
        withTranExec {
            ultraHdTaskRepository.updateByIdManualFill(task)
            checkCallbackBusiness(task)
        }
    }
}
