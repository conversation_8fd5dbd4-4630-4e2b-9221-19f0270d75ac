package tech.tiangong.butted.openai.aiplatform.component

import jakarta.annotation.PostConstruct
import org.springframework.beans.factory.ObjectProvider
import org.springframework.stereotype.Component
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.core.pojo.JobBlockingQueue
import tech.tiangong.butted.openai.aiplatform.service.base.ZjTaskBaseSupport
import tech.tiangong.butted.openai.aiplatform.service.base.ZjTaskSyncSupport
import tech.tiangong.butted.openai.component.TaskProcessFactory
import java.util.stream.Collectors


/**
 * 任务工厂实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
class ZjTaskProcessFactoryImpl(
    taskProvider: ObjectProvider<ZjTaskBaseSupport<*>>
) : TaskProcessFactory {
    val taskServiceMap: Map<String, ZjTaskBaseSupport<*>> = taskProvider.orderedStream()
        .collect(Collectors.toList()).associateBy { it.taskMode() }

    companion object {
        val jobQueue = JobBlockingQueue()
    }

    override fun notify(taskMode: String, taskId: String, jsonBody: String?): Boolean {
        return taskServiceMap[taskMode]?.notify(taskMode, taskId, jsonBody) ?: false
    }

    override fun pullJob(modeNames: List<String>?): Boolean {
        if (modeNames.isNotEmpty()) {
            modeNames!!.forEach { modeName ->
                taskServiceMap[modeName]?.also { service ->
                    service.pull()
                }
            }
            return true
        }
        taskServiceMap.values.forEach { service ->
            service.pull()
        }
        return true
    }


    override fun syncJob(modeNames: List<String>?): Boolean {
        if (modeNames.isNotEmpty()) {
            modeNames!!.forEach { modeName ->
                taskServiceMap[modeName]?.also { service ->
                    if (service is ZjTaskSyncSupport) {
                        service.sync()
                    }
                }
            }
            return true
        }
        taskServiceMap.forEach { (mode, service) ->
            if (service is ZjTaskSyncSupport) {
                service.sync()
            }
        }
        return true
    }


    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val service = taskServiceMap[req.taskMode]
        if (service != null) {
            return service.checkSyncStatus(req)
        }
        log.warn { "没有找到对应的同步任务模型: $req" }
        return null
    }


    @PostConstruct
    fun initialize() {
        taskServiceMap.forEach { (key, service) ->
            println("初始化杉缔任务模型: $key[${service.javaClass.name}]")
        }
    }


}

