package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.UpscaleTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.UpscaleTaskCreateVo
import tech.tiangong.butted.common.vo.UpscaleTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.UpscaleProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.UpscaleModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.UpscaleVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.UpscaleConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.UpscaleTask
import tech.tiangong.butted.openai.repository.UpscaleTaskRepository
import tech.tiangong.butted.openai.service.UpscaleTaskService
import tech.tiangong.butted.openai.vo.query.UpscaleTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * 超分任务 Service
 *
 * <AUTHOR>
 * @date       ：2025/3/31 18:03
 * @version    :1.0
 */
@Slf4j
@Service
class UpscaleTaskServiceImpl(
    config: UpscaleProperties,
    private val transactionalManager: TransactionalManager,
    private val upscaleTaskRepository: UpscaleTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<UpscaleTask, UpscaleVo>(
    config,
    upscaleTaskRepository
), UpscaleTaskService {
    override fun buildTaskModelParams(task: UpscaleTask): UpscaleModelReq = UpscaleConvert.convert(task)

    override fun handlePullResult(
        task: UpscaleTask,
        changeStateEnum: TaskStatusEnum,
        output: UpscaleVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: UpscaleTask, canceled: Boolean) {
        log.info { "UPscale任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> = UpscaleTaskQuery()
        .let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                upscaleTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }

    override fun doSync(task: UpscaleTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: CompanyUserBatchReq<UpscaleTaskReq>): List<UpscaleTaskCreateVo> {
        val data = this.upscaleTaskRepository.listByBusIds(req.data.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { UpscaleTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val list = req.data.map {
                UpscaleConvert.convert(it).apply {
                    this.callback = req.callback
                }
            }
            batchCreateHandle.create(
                list,
                { upscaleTaskRepository.saveBatch(list, list.size) },
                { doPush(it) }) {
                upscaleTaskRepository.updateBatchById(list, list.size)
            }
            list.map { UpscaleTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<UpscaleTaskVo> =
        task2VO(this.upscaleTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<UpscaleTaskVo> =
        task2VO(this.upscaleTaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<UpscaleTask>) = if (data.isNotEmpty()) {
        data.map { UpscaleConvert.toVO(it) }
    } else {
        listOf()
    }

    private fun manualUpdateAiTaskInfo(task: UpscaleTask) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            upscaleTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    init {
        modeEnum = AiTaskModeEnum.UPSCALE
    }
}