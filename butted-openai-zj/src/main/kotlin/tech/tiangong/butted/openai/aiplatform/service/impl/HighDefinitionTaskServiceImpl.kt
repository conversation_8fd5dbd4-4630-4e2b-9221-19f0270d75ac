package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.HighDefinitionProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.HighDefinitionModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.HighDefinitionOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.HighDefinitionConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.HighDefinitionTask
import tech.tiangong.butted.openai.repository.HighDefinitionTaskRepository
import tech.tiangong.butted.openai.service.HighDefinitionTaskService
import tech.tiangong.butted.openai.vo.query.HighDefinitionTaskQuery
import tech.tiangong.butted.openai.vo.req.HighDefinitionTaskReq
import tech.tiangong.butted.openai.vo.resp.HighDefinitionTaskVo

/**
 * 4K高清服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class HighDefinitionTaskServiceImpl(
    config: HighDefinitionProperties,
    val highDefinitionTaskRepository: HighDefinitionTaskRepository
) : TaskBaseSupportImpl<HighDefinitionTask, HighDefinitionOutputVo>(
    config,
    highDefinitionTaskRepository
), HighDefinitionTaskService {
    init {
        modeEnum = AiTaskModeEnum.HIGH_DEFINITION
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun obtain(req: HighDefinitionTaskReq): HighDefinitionTaskVo? {
        var task = highDefinitionTaskRepository.getByPictureIdAndMode(req.pictureId!!, req.taskMode!!)
        if (task == null) {
            task = HighDefinitionConvert.build(req)
            highDefinitionTaskRepository.save(task)
            doPush(task)
        }
        return task.copy(HighDefinitionTaskVo::class).apply {
            originTaskId = task.busId
            resImg = task.resImgs?.splitAndFirst()
        }

    }

    override fun buildTaskModelParams(task: HighDefinitionTask): HighDefinitionModelReq {
        return HighDefinitionConvert.packHighDefinitionModelReq(task)
    }

    override fun handlePullResult(
        task: HighDefinitionTask,
        changeStateEnum: TaskStatusEnum,
        output: HighDefinitionOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: HighDefinitionTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return HighDefinitionTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                highDefinitionTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: HighDefinitionTask) {
        task.refreshRevisedTime()
        withTranExec {
            highDefinitionTaskRepository.updateByIdManualFill(task)
        }
    }
}
