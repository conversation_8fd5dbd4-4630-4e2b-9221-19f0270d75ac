package tech.tiangong.butted.openai.aiplatform.service.impl

import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.BizSourceEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.assertNotBlank
import tech.tiangong.butted.core.toolkit.parseObj
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.HandFootRepairEnum
import tech.tiangong.butted.enums.ShiftFaceEnum
import tech.tiangong.butted.enums.ShiftSceneEnum
import tech.tiangong.butted.enums.TryOnModeEnum
import tech.tiangong.butted.event.ShiftFaceStateEvent
import tech.tiangong.butted.event.ShiftFaceTaskEvent
import tech.tiangong.butted.event.publish.OpenaiTaskEventPublisher
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.convert.TryOnConvert
import tech.tiangong.butted.openai.entity.ShiftFaceTask
import tech.tiangong.butted.openai.entity.TryOnTask
import tech.tiangong.butted.openai.entity.TryOnTaskOutput
import tech.tiangong.butted.openai.repository.TryOnTaskOutputRepository
import tech.tiangong.butted.openai.repository.TryOnTaskRepository
import tech.tiangong.butted.openai.service.ShiftFaceTaskService
import tech.tiangong.butted.openai.service.ShiftSceneTaskService
import tech.tiangong.butted.openai.service.TryOnTaskOutputService
import tech.tiangong.butted.openai.vo.req.ShiftSceneTaskReq
import tech.tiangong.butted.openai.vo.req.TryOnTaskOutputReq
import tech.tiangong.butted.openai.vo.resp.base.ScenePictureEncodeVo
import tech.tiangong.butted.redis.RedissonHelper
import tech.tiangong.butted.remote.SceneInfoApi
import java.time.LocalDateTime
import java.util.stream.Collectors


/**
 * 虚拟换装生成图服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TryOnTaskOutputServiceImpl(
    @Qualifier("shiftFaceTaskJvServiceImpl")
    val shiftFaceTaskService: ShiftFaceTaskService,
    val tryOnTaskRepository: TryOnTaskRepository,
    val tryOnTaskOutputRepository: TryOnTaskOutputRepository,
    shiftSceneProvider: ObjectProvider<ShiftSceneTaskService>
) : TryOnTaskOutputService {
    val shiftSceneServiceMap: Map<String, ShiftSceneTaskService> = shiftSceneProvider.orderedStream()
        .collect(Collectors.toList()).associateBy { it.algorithm().name }

    @Deprecated("即将废弃了")
    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: TryOnTaskOutputReq) {
        RedissonHelper.lockExec("TryOnTaskOutput", req.tryOnTaskId) {
            var outputList = tryOnTaskOutputRepository.listByTaskId(req.tryOnTaskId)
            if (outputList.isNotEmpty() || req.resImgList.isEmpty()) {
                return@lockExec
            }
            outputList = TryOnConvert.buildTryOnTaskOutputList(req)
            if (req.doShiftFace) {
                tryShift(outputList, req.shiftFaceImgList!!, false)
            }
            tryOnTaskOutputRepository.saveBatchManualFill(outputList)
        }
    }

    override fun findByTaskId(tryOnTaskId: Long): List<TryOnTaskOutput> {
        return tryOnTaskOutputRepository.listByTaskId(tryOnTaskId)
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenShiftFaceTaskEvent(event: ShiftFaceTaskEvent) {
        val shiftFaceTask = event.getTask()
        if (shiftFaceTask.sourceType != BizSourceEnum.TRY_ON_OUTPUT.code
            || shiftFaceTask.algorithms != AlgorithmEnum.JV.code
        ) {
            return
        }
        RedissonHelper.lockExec("TryOnTaskOutput:ShiftFaceTask", shiftFaceTask.taskId) {
            val statusEnum = event.taskStatus
            val groups = tryOnTaskOutputRepository.listByTaskId(shiftFaceTask.parentTaskId!!)
            if (groups.isEmpty()) {
                return@lockExec
            }
            val shiftEnum = when (statusEnum) {
                TaskStatusEnum.QUEUEING, TaskStatusEnum.GENERATING -> {
                    ShiftFaceEnum.SHIFTING
                }

                TaskStatusEnum.COMPLETED -> {
                    checkCompletedShift(groups, shiftFaceTask, false)
                }

                else -> {
                    failedShift(groups, shiftFaceTask, false)
                }
            }
            if (shiftEnum.code == groups.first().shiftState) {
                return@lockExec
            }
            groups.forEach { op ->
                if (shiftEnum != ShiftFaceEnum.COMPLETED) {
                    op.message = shiftFaceTask.message
                }
                op.shiftState = shiftEnum.code
                op.revisedTime = LocalDateTime.now()
            }
            withTranExec {
                tryOnTaskOutputRepository.updateBatchByIdManualFill(groups)
                afterCommitExec {
                    checkShiftProgress(groups.first().taskId!!, true)
                }
            }
        }
    }


    override fun checkShiftProgress(tryOnTaskId: Long): ShiftFaceEnum? {
        return checkShiftProgress(tryOnTaskId, false)
    }


    fun chooseShiftSceneService(scenePictureEncode: ScenePictureEncodeVo?): ShiftSceneTaskService? {
        //默认潮际
        if (scenePictureEncode == null) {
            return shiftSceneServiceMap[AlgorithmEnum.CJ.name]
        }
        if (TryOnModeEnum.fromCj(scenePictureEncode.modeCode)) {
            return shiftSceneServiceMap[AlgorithmEnum.CJ.name]
        }
        if (TryOnModeEnum.fromLaz(scenePictureEncode.modeCode)) {
            return shiftSceneServiceMap[AlgorithmEnum.LAZADA.name]
        }

        return null

    }


    override fun findShiftSceneThirdIdList(tryOnTask: TryOnTask): List<String> {
        if (tryOnTask.scenePicturePath.isBlank()) {
            return listOf()
        }
        val scenePictureEncode: ScenePictureEncodeVo? = tryOnTask.scenePictureEncode.parseObj()
        val shiftSceneService = chooseShiftSceneService(scenePictureEncode) ?: return listOf()
        val outputList = tryOnTaskOutputRepository.listByTaskId(tryOnTask.taskId!!)
        if (outputList.isEmpty()) {
            return listOf()
        }
        val shiftSceneTaskList =
            shiftSceneService.getBySource(outputList.map { it.outputId!! }, BizSourceEnum.TRY_ON_OUTPUT.code)
        return shiftSceneTaskList.mapNotNull { it.thirdTaskId }

    }

    override fun checkShiftSceneProgress(tryOnTask: TryOnTask): ShiftSceneEnum {
        if (tryOnTask.scenePicturePath.isBlank()) {
            return ShiftSceneEnum.COMPLETED
        }
        val outputList = tryOnTaskOutputRepository.listByTaskId(tryOnTask.taskId!!)
        if (outputList.isEmpty()) {
            return ShiftSceneEnum.COMPLETED
        }
        val scenePictureEncode: ScenePictureEncodeVo? = tryOnTask.scenePictureEncode.parseObj()
        val shiftSceneService = chooseShiftSceneService(scenePictureEncode) ?: return ShiftSceneEnum.COMPLETED
        val shiftSceneTaskMap =
            shiftSceneService.getBySource(outputList.map { it.outputId!! }, BizSourceEnum.TRY_ON_OUTPUT.code)
                .associateBy { it.sourceId }
        val shiftSceneEnumList: List<ShiftSceneEnum> = outputList.map { op ->
            try {
                if (op.shiftSceneState == ShiftSceneEnum.FAILED.code) {
                    ShiftSceneEnum.FAILED
                } else {
                    val ssTask = shiftSceneTaskMap[op.outputId!!]
                    if (ssTask == null) {
                        val shiftSceneTaskReq = if (shiftSceneService.algorithm() == AlgorithmEnum.CJ) {
                            packCJShiftSceneTaskReq(tryOnTask, op)
                        } else {
                            packLazShiftSceneTaskReq(tryOnTask, op)
                        }
                        shiftSceneService.manualCreate(shiftSceneTaskReq)
                        op.shiftSceneState = ShiftSceneEnum.SHIFTING.code
                        ShiftSceneEnum.SHIFTING
                    } else {
                        toShiftSceneEnum(ssTask.taskStatus!!).apply {
                            if (this == ShiftSceneEnum.COMPLETED) {
                                op.shiftSceneImg = ssTask.resImgs?.splitAndFirst()
                            }
                            op.shiftSceneState = this.code
                        }
                    }
                }
            } catch (e: Exception) {
                op.shiftSceneState = ShiftSceneEnum.FAILED.code
                ShiftSceneEnum.FAILED
            }
        }

        tryOnTaskOutputRepository.updateBatchByIdManualFill(outputList)
        if (shiftSceneEnumList.any { it == ShiftSceneEnum.SHIFTING }) {
            return ShiftSceneEnum.SHIFTING
        }
        if (shiftSceneEnumList.all { it == ShiftSceneEnum.COMPLETED }) {
            return ShiftSceneEnum.COMPLETED
        }
        if (shiftSceneEnumList.all { it == ShiftSceneEnum.FAILED }) {
            return ShiftSceneEnum.FAILED
        }
        return ShiftSceneEnum.COMPLETED_PARTIAL

    }


    fun packCJShiftSceneTaskReq(tryOnTask: TryOnTask, output: TryOnTaskOutput): ShiftSceneTaskReq {
        val scenePictureEncode: ScenePictureEncodeVo? = tryOnTask.scenePictureEncode.parseObj()
        val shiftSceneTaskReq = ShiftSceneTaskReq(
            parentTaskId = tryOnTask.taskId!!,
            source = BizSourceEnum.TRY_ON_OUTPUT,
            sourceId = output.outputId!!,
            srcImg = output.resImg,
            bgImg = tryOnTask.scenePicturePath,
            algorithms = AlgorithmEnum.CJ,
        ).apply {
            this.inferenceMode = "replica_gen"
            if (scenePictureEncode != null) {
                if (scenePictureEncode.encode.isNotBlank()) {
                    this.bgEncode = scenePictureEncode.encode
                    this.inferenceMode = "style_gen"
                }
            } else {
                //兼容一下旧的，后面删除这段代码，尝试获取场景图编码
                if (tryOnTask.scenePictureId != null) {
                    SceneInfoApi.getPictureInfo(tryOnTask.scenePictureId!!).also { pic ->
                        val picEncode =
                            pic.encodeList?.firstOrNull { TryOnModeEnum.fromCj(it.modeCode) }?.encode
                        if (picEncode.isNotBlank()) {
                            this.bgEncode = picEncode
                            this.inferenceMode = "style_gen"
                        }
                    }
                }
            }
            this.tenantId = tryOnTask.tenantId
            this.creatorId = tryOnTask.creatorId
            this.creatorName = tryOnTask.creatorName
        }
        return shiftSceneTaskReq
    }

    fun packLazShiftSceneTaskReq(tryOnTask: TryOnTask, output: TryOnTaskOutput): ShiftSceneTaskReq {
        val scenePictureEncode: ScenePictureEncodeVo = tryOnTask.scenePictureEncode.parseObj()!!
        val shiftSceneTaskReq = ShiftSceneTaskReq(
            parentTaskId = tryOnTask.taskId!!,
            source = BizSourceEnum.TRY_ON_OUTPUT,
            sourceId = output.outputId!!,
            srcImg = output.resImg,
            bgImg = tryOnTask.scenePicturePath,
            bgOutputSize = tryOnTask.scenePictureOutputSize,
            algorithms = AlgorithmEnum.LAZADA,
        ).apply {
            this.bgEncode = scenePictureEncode.encode.assertNotBlank("Lazada背景编码不能为空")
            this.tenantId = tryOnTask.tenantId
            this.creatorId = tryOnTask.creatorId
            this.creatorName = tryOnTask.creatorName
        }
        return shiftSceneTaskReq
    }

    fun toShiftSceneEnum(taskStatus: Int): ShiftSceneEnum {
        return when (taskStatus) {
            TaskStatusEnum.QUEUEING.code, TaskStatusEnum.GENERATING.code -> {
                ShiftSceneEnum.SHIFTING
            }

            TaskStatusEnum.COMPLETED.code -> {
                ShiftSceneEnum.COMPLETED
            }

            else -> {
                ShiftSceneEnum.FAILED
            }
        }
    }

    /*@Deprecated("不需要做手脚修复了，即将作废删掉")
    override fun checkHandFootRepairProgress(tryOnTask: TryOnTask): HandFootRepairEnum {
        val outputList = tryOnTaskOutputRepository.listByTaskId(tryOnTask.taskId!!)
        if (outputList.isEmpty()) {
            return HandFootRepairEnum.COMPLETED
        }
        val repairTaskMap =
            handFootRepairTaskService.getBySource(outputList.map { it.outputId!! }, BizSourceEnum.TRY_ON_OUTPUT.code)
                .associateBy { it.sourceId }
        val repairEnumList: List<HandFootRepairEnum> = outputList.map { op ->
            try {
                if (op.repairHandFootState == HandFootRepairEnum.FAILED.code) {
                    HandFootRepairEnum.FAILED
                } else {
                    val repairTask = repairTaskMap[op.outputId!!]
                    if (repairTask == null) {
                        val repairTaskReq = HandFootRepairTaskReq(
                            parentTaskId = tryOnTask.taskId!!,
                            source = BizSourceEnum.TRY_ON_OUTPUT,
                            sourceId = op.outputId!!,
                            algorithms = AlgorithmEnum.CJ,
                        ).apply {
                            this.inputImg = if (op.shiftSceneImg.isNotBlank()) {
                                op.shiftSceneImg
                            } else {
                                op.resImg
                            }
                            this.tenantId = tryOnTask.tenantId
                            this.creatorId = tryOnTask.creatorId
                            this.creatorName = tryOnTask.creatorName
                        }
                        handFootRepairTaskService.manualCreate(repairTaskReq)
                        op.repairHandFootState = HandFootRepairEnum.REPAIRING.code
                        HandFootRepairEnum.REPAIRING
                    } else {
                        toHandFootRepairEnum(repairTask.taskStatus!!).apply {
                            if (this == HandFootRepairEnum.COMPLETED) {
                                op.repairHandFootImg = repairTask.resImgs?.splitAndFirst()
                            }
                            op.repairHandFootState = this.code
                        }
                    }
                }
            } catch (e: Exception) {
                op.repairHandFootState = HandFootRepairEnum.FAILED.code
                HandFootRepairEnum.FAILED
            }
        }
        tryOnTaskOutputRepository.updateBatchByIdManualFill(outputList)
        if (repairEnumList.any { it == HandFootRepairEnum.REPAIRING }) {
            return HandFootRepairEnum.REPAIRING
        }
        if (repairEnumList.all { it == HandFootRepairEnum.COMPLETED }) {
            return HandFootRepairEnum.COMPLETED
        }
        if (repairEnumList.all { it == HandFootRepairEnum.FAILED }) {
            return HandFootRepairEnum.FAILED
        }
        return HandFootRepairEnum.COMPLETED_PARTIAL
    }*/

    fun toHandFootRepairEnum(taskStatus: Int): HandFootRepairEnum {
        return when (taskStatus) {
            TaskStatusEnum.QUEUEING.code, TaskStatusEnum.GENERATING.code -> {
                HandFootRepairEnum.REPAIRING
            }

            TaskStatusEnum.COMPLETED.code -> {
                HandFootRepairEnum.COMPLETED
            }

            else -> {
                HandFootRepairEnum.FAILED
            }
        }
    }

    private fun checkShiftProgress(tryOnTaskId: Long, publishEvent: Boolean): ShiftFaceEnum? {
        return RedissonHelper.lockExec("TryOnTaskOutput", tryOnTaskId) {
            val groups = tryOnTaskOutputRepository.listByTaskId(tryOnTaskId)
            if (groups.isEmpty() || !groups.first().needShiftFace()) {
                return@lockExec null
            }
            val first = groups.first()
            val shiftState = ShiftFaceEnum.of(first.shiftState!!)
            val checkShiftState = when (shiftState) {
                ShiftFaceEnum.YES -> {
                    val tryOnTask = tryOnTaskRepository.obtainById(groups.first().taskId!!)
                    tryShift(groups, tryOnTask.shiftFaceImgs!!.split(Constant.COMMA), true)
                }

                ShiftFaceEnum.SHIFTING -> {
                    //拉取修复任务
                    val shiftTask = shiftFaceTaskService.getByParentTaskId(first.taskId!!)
                    if (shiftTask == null) {
                        ShiftFaceEnum.SHIFTING
                    } else {
                        val taskStatus = shiftTask.taskStatus
                        if (TaskStatusEnum.processing(taskStatus)) {
                            ShiftFaceEnum.SHIFTING
                        } else if (TaskStatusEnum.completed(taskStatus)) {
                            checkCompletedShift(groups, shiftTask, true)
                        } else {//失败
                            failedShift(groups, shiftTask, true)
                        }
                    }
                }

                ShiftFaceEnum.COMPLETED -> {
                    ShiftFaceEnum.COMPLETED
                }

                else -> {
                    ShiftFaceEnum.FAILED
                }
            }
            if (publishEvent) {
                OpenaiTaskEventPublisher.publish(ShiftFaceStateEvent(tryOnTaskId, shiftState))
            }
            checkShiftState
        }


    }


    private fun checkCompletedShift(
        groups: List<TryOnTaskOutput>,
        shiftFaceTask: ShiftFaceTask,
        doUpdate: Boolean
    ): ShiftFaceEnum {
        val groupOutputList = groups.sortedBy { it.serialNum ?: 0 }
        val resImgList = shiftFaceTask.resImgs?.split(Constant.COMMA) ?: listOf()
        val shiftEnum = if (resImgList.size == groupOutputList.size) {
            ShiftFaceEnum.COMPLETED
        } else {
            ShiftFaceEnum.FAILED
        }
        groupOutputList.forEachIndexed { index, o ->
            o.message = shiftFaceTask.message
            if (shiftEnum == ShiftFaceEnum.COMPLETED) {
                o.shiftFaceImg = resImgList[index]
            } else {
                o.message = "换脸返回图片数量不一致"
            }
            o.shiftState = shiftEnum.code
        }
        if (doUpdate) {
            withTranExec {
                tryOnTaskOutputRepository.updateBatchByIdManualFill(groupOutputList)
            }
        }
        return shiftEnum
    }

    private fun failedShift(
        groups: List<TryOnTaskOutput>, shiftFaceTask: ShiftFaceTask,
        doUpdate: Boolean
    ): ShiftFaceEnum {
        groups.forEach { o ->
            o.message = shiftFaceTask.message
            o.shiftState = ShiftFaceEnum.FAILED.code
        }
        if (doUpdate) {
            withTranExec {
                tryOnTaskOutputRepository.updateBatchByIdManualFill(groups)
            }
        }
        return ShiftFaceEnum.FAILED
    }


    private fun tryShift(groups: List<TryOnTaskOutput>, faceImgList: List<String>, doUpdate: Boolean): ShiftFaceEnum {
        try {
            val req = TryOnConvert.packShiftFaceTaskReq(groups, faceImgList)
            val result = shiftFaceTaskService.manualCreate(req)
            return if (result.successful()) {
                ShiftFaceEnum.SHIFTING
            } else {
                groups.forEach { o ->
                    o.shiftState = ShiftFaceEnum.FAILED.code
                    o.message = "换脸任务失败：${result.message}"
                }
                if (doUpdate) {
                    withTranExec {
                        tryOnTaskOutputRepository.updateBatchByIdManualFill(groups)
                    }
                }
                ShiftFaceEnum.FAILED
            }
        } catch (e: Exception) {
            log.error { "shift face task create error:${e.stackTraceToString()}" }
            groups.forEach {
                it.shiftState = ShiftFaceEnum.FAILED.code
                it.message = "创建换脸任务失败"
            }
            if (doUpdate) {
                withTranExec {
                    tryOnTaskOutputRepository.updateBatchByIdManualFill(groups)
                }
            }
            return ShiftFaceEnum.FAILED
        }
    }


}
