package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.PictureMarkTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.PictureMarkProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.PictureMarkOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.PictureMarkConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.PictureMarkTask
import tech.tiangong.butted.openai.repository.PictureMarkTaskRepository
import tech.tiangong.butted.openai.service.PictureMarkTaskService
import tech.tiangong.butted.openai.vo.query.PictureMarkTaskQuery
import tech.tiangong.butted.openai.vo.req.PictureMarkTaskReq
import tech.tiangong.butted.openai.vo.resp.PictureMarkTaskVo

/**
 * 图片Mark任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class PictureMarkTaskServiceImpl(
    config: PictureMarkProperties,
    val pictureMarkTaskRepository: PictureMarkTaskRepository
) : TaskBaseSupportImpl<PictureMarkTask, PictureMarkOutputVo>(
    config,
    pictureMarkTaskRepository
), PictureMarkTaskService {

    init {
        modeEnum = AiTaskModeEnum.TRY_ON_MODEL_MARK
    }

    override fun create(req: PictureMarkTaskReq): Long {
        return PictureMarkConvert.buildPictureMarkTask(req).let {
            pictureMarkTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun detail(taskId: Long): PictureMarkTaskVo? {
        return pictureMarkTaskRepository.getById(taskId).let {
            PictureMarkConvert.packPictureMarkTaskVo(it)
        }
    }

    override fun buildTaskModelParams(task: PictureMarkTask): Any {
        return PictureMarkConvert.packPictureMarkModelReq(task)
    }

    override fun handlePullResult(
        task: PictureMarkTask,
        changeStateEnum: TaskStatusEnum,
        output: PictureMarkOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, PictureMarkTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: PictureMarkTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, PictureMarkTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return PictureMarkTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                pictureMarkTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: PictureMarkTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            pictureMarkTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
