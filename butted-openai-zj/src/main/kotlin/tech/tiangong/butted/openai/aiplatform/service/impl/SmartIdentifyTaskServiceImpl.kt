package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.lang.Assert
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.HandleStateEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.ClipLabelTaskVo
import tech.tiangong.butted.common.vo.SmartIdentifyTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.*
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.SmartIdentifyProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.SmartIdentifyOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.SmartIdentifyConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportAdapter
import tech.tiangong.butted.openai.common.IdentifyExtendAction
import tech.tiangong.butted.openai.common.SmartDesignRequire
import tech.tiangong.butted.openai.entity.ClipLabelTask
import tech.tiangong.butted.openai.entity.FlowerPatternLabelTask
import tech.tiangong.butted.openai.entity.SmartIdentifyTask
import tech.tiangong.butted.openai.entity.StyleLabelTask
import tech.tiangong.butted.openai.entity.base.BaseTask
import tech.tiangong.butted.openai.entity.base.SubTask
import tech.tiangong.butted.openai.repository.SmartIdentifyTaskRepository
import tech.tiangong.butted.openai.service.*
import tech.tiangong.butted.openai.vo.query.SmartIdentifyTaskQuery
import tech.tiangong.butted.openai.vo.req.*
import tech.tiangong.butted.openai.vo.resp.FlowerPatternLabelTaskVo
import tech.tiangong.butted.openai.vo.resp.SmartIdentifyInfoForDesignVo
import tech.tiangong.butted.openai.vo.resp.StyleLabelTaskVo

/**
 * 智能识别任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class SmartIdentifyTaskServiceImpl(
    config: SmartIdentifyProperties,
    val smartIdentifyTaskRepository: SmartIdentifyTaskRepository,
    val clipLabelTaskService: ClipLabelTaskService,
    val styleLabelTaskService: StyleLabelTaskService,
    val flowerPatternLabelTaskService: FlowerPatternLabelTaskService,
    val fabricRecommendTaskService: FabricRecommendTaskService,
) : TaskSyncSupportAdapter<SmartIdentifyTask, SmartIdentifyOutputVo>(
    config,
    smartIdentifyTaskRepository
), SmartIdentifyTaskService {
    init {
        modeEnum = AiTaskModeEnum.SMART_IDENTIFY
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: SmartIdentifyCreateReq): Long {
        return SmartIdentifyConvert.build(req).let {
            smartIdentifyTaskRepository.save(it)
            doClipLabel(it)
            it.taskId!!
        }
    }

    override fun getByBusId(busId: Long): SmartIdentifyTaskVo {
        return packSmartIdentifyTaskVo(smartIdentifyTaskRepository.getByBusId(busId))
    }

    override fun getByTaskId(taskId: Long): SmartIdentifyTaskVo {
        return packSmartIdentifyTaskVo(smartIdentifyTaskRepository.obtainById(taskId))
    }


    private fun packSmartIdentifyTaskVo(task: SmartIdentifyTask): SmartIdentifyTaskVo {
        val clip: ClipLabelTaskVo? = clipLabelTaskService.getByParentTaskId(task.taskId!!, SourceEnum.C_SMART_IDENTIFY)
        val extendAction = task.toIdentifyExtend()
        /*val style: StyleLabelTaskVo? = extendAction.checkTodoStyleLabel {
            styleLabelTaskService.getByParentTaskId(task.taskId!!)
        }*/
        val flower: FlowerPatternLabelTaskVo? = extendAction.checkFlowerPatternLabel {
            flowerPatternLabelTaskService.getByParentTaskId(task.taskId!!)
        }
        return SmartIdentifyConvert.packSmartIdentifyTaskVo(task, clip, flower)!!
    }


    override fun detail(taskId: Long): SmartIdentifyTaskVo? {
        val task = smartIdentifyTaskRepository.getById(taskId) ?: return null
        val clip: ClipLabelTaskVo? = clipLabelTaskService.getByParentTaskId(task.taskId!!, SourceEnum.C_SMART_IDENTIFY)
        val extendAction = task.toIdentifyExtend()
        /*val style: StyleLabelTaskVo? = extendAction.checkTodoStyleLabel {
            if (SourceEnum.fromFSmartDesign(task.source)) {
                null
            } else {
                styleLabelTaskService.getByParentTaskId(task.taskId!!)
            }
        }*/
        val flower: FlowerPatternLabelTaskVo? = extendAction.checkFlowerPatternLabel {
            if (SourceEnum.fromFSmartDesign(task.source)) {
                null
            } else {
                flowerPatternLabelTaskService.getByParentTaskId(task.taskId!!)
            }
        }
        return SmartIdentifyConvert.packSmartIdentifyTaskVo(task, clip, flower)?.apply {
            if (SourceEnum.fromFSmartDesign(task.source)) {
                this.taskStatus = when (task.clipLabelAction) {
                    HandleStateEnum.PROCESSING.code -> TaskStatusEnum.GENERATING.code
                    HandleStateEnum.DONE.code -> TaskStatusEnum.COMPLETED.code
                    HandleStateEnum.FAILED.code -> TaskStatusEnum.FAILED.code
                    else -> TaskStatusEnum.FAILED.code
                }
                this.message = clip?.message
            }
        }

    }

    //重写拉取逻辑
    override fun handlePull(taskId: Long): Boolean {
        try {
            return lockExec(taskId) {
                val task = obtainById(taskId)
                checkSubTask(task, null)
                true
            }
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }

    }

    override fun obtainIdentifyInfoForDesign(require: SmartDesignRequire): SmartIdentifyInfoForDesignVo {
        val task = smartIdentifyTaskRepository.obtainById(require.smartIdentifyId)
        val designVo = task.copy(SmartIdentifyInfoForDesignVo::class)
        clipLabelTaskService.getByParentTaskId(task.taskId!!, SourceEnum.C_SMART_IDENTIFY).also {
            designVo.clipTask = it
        }
        var doSub = false
        /*require.checkTodoStyleLabel {
            val styleTaskVo = styleLabelTaskService.getByParentTaskId(task.taskId!!)
            designVo.styleTask = styleTaskVo
            if (styleTaskVo == null && (HandleStateEnum.waiting(task.styleLabelAction) || HandleStateEnum.nothing(task.styleLabelAction))) {
                task.styleLabelAction = HandleStateEnum.PROCESSING.code
                doStyleLabel(task)
                doSub = true
                task.addExtendAction("1")
            }
        }*/
        require.checkFlowerPatternLabel {
            val flowerTaskVo = flowerPatternLabelTaskService.getByParentTaskId(task.taskId!!)
            designVo.flowerTask = flowerTaskVo
            if (flowerTaskVo == null && (HandleStateEnum.waiting(task.flowerPatternLabelAction)
                        || HandleStateEnum.nothing(task.flowerPatternLabelAction))
            ) {
                task.flowerPatternLabelAction = HandleStateEnum.PROCESSING.code
                doFlowerPatternLabel(task)
                doSub = true
                task.addExtendAction("2")
            }

        }
        require.checkFabricRecommend {
            val fabricTaskVo =
                fabricRecommendTaskService.getSmartDesignFabricRecommendTaskVo(task.taskId!!, require.category)
            designVo.fabricTask = fabricTaskVo
            if (fabricTaskVo == null) {
                doFabricRecommend(task, require)
                doSub = true
                task.addExtendAction("4")
            }
        }
        if (doSub) {
            manualUpdateAiTaskInfo(task)
        }
        return designVo
    }

    override fun identifyAccuracy(taskId: Long, accuracy: Boolean) {
        val task = smartIdentifyTaskRepository.obtainById(taskId, "识别任务不存在")
        val clip: ClipLabelTaskVo = clipLabelTaskService.getByParentTaskId(task.taskId!!, SourceEnum.C_SMART_IDENTIFY)
            ?: throw RuntimeException("clip任务不存在")
        Assert.isTrue(TaskStatusEnum.completed(clip.taskStatus), "任务不是完成状态，不能标记准确性")
        task.accuracy = if (accuracy) {
            Bool.YES.code
        } else {
            Bool.NO.code
        }
        smartIdentifyTaskRepository.updateById(task)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        val task = smartIdentifyTaskRepository.getByBusId(busId)
        lockExec(task.taskId) {
            val extendAction = task.toIdentifyExtend()
            clipLabelTaskService.suspendByParentTaskId(task.taskId!!)
            /*extendAction.checkTodoStyleLabel {
                styleLabelTaskService.suspendByParentTaskId(task.taskId!!)
            }*/
            extendAction.checkFlowerPatternLabel {
                flowerPatternLabelTaskService.suspendByParentTaskId(task.taskId!!)
            }
            if (TaskStatusEnum.processing(task.taskStatus)) {
                task.suspend()
                smartIdentifyTaskRepository.updateById(task)
            }
        }
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenClipLabelTaskEvent(event: ClipLabelTaskEvent) {
        val clip = event.getTask()
        if (!SourceEnum.fromSmartIdentify(clip.source)) {
            return
        }
        lockToCheckSubTask(clip, clip.parentTaskId)

    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    @Deprecated(message = "去掉风格标签处理，since 2025年4月14日18:42:43")
    fun listenStyleLabelTask(event: StyleLabelTaskEvent) {
        /*val styleTask = event.getTask()
        lockToCheckSubTask(styleTask, styleTask.parentTaskId)*/
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    fun listenFlowerPatternLabelTask(event: FlowerPatternLabelTaskEvent) {
        val flowerTask = event.getTask()
        lockToCheckSubTask(flowerTask, flowerTask.parentTaskId)
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    fun listenFabricRecommendTask(event: FabricRecommendTaskEvent) {
        val subTask = event.getTask()
        if (!SourceEnum.fromSmartIdentify(subTask.source)) {
            return
        }
        val taskStatus = event.taskStatus
        lockExec(subTask.parentTaskId) {
            if (!taskStatus.finished()) {
                return@lockExec
            }
            val task = smartIdentifyTaskRepository.getById(subTask.parentTaskId!!) ?: return@lockExec
            task.addExtendAction("4")
            manualUpdateAiTaskInfo(task, SmartIdentifyTaskEvent(task, subTask, AiTaskModeEnum.FABRIC_RECOMMEND))
        }

    }


    private fun lockToCheckSubTask(subTask: BaseTask, parentTaskId: Long?) {
        if (parentTaskId == null) {
            return
        }
        lockExec(parentTaskId) {
            smartIdentifyTaskRepository.getById(parentTaskId)?.also { task ->
                checkSubTask(task, subTask)
            }
        }
    }

    private fun checkSubTask(task: SmartIdentifyTask, subTask: BaseTask? = null) {
        try {
            val clipHandle = handleClipTask(task, subTask)
            if (clipHandle.failed() || clipHandle.processing()) {
                return
            }
            //clip成功了，检查其他子任务

            //去掉风格标签处理，since 2025年4月14日18:42:43
//            val styleHandle = handleStyleLabelTask(task, subTask)
//            if (styleHandle.failed()) {
//                return
//            }

            //检查花型标签
            val flowerHandle = handleFlowerPatternLabelTask(task, subTask)
            //失败任务结束
            if (flowerHandle.failed()) {
                return
            }
            val identifyCompleted = clipHandle.nothingOrCompleted()
                    //&& styleHandle.nothingOrCompleted()
                    && flowerHandle.nothingOrCompleted()
            task.waitSync()
            if (identifyCompleted) {
                task.completed()
            }
            val event = when (subTask) {
                is ClipLabelTask -> {
                    SmartIdentifyTaskEvent(task, subTask, AiTaskModeEnum.CLIP_VIT_L_14)
                }

                /*is StyleLabelTask -> {
                    SmartIdentifyTaskEvent(task, subTask, AiTaskModeEnum.CLIP_STYLE_LABEL)
                }*/

                is FlowerPatternLabelTask -> {
                    SmartIdentifyTaskEvent(task, subTask, AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL)
                }

                else -> {
                    null
                }
            }
            manualUpdateAiTaskInfo(task, event)
        } catch (e: Exception) {
            log.error { "[${modeEnum.name}]checkSubTask失败,\t${e.stackTraceToString()}" }
            handleException(task, e.message ?: "处理Clip失败", AiTaskModeEnum.CLIP_VIT_L_14)
        }

    }


    private fun handleClipTask(task: SmartIdentifyTask, subTask: BaseTask? = null): HandleStateEnum {
        try {
            val handleState = HandleStateEnum.of(task.clipLabelAction!!)
            if (handleState.finished()) {
                return handleState
            }
            val clipTaskVo = if (subTask != null && subTask is ClipLabelTask) {
                subTask.copy(ClipLabelTaskVo::class)
            } else {
                clipLabelTaskService.getByParentTaskId(task.taskId!!, SourceEnum.C_SMART_IDENTIFY)
                    ?: throw TaskException("clip标签任务创建失败", AiTaskModeEnum.CLIP_VIT_L_14, TaskStatusEnum.FAILED)
            }
            val statusEnum = TaskStatusEnum.of(clipTaskVo.taskStatus!!)
            if (statusEnum.processing()) {
                task.clipLabelAction = HandleStateEnum.PROCESSING.code
                return HandleStateEnum.PROCESSING
            }
            //失败或者终止了
            if (statusEnum.failedOrCanceled()) {
                task.clipLabelAction = HandleStateEnum.FAILED.code
                handleException(task, clipTaskVo.message, AiTaskModeEnum.CLIP_VIT_L_14, statusEnum)
                return HandleStateEnum.FAILED
            }
            //clip成功
            task.refImgType = clipTaskVo.inputImageType
            task.category = clipTaskVo.category
            task.categoryCode = clipTaskVo.categoryCode
            task.styleType = clipTaskVo.styleType
            task.extendActions = clipTaskVo.extendActions
            task.taskStatus = TaskStatusEnum.GENERATING.code
            task.clipLabelAction = HandleStateEnum.DONE.code
            return HandleStateEnum.DONE
        } catch (e: Exception) {
            log.error { "[${modeEnum.name}]checkClipTask失败,\t${e.stackTraceToString()}" }
            task.clipLabelAction = HandleStateEnum.FAILED.code
            handleException(task, e.message ?: "处理Clip失败", AiTaskModeEnum.CLIP_VIT_L_14)
            return HandleStateEnum.FAILED
        }
    }

    @Deprecated("去掉风格标签处理，since 2025年4月14日18:42:43，即将删除")
    private fun handleStyleLabelTask(task: SmartIdentifyTask, subTask: BaseTask? = null): HandleStateEnum {
        try {
            val styleTaskVo = if (subTask != null && subTask is StyleLabelTask) {
                task.addExtendAction("1")
                subTask.copy(StyleLabelTaskVo::class)
            } else {
                val extendAction = task.toIdentifyExtend()
                if (!extendAction.needStyleLabel()) {
                    task.styleLabelAction = HandleStateEnum.NOTHING.code
                    return HandleStateEnum.NOTHING
                }
                val handleState = HandleStateEnum.of(task.styleLabelAction!!)
                if (handleState.waiting() || handleState.nothing()) {
                    task.styleLabelAction = HandleStateEnum.PROCESSING.code
                    doStyleLabel(task)
                    return HandleStateEnum.PROCESSING
                }
                if (handleState.finished()) {
                    return handleState
                }
                styleLabelTaskService.getByParentTaskId(task.taskId!!)
                    ?: throw TaskException(
                        "style标签任务创建失败",
                        AiTaskModeEnum.CLIP_STYLE_LABEL,
                        TaskStatusEnum.FAILED
                    )
            }
            val statusEnum = TaskStatusEnum.of(styleTaskVo.taskStatus!!)
            if (statusEnum.processing()) {
                task.styleLabelAction = HandleStateEnum.PROCESSING.code
                return HandleStateEnum.PROCESSING
            }
            //失败或者终止了
            if (statusEnum.failedOrCanceled()) {
                task.styleLabelAction = HandleStateEnum.FAILED.code
                handleException(task, styleTaskVo.message, AiTaskModeEnum.CLIP_STYLE_LABEL, statusEnum)
                return HandleStateEnum.FAILED
            }
            //款式标签成功
            task.styleLabelAction = HandleStateEnum.DONE.code
            return HandleStateEnum.DONE
        } catch (e: Exception) {
            log.error { "[${modeEnum.name}]checkStyleLabelTask失败,\t${e.stackTraceToString()}" }
            task.styleLabelAction = HandleStateEnum.FAILED.code
            handleException(task, e.message ?: "处理StyleLabel失败", AiTaskModeEnum.CLIP_STYLE_LABEL)
            return HandleStateEnum.FAILED
        }
    }

    private fun handleFlowerPatternLabelTask(task: SmartIdentifyTask, subTask: BaseTask? = null): HandleStateEnum {
        try {
            val flowerTaskVo = if (subTask != null && subTask is FlowerPatternLabelTask) {
                task.addExtendAction("2")
                subTask.copy(FlowerPatternLabelTaskVo::class)
            } else {
                val extendAction = task.toIdentifyExtend()
                if (!extendAction.needFlowerPatternLabel()) {
                    task.flowerPatternLabelAction = HandleStateEnum.NOTHING.code
                    return HandleStateEnum.NOTHING
                }
                val handleState = HandleStateEnum.of(task.flowerPatternLabelAction!!)
                if (handleState.waiting() || handleState.nothing()) {
                    task.flowerPatternLabelAction = HandleStateEnum.PROCESSING.code
                    doFlowerPatternLabel(task)
                    return HandleStateEnum.PROCESSING
                }
                if (handleState.finished()) {
                    return handleState
                }
                flowerPatternLabelTaskService.getByParentTaskId(task.taskId!!)
                    ?: throw TaskException(
                        "flower标签任务创建失败",
                        AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL,
                        TaskStatusEnum.FAILED
                    )
            }
            val statusEnum = TaskStatusEnum.of(flowerTaskVo.taskStatus!!)
            if (statusEnum.processing()) {
                task.flowerPatternLabelAction = HandleStateEnum.PROCESSING.code
                return HandleStateEnum.PROCESSING
            }
            //失败或者终止了
            if (statusEnum.failedOrCanceled()) {
                task.flowerPatternLabelAction = HandleStateEnum.FAILED.code
                handleException(task, flowerTaskVo.message, AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL, statusEnum)
                return HandleStateEnum.FAILED
            }
            //款式标签成功
            task.flowerPatternLabelAction = HandleStateEnum.DONE.code
            return HandleStateEnum.DONE
        } catch (e: Exception) {
            log.error { "[${modeEnum.name}]checkFlowerPatternLabelTask失败,\t${e.stackTraceToString()}" }
            task.flowerPatternLabelAction = HandleStateEnum.FAILED.code
            handleException(task, e.message ?: "处理StyleLabel失败", AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL)
            return HandleStateEnum.FAILED
        }
    }


    fun doClipLabel(task: SmartIdentifyTask): Long {
        val req = ClipLabelTaskCreateReq(
            parentTaskId = task.taskId,
            source = SourceEnum.C_SMART_IDENTIFY,
            taskAttribute = task.taskAttribute
        ).apply {
            this.inputImage = task.refImgUrl
        }
        return clipLabelTaskService.create(req)
    }

    @Deprecated("去掉风格标签处理，since 2025年4月14日18:42:43，即将删除")
    fun doStyleLabel(task: SmartIdentifyTask): Long {
        return userInvoke(task) {
            val req = StyleLabelTaskReq(
                inputImage = task.refImgUrl,
                parentTaskId = task.taskId,
                taskAttribute = task.taskAttribute
            )
            styleLabelTaskService.create(req)
        }
    }

    fun doFlowerPatternLabel(task: SmartIdentifyTask): Long {
        return userInvoke(task) {
            val req = FlowerPatternLabelTaskReq(
                parentTaskId = task.taskId,
                inputImage = task.refImgUrl,
                taskAttribute = task.taskAttribute
            )
            flowerPatternLabelTaskService.create(req)
        }
    }

    override fun checkTodoExtendActions(require: SmartDesignRequire) {
        lockExec(require.smartIdentifyId) {
            val task = smartIdentifyTaskRepository.obtainById(require.smartIdentifyId)
            var designExtendActionFinish = true
            /*require.checkTodoStyleLabel {
                if (HandleStateEnum.waiting(task.styleLabelAction) || HandleStateEnum.nothing(task.styleLabelAction)) {
                    task.styleLabelAction = HandleStateEnum.PROCESSING.code
                    doStyleLabel(task)
                    designExtendActionFinish = false
                    task.addExtendAction("1")
                }
            }*/
            require.checkFlowerPatternLabel {
                if (HandleStateEnum.waiting(task.flowerPatternLabelAction) || HandleStateEnum.nothing(task.flowerPatternLabelAction)) {
                    task.flowerPatternLabelAction = HandleStateEnum.PROCESSING.code
                    doFlowerPatternLabel(task)
                    designExtendActionFinish = false
                    task.addExtendAction("2")
                }
            }
            require.checkFabricRecommend {
                fabricRecommendTaskService.getSmartDesignFabricRecommendTask(task.taskId!!, require.category).also {
                    if (it == null) {
                        doFabricRecommend(task, require)
                        //designExtendActionFinish = false
                        task.addExtendAction("4")
                    }
                }
            }
            if (designExtendActionFinish) {
                manualUpdateAiTaskInfo(task, SmartIdentifyTaskEvent<SubTask>(task))
            } else {
                manualUpdateAiTaskInfo(task)
            }
        }

    }

    fun doFabricRecommend(task: SmartIdentifyTask, require: SmartDesignRequire): Long {
        return userInvoke(task) {
            val req = FabricRecommendTaskReq(
                sourceEnum = SourceEnum.C_SMART_IDENTIFY,
                inputImage = task.refImgUrl,
                category = require.category,
                flowered = require.styleType,
                parentTaskId = task.taskId,
                taskAttribute = task.taskAttribute
            )
            fabricRecommendTaskService.create(req)
        }

    }


    /*private fun listenSubTask(event: OpenaiTaskEvent<SubTask>, subModeEnum: AiTaskModeEnum) {
        val subTask = event.getTask()
        val taskStatus = event.taskStatus
        lockExec(subTask.parentTaskId) {
            val task = smartIdentifyTaskRepository.getById(subTask.parentTaskId!!) ?: return@lockExec
            try {
                if (!taskStatus.finished()) {
                    return@lockExec
                }
                when (subTask) {
                    is FlowerPatternLabelTask -> {
                        if (taskStatus.completed()) {
                            task.flowerPatternLabelAction = HandleStateEnum.DONE.code
                        } else {
                            task.flowerPatternLabelAction = HandleStateEnum.FAILED.code
                        }
                    }

                    is StyleLabelTask -> {
                        if (taskStatus.completed()) {
                            task.styleLabelAction = HandleStateEnum.DONE.code
                        } else {
                            task.styleLabelAction = HandleStateEnum.FAILED.code
                        }
                    }

                }
                //失败或者终止了
                if (!taskStatus.completed()) {
                    handleException(task, subTask.message, subModeEnum, taskStatus)
                    return@lockExec
                }
                checkSmartIdentifyActionCompleted(
                    task,
                    SmartIdentifyTaskEvent(task, subTask, subModeEnum)
                )
            } catch (e: Exception) {
                log.error { "[${subModeEnum.name}]listenSubTask[${subTask.javaClass.simpleName}]失败,\t${e.stackTraceToString()}" }
                handleException(task, e.message ?: "处理[${subTask.javaClass.simpleName}]失败", subModeEnum)
            }
        }
    }*/

    /*fun checkSmartIdentifyActionCompleted(task: SmartIdentifyTask, event: OpenaiTaskEvent<*>? = null) {
        var identifyActionCompleted = true
        val extendAction = task.toIdentifyExtend()
        extendAction.checkTodoStyleLabel {
            if (!HandleStateEnum.completed(task.styleLabelAction)) {
                identifyActionCompleted = false
            }
        }
        extendAction.checkFlowerPatternLabel {
            if (!HandleStateEnum.completed(task.flowerPatternLabelAction)) {
                identifyActionCompleted = false
            }
        }
        if (TaskStatusEnum.processing(task.taskStatus)) {
            task.waitSync()
        }
        if (identifyActionCompleted) {
            task.completed()
        }
        manualUpdateAiTaskInfo(task, event)
    }*/


    fun handleException(
        task: SmartIdentifyTask,
        message: String?,
        modeEnum: AiTaskModeEnum,
        taskStatus: TaskStatusEnum? = null
    ) {
        val curStatus = taskStatus ?: TaskStatusEnum.FAILED
        task.message = message
        if (curStatus.finished()) {
            task.stopAiEndTime()
            if (curStatus.failedOrCanceled()) {
                task.failTaskMode = modeEnum.fullMode()
            }
        }
        task.waitSync()
        task.taskStatus = curStatus.code
        manualUpdateAiTaskInfo(task)
    }


    override fun doSync(task: SmartIdentifyTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return SmartIdentifyTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                smartIdentifyTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: SmartIdentifyTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (!SourceEnum.needSync(task.source)) {
            //不需要同步
            task.synced()
        }
        withTranExec {
            smartIdentifyTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
            callbackBusiness(task)
        }
    }

    fun SmartIdentifyTask.toIdentifyExtend(): IdentifyExtendAction {
        return IdentifyExtendAction(
            extendActions = this.extendActions,
            styleType = this.styleType
        )
    }


    //扩展处理:1-风格,2-花型识别,3-多姿势,4-面料识别及推荐,5-花型提取,6-场景,7-模特,8-Try换装
    fun SmartIdentifyTask.addExtendAction(extendAction: String): Boolean {
        if (this.extendActions.isBlank()) {
            this.extendActions = extendAction
            return true
        }
        if (this.extendActions!!.contains(extendAction)) {
            return false
        }
        this.extendActions = "${this.extendActions}${Constant.COMMA}$extendAction"
        return true
    }

    override fun handleTimeout(task: SmartIdentifyTask) {
        throw UnsupportedOperationException("暂不支持超时处理，已经监听子任务事件")
    }
}

