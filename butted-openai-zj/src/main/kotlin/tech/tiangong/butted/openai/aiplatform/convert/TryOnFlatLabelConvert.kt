package tech.tiangong.butted.openai.aiplatform.convert

import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJsonList
import tech.tiangong.butted.common.vo.PredLabelVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.openai.aiplatform.aimodel.req.TryOnFlatLabelModelReq
import tech.tiangong.butted.openai.entity.TryOnFlatLabelTask
import tech.tiangong.butted.openai.vo.req.TryOnFlatLabelTaskReq
import tech.tiangong.butted.openai.vo.resp.TryOnFlatLabelTaskVo
import tech.tiangong.butted.oss.toFullImgPath

@Slf4j
object TryOnFlatLabelConvert : PredLabelConvert {

    fun buildTryOnFlatLabelTask(req: TryOnFlatLabelTaskReq): TryOnFlatLabelTask {
        return TryOnFlatLabelTask().apply {
            this.manualInitBaseTask(req)
            this.inputImage = req.inputImage.toFullImgPath()
            this.parentTaskId = req.parentTaskId
            this.taskAttribute = req.taskAttribute
        }
    }

    fun packTryOnFlatLabelModelReq(task: TryOnFlatLabelTask): TryOnFlatLabelModelReq {
        return TryOnFlatLabelModelReq(
            inputImage = task.inputImage,
        )
    }

    fun packTryOnFlatLabelTaskVo(task: TryOnFlatLabelTask?): TryOnFlatLabelTaskVo? {
        return task?.copy(TryOnFlatLabelTaskVo::class)?.apply {
            predLabelList = task.usableLabels?.parseJsonList(PredLabelVo::class.java)
        }
    }


}


