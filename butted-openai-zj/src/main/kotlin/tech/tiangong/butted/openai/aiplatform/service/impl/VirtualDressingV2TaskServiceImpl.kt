package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.VirtualDressingV2TaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.VirtualDressingV2TaskCreateVo
import tech.tiangong.butted.common.vo.VirtualDressingV2TaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.ContentType
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.VirtualDressingV2Properties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.VirtualDressingV2ModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.VirtualDressingV2Vo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.VirtualDressingV2Convert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.VirtualDressingV2Task
import tech.tiangong.butted.openai.repository.VirtualDressingV2TaskRepository
import tech.tiangong.butted.openai.service.VirtualDressingV2TaskService
import tech.tiangong.butted.openai.vo.query.VirtualDressingV2TaskQuery
import tech.tiangong.butted.oss.upload
import tech.tiangong.butted.util.UserInvoke

/**
 * VirtualDressingV2任务 Service
 *
 */
@Slf4j
@Service
class VirtualDressingV2TaskServiceImpl(
    config: VirtualDressingV2Properties,
    private val transactionalManager: TransactionalManager,
    private val virtualDressingV2TaskRepository: VirtualDressingV2TaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<VirtualDressingV2Task, VirtualDressingV2Vo>(
    config,
    virtualDressingV2TaskRepository
), VirtualDressingV2TaskService {
    override fun buildTaskModelParams(task: VirtualDressingV2Task): VirtualDressingV2ModelReq = VirtualDressingV2Convert.convert(task)

    override fun handlePullResult(
        task: VirtualDressingV2Task,
        changeStateEnum: TaskStatusEnum,
        output: VirtualDressingV2Vo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                it.comfyuiHistoryResult?.let {
                    val historyDto = it
                    //转成阿里云oss存储 裂变的先不转
                    historyDto.youchuanMjUrls= historyDto.youchuanMjUrls?.split(Regex("[,\\n]+"))?.map {
                        it.upload(ContentType.IMAGE_PNG)
                    }?.toJson()
                    historyDto.chaojiTryOnUrls = historyDto.chaojiTryOnUrls?.split(Regex("[,\\n]+"))?.map {
                        it.upload(ContentType.IMAGE_PNG)
                    }?.toJson()
                    historyDto.chaojiTryOnKontextUrls = historyDto.chaojiTryOnKontextUrls?.split(Regex("[,\\n]+"))?.toJson()
                    historyDto.huiwaTryOnUrls = historyDto.huiwaTryOnUrls?.split(Regex("[,\\n]+"))?.map {
                        it.upload(ContentType.IMAGE_PNG)
                    }?.toJson()
                    historyDto.huiwaTryOnKontextUrls = historyDto.huiwaTryOnKontextUrls?.split(Regex("[,\\n]+"))?.toJson()
                    historyDto.lazadaTryOnUrls = historyDto.lazadaTryOnUrls?.split(Regex("[,\\n]+"))?.map {
                        it.upload(ContentType.IMAGE_PNG)
                    }?.toJson()
                    historyDto.lazadaTryOnKontextUrls = historyDto.lazadaTryOnKontextUrls?.split(Regex("[,\\n]+"))?.toJson()
                }
            }
            task.comfyuiHistoryResult = output?.comfyuiHistoryResult?.toJson()
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: VirtualDressingV2Task, canceled: Boolean) {
        log.info { "logo_det任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> = VirtualDressingV2TaskQuery()
        .let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                virtualDressingV2TaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }

    override fun doSync(task: VirtualDressingV2Task) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: VirtualDressingV2TaskReq): List<VirtualDressingV2TaskCreateVo> {
        val data = this.virtualDressingV2TaskRepository.listByBusIds(req.taskInfoList.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { VirtualDressingV2TaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        val list = req.taskInfoList.map {
            VirtualDressingV2Convert.convert(it).apply {
                this.callback = req.callback
            }
        }
        batchCreateHandle.create(
            list,
            { virtualDressingV2TaskRepository.saveBatch(list, list.size) },
            { doPush(it) }) {
            virtualDressingV2TaskRepository.updateBatchById(list, list.size)
        }
        return list.map { VirtualDressingV2TaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
    }

    override fun listByIds(ids: List<Long>): List<VirtualDressingV2TaskVo> =
        task2VO(this.virtualDressingV2TaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<VirtualDressingV2TaskVo> =
        task2VO(this.virtualDressingV2TaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<VirtualDressingV2Task>) = if (data.isNotEmpty()) {
        data.map { VirtualDressingV2Convert.toVO(it) }
    } else {
        listOf()
    }

    private fun manualUpdateAiTaskInfo(task: VirtualDressingV2Task) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            virtualDressingV2TaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    init {
        modeEnum = AiTaskModeEnum.COMFYUI
    }
}