package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.ComfyuiTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.ComfyuiTaskCreateVo
import tech.tiangong.butted.common.vo.ComfyuiTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ComfyuiProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.ComfyuiModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.ComfyuiVo
import tech.tiangong.butted.openai.aiplatform.convert.ComfyuiConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.ComfyuiTask
import tech.tiangong.butted.openai.repository.ComfyuiTaskRepository
import tech.tiangong.butted.openai.service.ComfyuiTaskService
import tech.tiangong.butted.openai.vo.query.ComfyuiTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * comfyui任务 Service
 *
 * <AUTHOR>
 * @date       ：2025/2/17 11:21
 * @version    :1.0
 */
@Slf4j
@Service
class ComfyuiTaskServiceImpl(
    config: ComfyuiProperties,
    private val comfyuiTaskRepository: ComfyuiTaskRepository
) : TaskSyncSupportImpl<ComfyuiTask, ComfyuiVo>(
    config,
    comfyuiTaskRepository
), ComfyuiTaskService {
    override fun doSync(task: ComfyuiTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun buildTaskModelParams(task: ComfyuiTask): ComfyuiModelReq = ComfyuiConvert.convert(task)

    override fun handlePullResult(
        task: ComfyuiTask,
        changeStateEnum: TaskStatusEnum,
        output: ComfyuiVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: ComfyuiTask, canceled: Boolean) {
        log.info { "comfyui任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> = ComfyuiTaskQuery()
        .let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                comfyuiTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }


    override fun batchCreate(req: CompanyUserBatchReq<ComfyuiTaskReq>): List<ComfyuiTaskCreateVo> =
        UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val data = req.data.map {
                ComfyuiConvert.convert(it)
                    .apply {
                        this.callback = req.callback
                    }
            }
            batchSave(data)
        }

    override fun batchCreateInner(req:List<ComfyuiTaskReq>): List<ComfyuiTaskCreateVo> {
        val data = req.map {
                ComfyuiConvert.convert(it)
                    .apply {}
            }
        return batchSave(data)
    }

    fun batchSave(tasks : List<ComfyuiTask>): List<ComfyuiTaskCreateVo>{
            withTranExec {
                comfyuiTaskRepository.saveBatch(tasks, tasks.size)
            }
            tasks.forEach {
                try {
                    doPush(it)
                    it.firstPushed()
                } catch (e: Exception) {
                    log.error { e }
                    it.pushFailed()
                    it.message = e.message
                    it.taskStatus = TaskStatusEnum.FAILED.code
                }
                it.waitSync()
            }
            withTranExec {
                comfyuiTaskRepository.updateBatchById(tasks, tasks.size)
            }
            return tasks.map { ComfyuiTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }

    override fun listByIds(ids: List<Long>): List<ComfyuiTaskVo> =
        task2VO(this.comfyuiTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<ComfyuiTaskVo> =
        task2VO(this.comfyuiTaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<ComfyuiTask>) = if (data.isNotEmpty()) {
        data.map { ComfyuiConvert.toVO(it) }
    } else {
        listOf()
    }

    fun manualUpdateAiTaskInfo(task: ComfyuiTask) {
        task.refreshRevisedTime()
        withTranExec {
            comfyuiTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    init {
        modeEnum = AiTaskModeEnum.COMFY_UI
    }
}