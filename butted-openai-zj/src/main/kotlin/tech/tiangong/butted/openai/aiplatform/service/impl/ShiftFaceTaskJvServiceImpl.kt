package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pojo.CreatedResult
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.ShiftFaceTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ShiftFaceProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.ShiftFaceOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.ShiftFaceConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.ShiftFaceTask
import tech.tiangong.butted.openai.repository.ShiftFaceTaskRepository
import tech.tiangong.butted.openai.service.ShiftFaceTaskService
import tech.tiangong.butted.openai.vo.query.ShiftFaceTaskQuery
import tech.tiangong.butted.openai.vo.req.ShiftFaceTaskReq

/**
 * 换脸任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class ShiftFaceTaskJvServiceImpl(
    config: ShiftFaceProperties,
    val shiftFaceTaskRepository: ShiftFaceTaskRepository
) : TaskBaseSupportImpl<ShiftFaceTask, ShiftFaceOutputVo>(
    config,
    shiftFaceTaskRepository
), ShiftFaceTaskService {
    init {
        modeEnum = AiTaskModeEnum.SHIFT_FACE
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: ShiftFaceTaskReq): CreatedResult {
        return try {
            ShiftFaceConvert.buildShiftFaceTask(req).let {
                shiftFaceTaskRepository.saveManualFill(it)
                CreatedResult.success(doPush(it))
            }
        } catch (e: Exception) {
            val stackTraceToString = e.stackTraceToString()
            log.error { "创建换脸任务失败：${stackTraceToString}" }
            CreatedResult.fail(stackTraceToString)
        }
    }

    override fun buildTaskModelParams(task: ShiftFaceTask): Any {
        return ShiftFaceConvert.packShiftFaceModelReq(task)
    }

    override fun getByParentTaskId(parentTaskId: Long): ShiftFaceTask? {
        return shiftFaceTaskRepository.getByParentTaskId(parentTaskId)
    }

    override fun getBySource(sourceId: Long, sourceType: String): ShiftFaceTask? {
        return shiftFaceTaskRepository.getBySource(sourceId, sourceType)
    }

    override fun handlePullResult(
        task: ShiftFaceTask,
        changeStateEnum: TaskStatusEnum,
        output: ShiftFaceOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.output_urls?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, ShiftFaceTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: ShiftFaceTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, ShiftFaceTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByParentTaskId(parentTaskId: Long) {
        shiftFaceTaskRepository.getByParentTaskId(parentTaskId)?.also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return ShiftFaceTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.algorithms = algorithm().code
                it.pageNum = curPageNum
                shiftFaceTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: ShiftFaceTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            shiftFaceTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }

    override fun algorithm(): AlgorithmEnum {
        return AlgorithmEnum.JV
    }
}
