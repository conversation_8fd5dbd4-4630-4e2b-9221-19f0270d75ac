package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.joinToStr
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.WrinkleEliminateProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.WrinkleEliminateModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.WrinkleEliminateOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.WrinkleEliminateConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.WrinkleEliminateTask
import tech.tiangong.butted.openai.repository.WrinkleEliminateTaskRepository
import tech.tiangong.butted.openai.service.WrinkleEliminateTaskService
import tech.tiangong.butted.openai.vo.query.WrinkleEliminateTaskQuery
import tech.tiangong.butted.openai.vo.req.WrinkleEliminateTaskReq
import tech.tiangong.butted.openai.vo.resp.WrinkleEliminateTaskVo

/**
 * 褶皱消除任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class WrinkleEliminateTaskServiceImpl(
    config: WrinkleEliminateProperties,
    val wrinkleEliminateTaskRepository: WrinkleEliminateTaskRepository
) : TaskBaseSupportImpl<WrinkleEliminateTask, WrinkleEliminateOutputVo>(
    config,
    wrinkleEliminateTaskRepository
), WrinkleEliminateTaskService {
    init {
        modeEnum = AiTaskModeEnum.WRINKLE_ELIMINATE
    }


    override fun detail(taskId: Long): WrinkleEliminateTaskVo? {
        val task = wrinkleEliminateTaskRepository.getById(taskId)
        return task?.copy(WrinkleEliminateTaskVo::class)?.apply {
            resImg = task.resImgs?.splitAndFirst()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: WrinkleEliminateTaskReq): Long {
        return WrinkleEliminateConvert.buildWrinkleEliminateTask(req).let {
            wrinkleEliminateTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun buildTaskModelParams(task: WrinkleEliminateTask): WrinkleEliminateModelReq {
        return WrinkleEliminateConvert.packWrinkleEliminateModelReq(task)
    }

    override fun handlePullResult(
        task: WrinkleEliminateTask,
        changeStateEnum: TaskStatusEnum,
        output: WrinkleEliminateOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.resImgs = it.resImgs?.joinToStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: WrinkleEliminateTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return WrinkleEliminateTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                wrinkleEliminateTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: WrinkleEliminateTask) {
        task.refreshRevisedTime()
        withTranExec {
            wrinkleEliminateTaskRepository.updateByIdManualFill(task)
        }
    }
}
