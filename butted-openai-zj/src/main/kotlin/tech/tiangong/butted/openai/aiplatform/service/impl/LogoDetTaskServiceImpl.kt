package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.LogoDetTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.LogoDetTaskCreateVo
import tech.tiangong.butted.common.vo.LogoDetTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.LogoDetProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.LogoDetModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.LogoDetVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.LogoDetConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.LogoDetTask
import tech.tiangong.butted.openai.repository.LogoDetTaskRepository
import tech.tiangong.butted.openai.service.LogoDetTaskService
import tech.tiangong.butted.openai.vo.query.LogoDetTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * logo_det任务 Service
 *
 * <AUTHOR>
 * @date       ：2025/3/31 18:03
 * @version    :1.0
 */
@Slf4j
@Service
class LogoDetTaskServiceImpl(
    config: LogoDetProperties,
    private val transactionalManager: TransactionalManager,
    private val logoDetTaskRepository: LogoDetTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<LogoDetTask, LogoDetVo>(
    config,
    logoDetTaskRepository
), LogoDetTaskService {
    override fun buildTaskModelParams(task: LogoDetTask): LogoDetModelReq = LogoDetConvert.convert(task)

    override fun handlePullResult(
        task: LogoDetTask,
        changeStateEnum: TaskStatusEnum,
        output: LogoDetVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                task.outputBoxes = it.outputBoxes.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: LogoDetTask, canceled: Boolean) {
        log.info { "logo_det任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> = LogoDetTaskQuery()
        .let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                logoDetTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }

    override fun doSync(task: LogoDetTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: CompanyUserBatchReq<LogoDetTaskReq>): List<LogoDetTaskCreateVo> {
        val data = this.logoDetTaskRepository.listByBusIds(req.data.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { LogoDetTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val list = req.data.map {
                LogoDetConvert.convert(it).apply {
                    this.callback = req.callback
                }
            }
            batchCreateHandle.create(
                list,
                { logoDetTaskRepository.saveBatch(list, list.size) },
                { doPush(it) }) {
                logoDetTaskRepository.updateBatchById(list, list.size)
            }
            list.map { LogoDetTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<LogoDetTaskVo> =
        task2VO(this.logoDetTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<LogoDetTaskVo> =
        task2VO(this.logoDetTaskRepository.listByBusIds(ids))

    private fun task2VO(data: List<LogoDetTask>) = if (data.isNotEmpty()) {
        data.map { LogoDetConvert.toVO(it) }
    } else {
        listOf()
    }

    private fun manualUpdateAiTaskInfo(task: LogoDetTask) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            logoDetTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    init {
        modeEnum = AiTaskModeEnum.LOGO_DET
    }
}