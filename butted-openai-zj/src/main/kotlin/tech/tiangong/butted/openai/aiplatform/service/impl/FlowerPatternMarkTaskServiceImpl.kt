package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.dto.RectangleCoordinateDto
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.RectangleCoordinateVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.FlowerPatternMarkProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternMarkModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.FlowerPatternMarkOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.FlowerPatternMarkConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.FlowerPatternMarkTask
import tech.tiangong.butted.openai.repository.FlowerPatternMarkTaskRepository
import tech.tiangong.butted.openai.service.FlowerPatternMarkTaskService
import tech.tiangong.butted.openai.vo.query.FlowerPatternMarkTaskQuery
import tech.tiangong.butted.openai.vo.req.FlowerPatternMarkTaskReq
import tech.tiangong.butted.openai.vo.resp.FlowerPatternMarkTaskVo

/**
 * 花型mark任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class FlowerPatternMarkTaskServiceImpl(
    config: FlowerPatternMarkProperties,
    val flowerPatternMarkTaskRepository: FlowerPatternMarkTaskRepository
) : TaskBaseSupportImpl<FlowerPatternMarkTask, FlowerPatternMarkOutputVo>(
    config,
    flowerPatternMarkTaskRepository
), FlowerPatternMarkTaskService {

    init {
        modeEnum = AiTaskModeEnum.FLOWER_PATTERN_MARK
    }


    override fun detail(taskId: Long): FlowerPatternMarkTaskVo {
        val task = flowerPatternMarkTaskRepository.obtainById(taskId)
        return task.copy(FlowerPatternMarkTaskVo::class).apply {
            coordsList = task.coords?.parseJsonList(RectangleCoordinateVo::class.java)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: FlowerPatternMarkTaskReq): Long {
        return FlowerPatternMarkConvert.buildFlowerPatternMarkTask(req).let {
            flowerPatternMarkTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun buildTaskModelParams(task: FlowerPatternMarkTask): FlowerPatternMarkModelReq {
        return FlowerPatternMarkConvert.packFlowerPatternMarkModelReq(task)
    }

    override fun handlePullResult(
        task: FlowerPatternMarkTask,
        changeStateEnum: TaskStatusEnum,
        output: FlowerPatternMarkOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                val coords = it.polygons?.filter { cl -> cl.isNotEmpty() && cl.size == 4 }
                    ?.map { cl ->
                        RectangleCoordinateDto(
                            xmin = cl[0], ymin = cl[1],
                            xmax = cl[2], ymax = cl[3]
                        )
                    }
                task.coords = coords?.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: FlowerPatternMarkTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return FlowerPatternMarkTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                flowerPatternMarkTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: FlowerPatternMarkTask) {
        task.refreshRevisedTime()
        withTranExec {
            flowerPatternMarkTaskRepository.updateByIdManualFill(task)
        }
    }

}
