package tech.tiangong.butted.openai.aiplatform.service.impl

import org.springframework.beans.factory.DisposableBean
import org.springframework.beans.factory.InitializingBean
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.holder.CurrentUserHolder
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.tryExec
import tech.tiangong.butted.enums.QueueStatusEnum
import tech.tiangong.butted.enums.TaskTypeEnum
import tech.tiangong.butted.event.FlowerPatternExtractTaskEvent
import tech.tiangong.butted.event.SmartDesignTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.convert.TaskQueueConvert
import tech.tiangong.butted.openai.aiplatform.pojo.TaskQueueInfo
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.repository.TaskDurationRepository
import tech.tiangong.butted.openai.repository.TaskQueueRepository
import tech.tiangong.butted.openai.service.TaskQueueService
import tech.tiangong.butted.openai.vo.query.TaskQueueQuery
import tech.tiangong.butted.openai.vo.resp.TaskCompletedQueueVo
import tech.tiangong.butted.openai.vo.resp.TaskProcessingQueueVo
import tech.tiangong.butted.openai.vo.resp.TaskQueueNoticeVo
import tech.tiangong.butted.openai.vo.resp.TaskQueueVo
import java.time.LocalDateTime
import java.util.concurrent.PriorityBlockingQueue

/**
 * 任务队列服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TaskQueueServiceImpl(
    val taskQueueRepository: TaskQueueRepository,
    val taskDurationRepository: TaskDurationRepository,
) : TaskQueueService, InitializingBean, DisposableBean {
    private var stop: Boolean = false
    private val taskQueue = PriorityBlockingQueue<TaskQueueInfo<SyncTask>>()

    override fun getById(queueId: Long): TaskQueueVo {
        return TaskQueueVo()
    }

    override fun myselfProcessingList(): TaskQueueNoticeVo {
        val currentUser = CurrentUserHolder.get()
        val query = TaskQueueQuery().apply {
            pageSize = 10
            tenantId = currentUser.tenantId
            creatorId = currentUser.id
            queueStatusList = listOf(QueueStatusEnum.QUEUEING.code, QueueStatusEnum.GENERATING.code)
            afterCreatedTime = LocalDateTime.now().minusDays(7)
            descOrder("queue_status")
            ascOrder("rank_position")
        }
        val page = taskQueueRepository.findPage(query)
        if (page.records.isEmpty()) {
            return TaskQueueNoticeVo()
        }
        val groupQueueMap = page.records.groupBy { it.type!! }
        val typeSet = groupQueueMap.keys
        val durationMap = taskDurationRepository.findByType(typeSet).associateBy { it.type }

        val list = mutableListOf<TaskProcessingQueueVo>()
        val queuingList = mutableListOf<TaskProcessingQueueVo>()
        groupQueueMap.forEach { (type, tgl) ->
            val taskDuration = durationMap[type]
            val qslMap = tgl.groupBy { it.queueStatus!! }
            qslMap.forEach { (status, qsl) ->
                if (status == QueueStatusEnum.QUEUEING.code) {
                    queuingList.addAll(
                        TaskQueueConvert.packRankingEstimateTimeQueue(qsl, type, status, taskDuration)
                    )
                }
                if (status == QueueStatusEnum.GENERATING.code) {
                    list.addAll(
                        TaskQueueConvert.packGeneratingEstimateTimeQueue(qsl, type, status, taskDuration)
                    )
                }
            }
        }
        list.addAll(queuingList)
        /*list = list.sortedWith { o1, o2 ->
            val status2 = o2.taskStatus!!
            val status1 = o1.taskStatus!!
            if (status2 != status1) {
                return@sortedWith status2.compareTo(status1)
            }
            val es1 = o1.estimateTime ?: Int.MAX_VALUE
            val es2 = o2.estimateTime ?: Int.MAX_VALUE
            if (es1 != es2) {
                es1.compareTo(es2)
            } else {
                0
            }
        }*/
        return TaskQueueNoticeVo().apply {
            total = page.total
            show = list.size.toLong()
            hide = total!! - show!!
            queueList = list
        }
    }

    override fun myselfCompletedList(): List<TaskCompletedQueueVo> {
        val currentUser = CurrentUserHolder.get()
        val query = TaskQueueQuery().apply {
            pageSize = 10
            tenantId = currentUser.tenantId
            creatorId = currentUser.id
            queueStatus = QueueStatusEnum.COMPLETED_WAITING_PUSH.code
            afterCreatedTime = LocalDateTime.now().minusDays(3)
            sortCreatedTimeAsc()
        }
        val page = taskQueueRepository.findPage(query)
        val list = page.records.map {
            it.copy(TaskCompletedQueueVo::class).apply {
                typeName = TaskTypeEnum.of(type!!).desc
            }
        }
        val queueMap = list.groupBy { it.type!! }
        queueMap.forEach { (type, tl) ->
            try {
                val taskIdList = tl.map { it.taskId!! }
                withTranExec {
                    taskQueueRepository.completedPushed(taskIdList, type)
                }
            } catch (e: Exception) {
                log.error { "更新任务队列推送状态失败：${e.message}\n ${e.stackTraceToString()}" }
            }
        }
        return list
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenFlowerPatternExtractTaskEvent(event: FlowerPatternExtractTaskEvent) {
        tryExec {
            val task = event.getTask()
            taskQueue.offer(
                TaskQueueInfo(
                    task = task,
                    type = TaskTypeEnum.FLOWER_PATTERN_EXTRACT
                )
            )
        }
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenSmartDesignTaskEvent(event: SmartDesignTaskEvent) {
        tryExec {
            val task = event.getTask()
            taskQueue.offer(
                TaskQueueInfo(
                    task = task,
                    type = TaskTypeEnum.SMART_DESIGN
                )
            )
        }
    }

    fun processingQueue() {
        val queueInfo = taskQueue.take()
        val task = queueInfo.task
        var queue = taskQueueRepository.getByTaskIdAndType(task.taskId!!, queueInfo.type.code)
        if (queue == null) {
            queue = TaskQueueConvert.buildTaskQueue(task, queueInfo.type)
            withTranExec {
                taskQueueRepository.saveManualFill(queue)
            }
            return
        }
        if (QueueStatusEnum.finished(queue.queueStatus)) {
            return
        }
        TaskQueueConvert.updateTaskQueue(queue, task)
        withTranExec {
            taskQueueRepository.updateByIdManualFill(queue)
        }
    }

    //@PostConstruct
    override fun afterPropertiesSet() {
        Thread {
            while (!stop) {
                try {
                    processingQueue()
                } catch (e: Throwable) {
                    log.error { "处理任务队列失败：${e.message}\n ${e.stackTraceToString()}" }
                }
            }
        }.start()
    }

    //@PreDestroy
    override fun destroy() {
        stop = true
    }


}

