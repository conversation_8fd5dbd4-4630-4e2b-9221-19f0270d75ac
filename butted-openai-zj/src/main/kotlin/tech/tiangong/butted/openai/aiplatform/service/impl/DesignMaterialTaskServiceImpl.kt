package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.DesignMaterialTaskReq
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.DesignMaterialTaskVo
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.enums.*
import tech.tiangong.butted.event.RepairStateEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.DesignMaterialProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.DesignMaterialModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.pkg.output.DesignMaterialOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.DesignMaterialConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.extend.validToJson
import tech.tiangong.butted.openai.aiplatform.extend.validToRefImgModelReq
import tech.tiangong.butted.openai.aiplatform.remote.RestAiPlatformApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.DesignMaterialTask
import tech.tiangong.butted.openai.repository.DesignMaterialOutputRepository
import tech.tiangong.butted.openai.repository.DesignMaterialTaskRepository
import tech.tiangong.butted.openai.service.*
import tech.tiangong.butted.openai.vo.query.DesignMaterialTaskQuery
import tech.tiangong.butted.openai.vo.req.PictureCaptionApiReq
import tech.tiangong.butted.util.UserInvoke
import java.time.LocalDateTime

/**
 * AI素材任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class DesignMaterialTaskServiceImpl(
    config: DesignMaterialProperties,
    val designMaterialOutputService: DesignMaterialOutputService,
    val pictureCaptionTaskService: PictureCaptionTaskService,
    val designMaterialTaskRepository: DesignMaterialTaskRepository,
    val designMaterialOutputRepository: DesignMaterialOutputRepository
) : TaskSyncSupportImpl<DesignMaterialTask, DesignMaterialOutputVo>(
    config,
    designMaterialTaskRepository
), DesignMaterialTaskService {
    init {
        modeEnum = AiTaskModeEnum.DESIGN_MATERIAL
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: DesignMaterialTaskReq): Long {
        val task = DesignMaterialConvert.build(req)
        designMaterialTaskRepository.save(task)
        afterCommitExec {
            executor.delayExecute(1000L * 10) {
                checkToPush(task.taskId!!)
            }
        }
        return task.taskId!!
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: DesignMaterialTaskReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }


    override fun getByBusId(busId: Long): DesignMaterialTaskVo {
        val task = designMaterialTaskRepository.getByBusId(busId)
        return packSmartDesignTaskVo(task)
    }

    private fun packSmartDesignTaskVo(task: DesignMaterialTask): DesignMaterialTaskVo {
        val callbackStatus = getCallbackStatus(task)
        val outputList = if (callbackStatus.completed()) {
            designMaterialOutputRepository.listByTaskId(task.taskId!!)
        } else {
            listOf()
        }
        return DesignMaterialConvert.packSmartDesignTaskVo(task, callbackStatus, outputList)
    }

    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查脸部修复是否完成
                if (TaskStatusEnum.completed(task.taskStatus)) {
                    handleRepairProgress(task).run {
                        println("AI素材任务[${task.taskId}]脸部修复进度：${this.desc}")
                    }
                    return@lockExec
                }
                //检查推送
                if (task.pushStatus == PushStatusEnum.UN_PUSH.code) {
                    checkToPush(taskId)
                    return@lockExec
                }
                //检查超时
                if (task.checkTime() < LocalDateTime.now().minusSeconds(config.timeout + config.pullInterval)) {
                    handleTimeout(task)
                    return@lockExec
                }
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }
    }

    fun handleRepairProgress(task: DesignMaterialTask): FaceRepairEnum {
        val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
        if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
            return curFaceRepair
        }
        val checkRepairEnum: FaceRepairEnum =
            designMaterialOutputService.checkRepairProgress(task.taskId!!) ?: FaceRepairEnum.NO
        task.faceRepair = checkRepairEnum.code
        if (checkRepairEnum.notRequired() || checkRepairEnum.finished()) {
            task.waitSync()
            task.aiEndTime = LocalDateTime.now()
        }
        manualUpdateAiTaskInfo(task)
        return checkRepairEnum
    }


    override fun buildTaskModelParams(task: DesignMaterialTask): DesignMaterialModelReq {
        return DesignMaterialConvert.packDesignMaterialModelReq(task)
    }

    @Throws(Throwable::class)
    fun checkToPush(taskId: Long) {
        lockExec("CheckToPush:$taskId") {
            val task = designMaterialTaskRepository.obtainById(taskId)
            if (!PushStatusEnum.unPushed(task.pushStatus)) {
                return@lockExec
            }
            withTranExec {
                try {
                    checkSceneCaptions(task)
                    checkModelMaterialCaptions(task)
                    doPush(task)
                    task.waitSync().firstPushed()
                    manualUpdateAiTaskInfo(task)
                } catch (e: Throwable) {
                    log.error { "推送AI素材任务失败：${e.stackTraceToString()}" }
                    task.waitSync().pushFailed()
                    task.taskStatus = TaskStatusEnum.FAILED.code
                    task.message = e.message.blankDefault("推送任务失败")
                    manualUpdateAiTaskInfo(task)
                }
            }
        }

    }


    @Throws(Exception::class)
    fun checkSceneCaptions(task: DesignMaterialTask) {
        val sceneInfo = task.bgImgInfo.validToRefImgModelReq()
        if (sceneInfo == null || sceneInfo.captions.isNotBlank()) {
            return
        }
        val apiReq = PictureCaptionApiReq(source = SourceEnum.A_SCENE_PICTURE, inputImg = sceneInfo.url!!)
        val captionText = pictureCaptionTaskService.callCaptionApi(apiReq)
        if (captionText.isBlank()) {
            throw TaskException(
                "场景图描述任务未生成结果",
                AiTaskModeEnum.CHAT_GPT_PICTURE_CAPTION
            )
        }
        sceneInfo.captions = captionText
        task.bgImgInfo = sceneInfo.validToJson()
    }

    @Throws(Exception::class)
    fun checkModelMaterialCaptions(task: DesignMaterialTask) {
        //模特人种不需要获取模特面容描述
        if (task.modelCode.isNotBlank()) {
            return
        }
        if (task.modelUrl.isBlank() || task.modelCaption.isNotBlank()) {
            return
        }
        val apiReq = PictureCaptionApiReq(source = SourceEnum.A_MODEL_MATERIAL, inputImg = task.modelUrl!!)
        val captionText = pictureCaptionTaskService.callCaptionApi(apiReq)
        if (captionText.isBlank()) {
            throw TaskException(
                "模特面容图描述任务未生成结果",
                AiTaskModeEnum.CHAT_GPT_PICTURE_CAPTION
            )
        }
        task.modelCaption = captionText
    }

    override fun handlePullResult(
        task: DesignMaterialTask,
        changeStateEnum: TaskStatusEnum,
        output: DesignMaterialOutputVo?,
        completed: Boolean,
        message: String?
    ) {

        withTranExec {
            var checkStateEnum = changeStateEnum
            if (completed) {
                if (output == null || output.resImgs.isEmpty()) {
                    checkStateEnum = TaskStatusEnum.FAILED
                    task.message = "AI生图结果为空"
                } else {
                    val outputReq = DesignMaterialConvert.packDesignMaterialOutputReq(task, output.resImgs!!)
                    designMaterialOutputService.manualSave(outputReq)
                    task.prompts = output.prompts
                    task.modelVersion = output.modelVersion
                    task.taskProgress = 100
                    if (task.needFaceRepair()) {
                        task.faceRepair = FaceRepairEnum.REPAIRING.code
                    }
                }
            }
            /*if (task.taskStatus != checkStateEnum.code) {
                if (!(checkStateEnum.completed() && task.needFaceRepair())) {
                    task.waitSync()
                }
            }*/
            if (task.taskStatus != checkStateEnum.code) {
                task.waitSync()
            }
            task.taskStatus = checkStateEnum.code
            if (checkStateEnum.failedOrCanceled()) {
                task.failTaskMode = AiTaskModeEnum.DESIGN_MATERIAL.fullMode()
            }
            manualUpdateAiTaskInfo(task)
        }
    }


    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenRepairStateEvent(event: RepairStateEvent) {
        try {
            if (event.sourceEnum != SourceEnum.A_DESIGN_MATERIAL) {
                return
            }
            lockExec(event.getTaskId()) {
                val task = obtainById(event.getTaskId())
                val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
                if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
                    return@lockExec
                }
                if (event.repairEnum.finished()) {
                    task.waitSync()
                    task.aiEndTime = LocalDateTime.now()
                }
                task.faceRepair = event.repairEnum.code
                manualUpdateAiTaskInfo(task)
            }
        } catch (e: Exception) {
            log.error { "监听脸部修复状态失败：\n ${e.stackTraceToString()}" }
        }
    }

    override fun handleTimeoutResult(task: DesignMaterialTask, canceled: Boolean) {
        task.waitSync()
        task.failTaskMode = AiTaskModeEnum.DESIGN_MATERIAL.fullMode()
        manualUpdateAiTaskInfo(task)
    }


    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return DesignMaterialTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                designMaterialTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    override fun doSync(task: DesignMaterialTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = designMaterialTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步AI素材任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        designMaterialTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: DesignMaterialTask) {
        task.refreshRevisedTime()
        withTranExec {
            designMaterialTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    /**
     * 事务提交后回调业务
     */
    override fun callbackBusiness(task: DesignMaterialTask) {
        if (!needAsync(task)) {
            return
        }
        afterCommitExec {
            val cloneTask = task.copy(DesignMaterialTask::class)
            cloneTask.taskStatus = getCallbackStatus(task).code
            RestAiPlatformApi.callback(cloneTask, modeEnum).also {
                super.fillSyncStatus(task, modeEnum, it)
            }
            designMaterialTaskRepository.updateByIdManualFill(task)
        }
    }

    fun getCallbackStatus(task: DesignMaterialTask): TaskStatusEnum {
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus != TaskStatusEnum.COMPLETED) {
            return curStatus
        }
        //如果不需要修复脸部，或者修复完成，直接返回
        val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
        if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
            return curStatus
        }
        return TaskStatusEnum.GENERATING
    }

}
