package tech.tiangong.butted.openai.aiplatform.service.impl

import cn.hutool.core.collection.CollectionUtil
import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.PtnMarkTaskReq
import tech.tiangong.butted.common.req.base.CompanyUserBatchReq
import tech.tiangong.butted.common.vo.PtnMarkTaskCreateVo
import tech.tiangong.butted.common.vo.PtnMarkTaskVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.jdbc.TransactionalManager
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.PtnMarkProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.PtnMarkModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.PtnMarkVo
import tech.tiangong.butted.openai.aiplatform.component.BatchCreateHandle
import tech.tiangong.butted.openai.aiplatform.convert.PtnMarkConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.PtnMarkTask
import tech.tiangong.butted.openai.repository.PtnMarkTaskRepository
import tech.tiangong.butted.openai.service.PtnMarkTaskService
import tech.tiangong.butted.openai.vo.query.PtnMarkTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * PtnMarkTaskServiceImpl
 *
 * <AUTHOR>
 * @date       ：2025/4/23 19:04
 * @version    :1.0
 */
@Slf4j
@Service
class PtnMarkTaskServiceImpl(
    config: PtnMarkProperties,
    private val transactionalManager: TransactionalManager,
    private val ptnMarkTaskRepository: PtnMarkTaskRepository,
    private val batchCreateHandle: BatchCreateHandle,
) : TaskSyncSupportImpl<PtnMarkTask, PtnMarkVo>(
    config,
    ptnMarkTaskRepository
), PtnMarkTaskService {
    override fun buildTaskModelParams(task: PtnMarkTask): PtnMarkModelReq = PtnMarkConvert.convert(task)

    override fun handlePullResult(
        task: PtnMarkTask,
        changeStateEnum: TaskStatusEnum,
        output: PtnMarkVo?,
        completed: Boolean,
        message: String?
    ) {
        task.responseData = output?.toJson()
        if (completed) {
            output?.let {
                val result = it.result
                if (CollectionUtil.isNotEmpty(result)) {
                    task.inferenceResult = result[0].inferenceResult?.toJson()
                }else {
                    log.info { "ptn_mark任务回调返回结果为空\t${output.toJson()}" }
                }
            }
        }
        task.taskStatus = changeStateEnum.code
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: PtnMarkTask, canceled: Boolean) {
        log.info { "ptn_mark任务超时回调处理\t${task.toJson()}" }
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> =
        PtnMarkTaskQuery()
            .let {
                if (it.adaptActionQuery(taskAction)) {
                    it.pageNum = curPageNum
                    ptnMarkTaskRepository.findPage(it)
                } else {
                    emptyPage()
                }
            }

    override fun doSync(task: PtnMarkTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun batchCreate(req: CompanyUserBatchReq<PtnMarkTaskReq>): List<PtnMarkTaskCreateVo> {
        val data = this.ptnMarkTaskRepository.listByBusIds(req.data.map { it.busId ?: 0 })
        if (CollectionUtil.isNotEmpty(data)) {
            return data.map { PtnMarkTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
        return UserInvoke.doAction(req.creatorId, req.creatorName, req.companyId) {
            val list = req.data.map {
                PtnMarkConvert.convert(it).apply {
                    this.callback = req.callback
                }
            }
            batchCreateHandle.create(
                list,
                { ptnMarkTaskRepository.saveBatch(list, list.size) },
                { doPush(it) }) {
                ptnMarkTaskRepository.updateBatchById(list, list.size)
            }
            list.map { PtnMarkTaskCreateVo(it.busId ?: 0, it.taskId ?: 0) }
        }
    }

    override fun listByIds(ids: List<Long>): List<PtnMarkTaskVo> =
        task2VO(this.ptnMarkTaskRepository.listByIds(ids))

    override fun listByBusIds(ids: List<Long>): List<PtnMarkTaskVo> =
        task2VO(this.ptnMarkTaskRepository.listByBusIds(ids))

    private fun manualUpdateAiTaskInfo(task: PtnMarkTask) {
        task.refreshRevisedTime()
        transactionalManager.exec {
            ptnMarkTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }

    private fun task2VO(data: List<PtnMarkTask>) = if (data.isNotEmpty()) {
        data.map { PtnMarkConvert.toVO(it) }
    } else {
        listOf()
    }

    init {
        modeEnum = AiTaskModeEnum.PTN_MARK
    }
}