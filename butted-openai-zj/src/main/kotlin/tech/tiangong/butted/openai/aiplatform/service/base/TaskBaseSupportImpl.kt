package tech.tiangong.butted.openai.aiplatform.service.base

import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.util.Assert
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.pool.ButtedThreadPoolExecutor
import tech.tiangong.butted.core.reflect.ParameterizedTypeImpl
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.ofInstant
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.publish.OpenaiTaskEventPublisher
import tech.tiangong.butted.exception.RemoteException
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.openai.aiplatform.aimodel.enums.AiModelStatusEnum
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelBriefDataVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelDataVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelOutputVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelResponse
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.remote.RestAiPlatformApi
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.*
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository
import tech.tiangong.butted.openai.vo.query.base.TaskCommonQuery
import tech.tiangong.butted.redis.RedissonHelper
import tech.tiangong.butted.util.UserInvoke
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import java.time.LocalDateTime
import java.util.concurrent.Future
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

/**
 * TaskBaseSupportImpl
 */
@Slf4j
abstract class TaskBaseSupportImpl<T : BaseTask, O : TaskModelOutputVo>(
    val config: OpenAiTaskConfig,
    val manualBaseRepository: ManualBaseRepository<*, T>
) : ZjTaskBaseSupport<T> {
    @Autowired
    protected lateinit var jvTaskPriorityHandle: JvTaskPriorityHandle
    private var outputType: Class<O>? = null
    lateinit var modeEnum: AiTaskModeEnum

    protected val executor = ButtedThreadPoolExecutor.build(8, "jv-task-process")

    @Async
    override fun pull(): Future<Int> {
        return batchProcessing(TaskActionEnum.PULL) {
            val pullCount = loopDoHandle("循环拉取", TaskActionEnum.PULL) {
                handlePull(it)
            }
            pullCount
        }
    }

    override fun pull(taskIds: List<Long>) {
        taskIds.forEach { handlePull(it) }
    }

    open fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "拉取${modeEnum.name}任务失败：${e.stackTraceToString()}" }
            return false
        }
    }

    override fun notify(taskMode: String, taskId: String, jsonBody: String?): Boolean {
        try {
            return lockExec(taskId.toLong()) {
                if (jsonBody.isBlank()) {
                    doHandlePull(taskId.toLong())
                    return@lockExec true
                }
                val task = obtainById(taskId.toLong())
                val bodyType = object :
                    TypeReference<TaskModelDataVo<O>>() {
                    override fun getType(): Type {
                        return ParameterizedTypeImpl(
                            TaskModelDataVo::class.java,
                            getOutputType()
                        )
                    }
                }
                val dataVo = jsonBody!!.parseJson(bodyType)
                routeResult(task, TaskModelResponse.ok(dataVo))
                true
            }
        } catch (e: Exception) {
            log.error { "回调通知${modeEnum.name}任务失败：${e.stackTraceToString()}" }
            return false
        }

    }

    open fun doHandlePull(taskId: Long) {
        val task = obtainById(taskId)
        doHandlePull(task)
    }

    open fun doHandlePull(task: T) {
        //任务已结束
        if (!TaskStatusEnum.processing(task.taskStatus!!)) {
            return
        }
        //检查是否超时了
        if (checkTimeout(task)) {
            return
        }
        task.addPullTimes()
        val response = invokePullAiTask(task)
        routeResult(task, response)
    }

    open fun routeResult(task: T, response: TaskModelResponse<TaskModelDataVo<O>>) {
        if (TaskStatusEnum.finished(task.taskStatus)) {
            return
        }
        if (response.failed()) {
            log.error { "${modeEnum.name}任务拉取失败，${response.toJson()}" }
            return
        }
        val data = response.data
        if (data == null) {
            log.warn { "${modeEnum.name}任务拉取失败，没有DATA数据，${response.toJson()}" }
            return
        }
        task.message = response.message
        if (data.message.isNotBlank()) {
            task.message = data.message
        }
        val checkStateEnum = checkStateAndFillTaskInfo(task, data)
        if (checkStateEnum != null) {
            when (checkStateEnum) {
                TaskStatusEnum.COMPLETED -> {
                    handlePullResult(task, checkStateEnum, data.output, true, data.message)
                }

                TaskStatusEnum.GENERATING,
                TaskStatusEnum.QUEUEING,
                TaskStatusEnum.FAILED,
                TaskStatusEnum.TIMEOUT_FAILED,
                TaskStatusEnum.CANCELED -> {
                    handlePullResult(task, checkStateEnum, data.output, false, data.message)
                }

                else -> {
                    println("状态不支持，跳过")
                }
            }
        }

    }


    open fun checkTimeout(task: T): Boolean {
        //超时，没响应结果即取消
        val checkTime = task.checkTime()
        if (checkTime < LocalDateTime.now().minusSeconds(config.timeout + config.pullInterval)) {
            handleTimeout(task)
            return true
        }
        return false
    }


    open fun handleTimeout(task: T) {
        val canceled = invokeCancelAiTask(task.taskId!!)
        task.timeout()
        handleTimeoutResult(task, canceled)
    }

    /**
     * 推送任务到算法
     */
    open fun doPush(task: T): Long {
        return invokePushAiTask(task).let { resp ->
            if (resp.failed()) {
                log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                throw RemoteException(resp.message.blankDefault("推送任务失败"))
            }
            task.taskId!!
        }
    }


    fun <T> loopDoHandle(tag: String, taskAction: TaskActionEnum, handle: (taskId: Long) -> T): Int {
        val handleCount = AtomicInteger(0)
        val curPageNum = AtomicInteger(0)
        var hasNext: Boolean
        val stop = AtomicBoolean(false)
        do {
            val page: IPage<out BaseTaskVo> = loopPage(taskAction, curPageNum.incrementAndGet())
            val list: List<BaseTaskVo>? = page.records
            if (list.isNotEmpty()) {
                for (taskVo in list!!) {
                    try {
                        if (stop.get()) {
                            break
                        }
                        handle(taskVo.taskId!!)
                        handleCount.incrementAndGet()
                        try {
                            if (taskAction == TaskActionEnum.PULL) {
                                stop.set(stopLoopHandlePull(taskVo.taskId!!, handleCount.get()))
                            }
                        } catch (e: Exception) {
                            log.error { "检查StopLoopHandlePull失败：${e.stackTraceToString()}" }
                        }
                    } catch (e: Exception) {
                        log.error { "$tag【${taskVo.taskId}】${modeEnum.name}任务失败：${e.stackTraceToString()}" }
                    }
                }
            }
            //是否还有下一页
            hasNext = list.isNotEmpty() && page.pages > curPageNum.get()
        } while (!stop.get() && hasNext)
        return handleCount.get()
    }


    /**
     * 子类重写改方法停止循环处理任务，释放资源，默认不停止
     * 如果有一个任务正在生成中，则可以停止循环处理任务了，因为后面的肯定在排队中了
     */
    open fun stopLoopHandlePull(taskId: Long, handleCount: Int): Boolean {
        return handleCount > Random.nextLong(100, 10000)
    }

    fun emptyPage(): IPage<out BaseTaskVo> = Page.of(0, 0, 0)

    private fun checkStateAndFillTaskInfo(
        task: T,
        data: TaskModelDataVo<O>
    ): TaskStatusEnum? {
        task.taskProgress = data.progress
        task.aiStartTime = ofInstant(data.taskStartTime)
        task.aiEndTime = ofInstant(data.taskCompletionTime)
        //排队位置
        task.rankPosition = data.rank?.toInt()
        val now = LocalDateTime.now()
        data.output?.let {
            if (it.message.isNotBlank()) {
                task.message = it.message
            }
        }
        when (data.state) {
            AiModelStatusEnum.WAITE.code -> {
                return TaskStatusEnum.QUEUEING
            }

            AiModelStatusEnum.RUNNING.code -> {
                return TaskStatusEnum.GENERATING
            }

            AiModelStatusEnum.SUCCESS.code -> {
                task.stopAiEndTime(now)
                return TaskStatusEnum.COMPLETED
            }

            AiModelStatusEnum.CANCELED.code -> {
                //如果是处理中即更改为取消状态
                if (TaskStatusEnum.processing(task.taskStatus)) {
                    task.suspend()
                }
                task.stopAiEndTime(now)
                return TaskStatusEnum.CANCELED
            }

            AiModelStatusEnum.FAILED.code -> {
                task.stopAiEndTime(now)
                return TaskStatusEnum.FAILED
            }

            AiModelStatusEnum.TIMEOUT_FAILED.code -> {
                task.stopAiEndTime(now)
                task.message = AiModelStatusEnum.TIMEOUT_FAILED.desc
                return TaskStatusEnum.TIMEOUT_FAILED
            }

            else -> {
                log.warn { "UNKNOWN TaskStatusEnum: ${data.state}" }
                return null
            }
        }
    }


    /**
     * 构建任务参数，子类重写该方法，推送任务到算法时调用
     */
    abstract fun buildTaskModelParams(task: T): Any


    /**
     * 处理拉取结果，子类重写该方法处理任务结果，拉取到结果时调用
     */
    abstract fun handlePullResult(
        task: T,
        changeStateEnum: TaskStatusEnum,
        output: O?,
        completed: Boolean,
        message: String?
    )

    /**
     * 处理超时，子类重写该方法处理超时，超时时调用
     */
    abstract fun handleTimeoutResult(task: T, canceled: Boolean)


    //给子类重写，调用
    open fun handleException(task: T, message: String?, statusEnum: TaskStatusEnum? = TaskStatusEnum.FAILED) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        task.message = message
        task.stopAiEndTime()
        task.taskStatus = failStatus.code
    }

    override fun obtainById(taskId: Long): T {
        return manualBaseRepository.obtainById(taskId, "[${modeEnum.name}]任务不存在！")
    }


    fun afterPublishEvent(event: OpenaiTaskEvent<*>? = null) {
        event?.also {
            afterCommitExec {
                publishEvent(it)
            }
        }
    }

    fun publishEvent(event: OpenaiTaskEvent<*>) {
        OpenaiTaskEventPublisher.publish(event)
    }

    open fun invokePullAiTask(task: T): TaskModelResponse<TaskModelDataVo<O>> {
        return RestAiPlatformApi.query(
            task.taskId!!,
            modeEnum,
            getOutputType()
        )
    }


    open fun getOutputResult(task: T): O? {
        val response = invokePullAiTask(task)
        if (response.failed()) {
            log.error { "${modeEnum.name}任务拉取失败，${response.toJson()}" }
            return null
        }
        val data = response.data
        if (data == null) {
            log.warn { "${modeEnum.name}任务拉取失败，没有DATA数据，${response.toJson()}" }
            return null
        }
        task.message = response.message
        if (data.message.isNotBlank()) {
            task.message = data.message
        }
        val checkStateEnum = checkStateAndFillTaskInfo(task, data)
        if (checkStateEnum != null) {
            when (checkStateEnum) {
                TaskStatusEnum.GENERATING,
                TaskStatusEnum.QUEUEING -> {
                    return null
                }

                TaskStatusEnum.FAILED -> {
                    throw TaskException(data.message.blankDefault("AI任务执行失败"), modeEnum, checkStateEnum)
                }

                TaskStatusEnum.TIMEOUT_FAILED -> {
                    throw TaskException(data.message.blankDefault("AI任务执行超时失败"), modeEnum, checkStateEnum)
                }

                TaskStatusEnum.CANCELED -> {
                    throw TaskException(data.message.blankDefault("AI任务已取消"), modeEnum, checkStateEnum)
                }

                TaskStatusEnum.COMPLETED -> {
                    return data.output
                }

                else -> {
                    println("状态不支持，跳过")
                }
            }
        }
        return null

    }

    open fun <R> createTask(
        task: T,
        tCls: Class<R>
    ): TaskModelResponse<R> {
        val resultType: TypeReference<R> = object :
            TypeReference<R>() {
            override fun getType(): Type {
                return tCls
            }
        }
        return createTask(task, resultType)
    }

    open fun <R> createTask(
        task: T,
        typeReference: TypeReference<R>,
    ): TaskModelResponse<R> {
        ////是否批量任务：0-非批量任务，1-批量任务（默认：否）
        var taskPriority: Int? = when (task) {
            is BaseTaskAttribute -> task.taskAttribute
            is SubTaskAttribute -> task.taskAttribute
            is SyncTaskAttribute -> task.taskAttribute
            is SyncSubTaskAttribute -> task.taskAttribute
            else -> null
        }
        taskPriority = jvTaskPriorityHandle.modifyTaskPriority(task, taskPriority)
        val modelParams = buildTaskModelParams(task)
        return RestAiPlatformApi.create(
            task.taskId!!,
            modelParams,
            modeEnum,
            taskPriority,
            typeReference
        )
    }

    open fun invokePushAiTask(task: T): TaskModelResponse<TaskModelBriefDataVo> {
        return createTask(task, TaskModelBriefDataVo::class.java)
    }

    open fun invokeCancelAiTask(taskId: Long): Boolean {
        val resp = RestAiPlatformApi.cancel(taskId, modeEnum)
        if (resp.succeed() && resp.data?.failed.isEmpty()) {
            return true
        }
        log.error { "取消失败：${modeEnum.name}任务[${taskId}]" }
        return false
    }

    open fun userSuspendTask(task: T) {
        lockExec(task.taskId) {
            invokeCancelAiTask(task.taskId!!)
            task.suspend()
            manualBaseRepository.updateById(task)
        }
    }


    /**
     * 获取泛型输出类型，返序列化算法返回结果
     */
    @Suppress("UNCHECKED_CAST")
    open fun getOutputType(): Class<O> {
        if (outputType != null) {
            return outputType!!
        }
        val aClass = this.javaClass
        val genericSuperclass = aClass.genericSuperclass
        val parameterizedType = genericSuperclass as ParameterizedType
        val actualTypeArguments = parameterizedType.actualTypeArguments
        Assert.isTrue(actualTypeArguments.size == 2, "Not Found OutputType!")
        outputType = actualTypeArguments[1] as Class<O>
        return outputType!!
    }

    private fun findTypeReferenceSubclass(child: Class<*>): Class<*> {
        val parent = child.superclass
        return if (Any::class.java == parent) {
            throw IllegalStateException("Expected ParameterizedTypeReference superclass")
        } else if (TaskBaseSupportImpl::class.java == parent) {
            child
        } else {
            findTypeReferenceSubclass(parent)
        }
    }


    fun <R> lockExec(lockId: Long?, fn: () -> R): R {
        return lockExec(lockId?.toString(), fn)
    }

    fun <R> checkLockExec(lockId: Long?, needLock: Boolean, fn: () -> R): R {
        if (!needLock) {
            return fn()
        }
        return lockExec(lockId?.toString(), fn)
    }

    fun <R> lockExec(lockKey: String?, fn: () -> R): R {
        try {
            return RedissonHelper.lock("${config.handleLockKey}:$lockKey", config.lockWait, config.lockLease) {
                fn()
            }
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            throw RuntimeException(e)
        }
    }

    fun <R> userInvoke(task: T, action: () -> R): R {
        return UserInvoke.doAction(task.creatorId!!, task.creatorName!!, task.tenantId!!, action)
    }


    abstract fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo>


    fun <Q : TaskCommonQuery> Q.adaptActionQuery(taskAction: TaskActionEnum): Boolean {
        return when (taskAction) {
            TaskActionEnum.PULL -> {
                this.pullQuery()
                true
            }

            TaskActionEnum.SYNC -> {
                this.syncQuery(config.maxSyncFailTimes, config.lastSyncHours)
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 任务类型
     */
    override fun taskMode(): String {
        return modeEnum.name
    }

}


