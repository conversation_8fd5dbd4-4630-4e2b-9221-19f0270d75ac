package tech.tiangong.butted.openai.aiplatform.convert

import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternLabelModelReq
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FlowerPatternLabelTask
import tech.tiangong.butted.openai.vo.req.FlowerPatternLabelTaskReq
import team.aikero.blade.logging.core.annotation.Slf4j

@Slf4j
object FlowerPatternLabelConvert : BaseConvert {

    fun buildFlowerPatternLabelTask(req: FlowerPatternLabelTaskReq): FlowerPatternLabelTask {
        return FlowerPatternLabelTask().apply {
            this.initBase()
            this.parentTaskId = req.parentTaskId
            this.inputImage = req.inputImage.toFullImgPath()
            this.labelVersion = req.labelVersion
            this.taskAttribute = req.taskAttribute
        }
    }

    fun packFlowerPatternLabelModelReq(task: FlowerPatternLabelTask): FlowerPatternLabelModelReq {
        return FlowerPatternLabelModelReq(
            inputImage = task.inputImage,
            labelVersion = task.labelVersion
        )
    }

}

