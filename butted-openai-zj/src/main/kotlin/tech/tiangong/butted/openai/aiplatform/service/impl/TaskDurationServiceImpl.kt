package tech.tiangong.butted.openai.aiplatform.service.impl

import tech.tiangong.butted.openai.aiplatform.convert.TaskDurationConvert
import tech.tiangong.butted.core.toolkit.tryExec
import tech.tiangong.butted.enums.TaskTypeEnum
import tech.tiangong.butted.event.FlowerPatternExtractTaskEvent
import tech.tiangong.butted.event.SmartDesignTaskEvent
import tech.tiangong.butted.openai.entity.base.BaseTask
import tech.tiangong.butted.openai.repository.TaskDurationRepository
import tech.tiangong.butted.openai.service.TaskDurationService
import tech.tiangong.butted.openai.vo.resp.TaskDurationVo
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j

/**
 * 任务耗时统计服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TaskDurationServiceImpl(
    val taskDurationRepository: TaskDurationRepository
) : TaskDurationService {

    override fun getById(durationId: Long): TaskDurationVo {
        return TaskDurationVo()
    }


    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenFlowerPatternExtractTaskEvent(event: FlowerPatternExtractTaskEvent) {
        tryExec {
            val taskStatusEnum = event.taskStatus
            if (!taskStatusEnum.completed()) {
                return@tryExec
            }
            handlingTime(event.getTask(), TaskTypeEnum.FLOWER_PATTERN_EXTRACT)
        }
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenSmartDesignTaskEvent(event: SmartDesignTaskEvent) {
        tryExec {
            val taskStatusEnum = event.taskStatus
            if (!taskStatusEnum.completed()) {
                return@tryExec
            }
            handlingTime(event.getTask(), TaskTypeEnum.SMART_DESIGN)
        }
    }


    fun handlingTime(task: BaseTask, typeEnum: TaskTypeEnum) {
        task.createdTime ?: return
        task.aiStartTime ?: return
        task.aiEndTime ?: return
        var taskDuration = taskDurationRepository.getByType(typeEnum.code)
        if (taskDuration == null) {
            taskDuration = TaskDurationConvert.buildTaskDuration(task, typeEnum)
            taskDurationRepository.save(taskDuration)
            return
        }
        val updateTaskDuration = TaskDurationConvert.updateTaskDuration(taskDuration, task)
        if (updateTaskDuration) {
            taskDurationRepository.updateById(taskDuration)
        }

    }

}
