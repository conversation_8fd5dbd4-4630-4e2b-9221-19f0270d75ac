package tech.tiangong.butted.openai.aiplatform.service.base

import com.fasterxml.jackson.core.type.TypeReference
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.butted.core.reflect.ParameterizedTypeImpl
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelBriefDataVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelDataVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelOutputVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelResponse
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.pkg.TaskModelDataPkgVo
import tech.tiangong.butted.openai.aiplatform.remote.RestPkgApi
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.BaseTask
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository
import java.lang.reflect.Type

/**
 * TaskBasePkgSupportImpl
 */
@Slf4j
abstract class TaskBasePkgSupportImpl<T : BaseTask, O : TaskModelOutputVo>(
    config: OpenAiTaskConfig,
    manualBaseRepository: ManualBaseRepository<*, T>
) : TaskBaseSupportImpl<T, O>(
    config,
    manualBaseRepository
), ZjTaskBaseSupport<T> {

    override fun notify(taskMode: String, taskId: String, jsonBody: String?): Boolean {
        try {
            return lockExec(taskId.toLong()) {
                if (jsonBody.isBlank()) {
                    doHandlePull(taskId.toLong())
                    return@lockExec true
                }
                val task = obtainById(taskId.toLong())
                val bodyType = object :
                    TypeReference<TaskModelDataPkgVo<O>>() {
                    override fun getType(): Type {
                        return ParameterizedTypeImpl(
                            TaskModelDataPkgVo::class.java,
                            getOutputType()
                        )
                    }
                }
                val dataVo = jsonBody!!.parseJson(bodyType)
                super.routeResult(task, TaskModelResponse.ok(dataVo.toTaskModelDataVo()))
                true
            }
        } catch (e: Exception) {
            log.error { "回调通知${modeEnum.name}任务失败：${e.stackTraceToString()}" }
            return false
        }

    }

    override fun invokePullAiTask(task: T): TaskModelResponse<TaskModelDataVo<O>> {
        return RestPkgApi.query(
            task.taskId!!,
            modeEnum,
            getOutputType()
        ).toTaskModelResponse().let {
            val dataVo = it.data?.toTaskModelDataVo()
            TaskModelResponse(
                successful = it.successful,
                code = it.code,
                data = dataVo,
                message = it.message,
            )
        }
    }

    override fun invokePushAiTask(task: T): TaskModelResponse<TaskModelBriefDataVo> {
        val modelParams = buildTaskModelParams(task)
        val response = RestPkgApi.create(
            task.taskId!!,
            modelParams,
            modeEnum,
        )
        return response.toTaskModelResponse()
    }

    override fun invokeCancelAiTask(taskId: Long): Boolean {
        val resp = RestPkgApi.cancel(taskId, modeEnum)
        if (resp.succeed() && resp.data?.failed.isEmpty()) {
            return true
        }
        log.error { "取消失败：${modeEnum.name}任务[${taskId}]" }
        return false
    }


}


