package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.DressUpTaskReq
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.DressUpTaskVo
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.common.vo.base.ResImgVo
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.core.toolkit.splitAndFirst
import tech.tiangong.butted.enums.*
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.DressUpProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.DressUpModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.pkg.output.DressUpOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.DressUpConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.DressUpTask
import tech.tiangong.butted.openai.repository.DressUpOutputRepository
import tech.tiangong.butted.openai.repository.DressUpTaskRepository
import tech.tiangong.butted.openai.repository.PictureMarkTaskRepository
import tech.tiangong.butted.openai.service.DressUpOutputService
import tech.tiangong.butted.openai.service.DressUpTaskService
import tech.tiangong.butted.openai.service.PictureMarkTaskService
import tech.tiangong.butted.openai.vo.query.DressUpTaskQuery
import tech.tiangong.butted.openai.vo.req.PictureMarkTaskReq
import tech.tiangong.butted.util.UserInvoke
import java.time.LocalDateTime

/**
 * 服装上身任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class DressUpTaskServiceImpl(
    config: DressUpProperties,
    val dressUpOutputService: DressUpOutputService,
    val dressUpTaskRepository: DressUpTaskRepository,
    val dressUpOutputRepository: DressUpOutputRepository,
    val pictureMarkTaskService: PictureMarkTaskService,
    val pictureMarkTaskRepository: PictureMarkTaskRepository,
) : TaskSyncSupportImpl<DressUpTask, DressUpOutputVo>(
    config,
    dressUpTaskRepository
), DressUpTaskService {

    init {
        modeEnum = AiTaskModeEnum.DRESS_UP
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: DressUpTaskReq): Long {
        val task = DressUpConvert.build(req)
        dressUpTaskRepository.save(task)
        afterCommitExec {
            executor.delayExecute(1000L * 10) {
                checkToPush(task.taskId!!)
            }
        }
        return task.taskId!!
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: DressUpTaskReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }

    override fun getByBusId(busId: Long): DressUpTaskVo {
        val task = dressUpTaskRepository.getByBusId(busId)
        return packDressUpTaskVo(task)
    }


    private fun packDressUpTaskVo(task: DressUpTask): DressUpTaskVo {
        val outputList = if (TaskStatusEnum.completed(task.taskStatus)) {
            dressUpOutputRepository.listByTaskId(task.taskId!!)
        } else {
            listOf()
        }
        return task.copy(DressUpTaskVo::class).apply {
            this.resImgList = outputList.map { ResImgVo(resImg = it.resImg, serialNum = it.serialNum) }
            this.calculateDuration()
        }
    }

    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查推送
                if (task.pushStatus == PushStatusEnum.UN_PUSH.code) {
                    checkToPush(taskId)
                    return@lockExec
                }
                //检查超时
                if (task.checkTime() < LocalDateTime.now().minusSeconds(config.timeout + config.pullInterval)) {
                    handleTimeout(task)
                    return@lockExec
                }
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }
    }


    override fun buildTaskModelParams(task: DressUpTask): DressUpModelReq {
        return DressUpConvert.packDressUpModelReq(task)
    }

    @Throws(Throwable::class)
    fun checkToPush(taskId: Long) {
        lockExec("CheckToPush:$taskId") {
            val task = dressUpTaskRepository.obtainById(taskId)
            if (!PushStatusEnum.unPushed(task.pushStatus)) {
                return@lockExec
            }
            withTranExec {
                try {
                    if (!waitModelMarkImg(task)) {
                        /*val modelReq = DressUpConvert.packDressUpModelReq(task)
                        RestPkgApi.create(
                            task.taskId!!,
                            modelReq,
                            modeEnum
                        ).also { resp ->
                            if (resp.failed()) {
                                log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                                throw RemoteException(resp.message ?: "推送任务失败")
                            }
                        }*/
                        doPush(task)
                        task.waitSync().firstPushed()
                        manualUpdateAiTaskInfo(task)
                    }
                } catch (e: Throwable) {
                    log.error { "推送服装上身任务失败：${e.stackTraceToString()}" }
                    task.waitSync().pushFailed()
                    task.taskStatus = TaskStatusEnum.FAILED.code
                    task.message = e.message.blankDefault("推送任务失败")
                    manualUpdateAiTaskInfo(task)
                }
            }
        }

    }

    @Throws(Exception::class)
    fun waitModelMarkImg(task: DressUpTask): Boolean {
        if (task.modelMarkImg.isNotBlank()) {
            return false
        }
        val markTask =
            pictureMarkTaskRepository.getByParentTaskId(task.taskId!!, MarkTypeEnum.DRESS_UP_MODEL.code)
        if (markTask != null) {
            if (TaskStatusEnum.completed(markTask.taskStatus)) {
                task.modelMarkImg = markTask.resImgs?.splitAndFirst()
                return false
            }
            if (TaskStatusEnum.processing(markTask.taskStatus)) {
                return true
            }
            throw TaskException(
                "获取模特Mark失败",
                AiTaskModeEnum.TRY_ON_MODEL_MARK
            )
        }
        UserInvoke.doAction(task) {
            val req = PictureMarkTaskReq(
                parentTaskId = task.taskId!!,
                inputImg = task.modelImg!!,
                markType = MarkTypeEnum.DRESS_UP_MODEL.code,
                modelType = "tryon",
            ).apply {
                if (task.categoryName.isNotBlank()) {
                    this.category = task.categoryName
                }
            }
            pictureMarkTaskService.create(req)
        }

        return true
    }


    override fun handlePullResult(
        task: DressUpTask,
        changeStateEnum: TaskStatusEnum,
        output: DressUpOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        withTranExec {
            var checkStateEnum = changeStateEnum
            if (completed) {
                if (output == null || output.resImgs.isEmpty()) {
                    checkStateEnum = TaskStatusEnum.FAILED
                    task.message = "AI生图结果为空"
                } else {
                    val outputReq = DressUpConvert.packDressUpOutputReq(task, output.resImgs!!)
                    dressUpOutputService.manualCreate(outputReq)
                    task.taskProgress = 100
                }
            }
            if (task.taskStatus != checkStateEnum.code) {
                task.waitSync()
            }
            task.taskStatus = checkStateEnum.code
            manualUpdateAiTaskInfo(task)
        }
    }


    override fun handleException(task: DressUpTask, message: String?, statusEnum: TaskStatusEnum?) {
        super.handleException(task, message, statusEnum)
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: DressUpTask, canceled: Boolean) {
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return DressUpTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                dressUpTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    override fun doSync(task: DressUpTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = dressUpTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步服装上身任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        dressUpTaskRepository.getByBusId(busId).also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: DressUpTask) {
        task.refreshRevisedTime()
        withTranExec {
            dressUpTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
        }
    }
}
