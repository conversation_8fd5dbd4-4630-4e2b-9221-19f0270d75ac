package tech.tiangong.butted.openai.aiplatform.convert

import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternLabelModelReq
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FlowerPatternLabelTask
import tech.tiangong.butted.openai.vo.req.FlowerPatternLabelTaskReq
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.openai.aiplatform.aimodel.req.MarketCategoryModelReq
import tech.tiangong.butted.openai.entity.MarketCategoryTask
import tech.tiangong.butted.openai.vo.req.MarketCategoryTaskReq

@Slf4j
object MarketCategoryConvert : BaseConvert {

    fun buildMarketCategoryTask(req: MarketCategoryTaskReq): MarketCategoryTask {
        return MarketCategoryTask().apply {
            this.initBase()
            this.inputImage = req.inputImage.toFullImgPath()
            this.marketCode = req.marketCode
        }
    }

    fun packMarketCategoryModelReq(task: MarketCategoryTask): MarketCategoryModelReq {
        return MarketCategoryModelReq(
            inputImage = task.inputImage,
            // 中东市场传"中东"
            region = if (task.marketCode!=null && task.marketCode.equals("zd")) "中东" else null
        )
    }

}

