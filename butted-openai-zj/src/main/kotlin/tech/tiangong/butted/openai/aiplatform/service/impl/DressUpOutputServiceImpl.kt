package tech.tiangong.butted.openai.aiplatform.service.impl

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.openai.aiplatform.convert.DressUpConvert
import tech.tiangong.butted.openai.repository.DressUpOutputRepository
import tech.tiangong.butted.openai.service.DressUpOutputService
import tech.tiangong.butted.openai.vo.req.DressUpOutputReq
import tech.tiangong.butted.redis.RedissonHelper

/**
 * 服装上身生成图服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class DressUpOutputServiceImpl(
    val dressUpOutputRepository: DressUpOutputRepository
) : DressUpOutputService {

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: DressUpOutputReq) {
        RedissonHelper.lockExec("DressUpOutput", req.taskId) {
            var outputList = dressUpOutputRepository.listByTaskId(req.taskId)
            if (outputList.isNotEmpty() || req.resImgList.isEmpty()) {
                return@lockExec
            }
            outputList = DressUpConvert.buildDressUpOutputList(req)
            dressUpOutputRepository.saveBatch(outputList)
        }
    }


}
