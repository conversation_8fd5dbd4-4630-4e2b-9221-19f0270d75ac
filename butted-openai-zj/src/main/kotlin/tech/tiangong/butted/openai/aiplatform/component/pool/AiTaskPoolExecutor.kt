package tech.tiangong.butted.openai.aiplatform.component.pool


import tech.tiangong.butted.core.pool.ButtedThreadPoolExecutor
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum

/**
 * AiTaskPoolExecutor
 */
object AiTaskPoolExecutor {
    private val taskExecutor = ButtedThreadPoolExecutor.build4("ZJ-AiTask")
    fun execute(modeEnum: AiTaskModeEnum, taskAction: TaskActionEnum, taskId: Long, command: Runnable) {
        val tag = "${modeEnum.name}-${taskAction.name}-${taskId}"
        taskExecutor.execute(tag, command)
    }

}
