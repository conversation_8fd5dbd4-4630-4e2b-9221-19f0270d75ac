package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.ClipLabelTaskVo
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.SourceEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.ClipLabelTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.exception.NotFoundException
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ClipLabelProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.ClipLabelModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.ClipLabelOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.ClipConvert
import tech.tiangong.butted.openai.aiplatform.remote.FmClientApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.ClipLabelTask
import tech.tiangong.butted.openai.repository.ClipLabelTaskRepository
import tech.tiangong.butted.openai.service.ClipLabelTaskService
import tech.tiangong.butted.openai.service.TryOnFlatLabelTaskService
import tech.tiangong.butted.openai.vo.query.ClipLabelTaskQuery
import tech.tiangong.butted.openai.vo.req.ClipLabelTaskCreateReq
import tech.tiangong.butted.openai.vo.req.ClipLabelTaskManualCreateReq
import tech.tiangong.butted.openai.vo.req.TryOnFlatLabelTaskReq
import tech.tiangong.butted.util.UserInvoke

/**
 * 标签提取任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class ClipLabelTaskServiceImpl(
    config: ClipLabelProperties,
    val tryOnFlatLabelTaskService: TryOnFlatLabelTaskService,
    val clipLabelTaskRepository: ClipLabelTaskRepository
) : TaskSyncSupportImpl<ClipLabelTask, ClipLabelOutputVo>(
    config,
    clipLabelTaskRepository
), ClipLabelTaskService {
    init {
        modeEnum = AiTaskModeEnum.CLIP_VIT_L_14
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: ClipLabelTaskCreateReq): Long {
        return ClipConvert.build(req).let {
            clipLabelTaskRepository.save(it)
            doPush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: ClipLabelTaskCreateReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: ClipLabelTaskManualCreateReq): Long {
        return ClipConvert.build(req).let {
            clipLabelTaskRepository.saveManualFill(it)
            doPush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun retry(clipTaskId: Long): Long {
        val originTask = obtainById(clipTaskId)
        val req = ClipLabelTaskCreateReq(
            source = SourceEnum.A_RETRY,
            parentTaskId = originTask.taskId
        ).apply {
            this.inputImage = originTask.inputImage
        }
        originTask.addRetryTimes(create(req))
        originTask.reset()
        originTask.refreshRevisedTime()
        clipLabelTaskRepository.updateByIdManualFill(originTask)
        return originTask.taskId!!
    }


    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查脸部修复是否完成
                if (task.retryTaskId != null) {
                    handleRetryResult(task)
                    return@lockExec
                }
                super.doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }

    }

    override fun detail(taskId: Long): ClipLabelTaskVo {
        val task = clipLabelTaskRepository.getById(taskId)
        return packClipLabelTaskVo(task)
            ?: throw NotFoundException("未找到Clip任务")
    }

    override fun getByBusId(busId: Long): ClipLabelTaskVo {
        return packClipLabelTaskVo(clipLabelTaskRepository.getByBusId(busId))!!
    }

    override fun listByTaskId(taskIdList: List<Long>): List<ClipLabelTaskVo> {
        return clipLabelTaskRepository.findByTaskId(taskIdList).map {
            packClipLabelTaskVo(it)!!
        }
    }

    override fun getIdentifyClipTask(smartIdentifyId: Long): ClipLabelTaskVo {
        return getByParentTaskId(smartIdentifyId, SourceEnum.C_SMART_IDENTIFY)
            ?: throw RuntimeException("未找到对应的智能识别任务")
    }

    override fun getByParentTaskId(parentTaskId: Long, sourceEnum: SourceEnum): ClipLabelTaskVo? {
        val task = clipLabelTaskRepository.getByParentTaskIdAndSource(parentTaskId, sourceEnum)
        return packClipLabelTaskVo(task)
    }

    fun packClipLabelTaskVo(task: ClipLabelTask?): ClipLabelTaskVo? {
        if (task == null) {
            return null
        }
        return ClipConvert.packClipLabelTaskVo(task).apply {
            if (task.needDoTryOnFlatLabelTask()) {
                val tryOnLabelVo = tryOnFlatLabelTaskService.getLastByParentTaskId(task.taskId!!)
                if (tryOnLabelVo == null) {
                    this.taskStatus = TaskStatusEnum.QUEUEING.code
                    this.taskProgress = 0
                    this.rankPosition = null
                    this.predLabelList = listOf()
                    this.category = null
                    this.categoryCode = null
                    this.extendActions = null
                    this.message = null
                } else {
                    this.taskStatus = tryOnLabelVo.taskStatus
                    this.taskProgress = tryOnLabelVo.taskProgress
                    this.rankPosition = tryOnLabelVo.rankPosition
                    this.predLabelList = tryOnLabelVo.predLabelList
                    this.category = tryOnLabelVo.category
                    this.categoryCode = tryOnLabelVo.categoryCode
                    this.extendActions = tryOnLabelVo.extendActions
                    this.message = tryOnLabelVo.message
                }
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByParentTaskId(parentTaskId: Long) {
        clipLabelTaskRepository.getByParentTaskId(parentTaskId)?.also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }


    override fun buildTaskModelParams(task: ClipLabelTask): ClipLabelModelReq {
        return ClipConvert.packClipLabelModelReq(task)
    }

    override fun handlePullResult(
        task: ClipLabelTask,
        changeStateEnum: TaskStatusEnum,
        output: ClipLabelOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.usefulFlat = it.usefulFlatToInt()
                task.predLabels = it.predLabels?.toJson()
                val predLabelDtoList = it.toPredLabelDtoList()
                val analysisClipLabel = ClipConvert.analysisClipLabel(predLabelDtoList)
                task.inputImageType = analysisClipLabel.inputImageType
                task.styleType = analysisClipLabel.styleType
                task.category = analysisClipLabel.category
                if (task.category.isNotBlank()) {
                    try {
                        val fmLabelVo = FmClientApi.obtainLabelInfoByName(task.category!!)
                        task.categoryCode = fmLabelVo.categoryCode
                        task.extendActions = fmLabelVo.extendLabel
                        task.usableLabels =
                            ClipConvert.checkAndFilterPredLabelJsonStr(
                                predLabelDtoList,
                                fmLabelVo.styleLabels,
                                fmLabelVo.themeLabels
                            )
                    } catch (e: Exception) {
                        log.error { "【${modeEnum.name}】任务异常：\n ${e.stackTraceToString()}" }
                        handleException(task, e.message)
                        return
                    }
                }
            }
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        withTranExec {
            checkTodoTryOnFlatLabelTask(task)
            manualUpdateAiTaskInfo(task, ClipLabelTaskEvent(task, changeStateEnum))
        }
    }

    //待优化
    fun handleRetryResult(task: ClipLabelTask) {
        val retryTask = clipLabelTaskRepository.obtainById(task.retryTaskId!!)
        if (task.taskStatus == retryTask.taskStatus) {
            return
        }
        task.taskStatus = retryTask.taskStatus
        task.taskProgress = retryTask.taskProgress
        task.rankPosition = retryTask.rankPosition
        task.inputImageType = retryTask.inputImageType
        task.extendActions = retryTask.extendActions
        task.category = retryTask.category
        task.categoryCode = retryTask.categoryCode
        task.styleType = retryTask.styleType
        task.usefulFlat = retryTask.usefulFlat
        task.predLabels = retryTask.predLabels
        task.usableLabels = retryTask.usableLabels
        task.message = retryTask.message
        task.pullTime = retryTask.pullTime
        task.pullTimes = retryTask.pullTimes
        task.aiStartTime = retryTask.aiStartTime
        task.aiEndTime = retryTask.aiEndTime
        val changeStateEnum = TaskStatusEnum.of(task.taskStatus!!)
        withTranExec {
            checkTodoTryOnFlatLabelTask(task)
            manualUpdateAiTaskInfo(task, ClipLabelTaskEvent(task, changeStateEnum))
        }
    }


    fun checkTodoTryOnFlatLabelTask(task: ClipLabelTask) {
        if (task.needDoTryOnFlatLabelTask()) {
            val req = TryOnFlatLabelTaskReq(
                parentTaskId = task.taskId,
                inputImage = task.inputImage,
                taskAttribute = task.taskAttribute,
            ).apply {
                tenantId = task.tenantId
                creatorId = task.creatorId
                creatorName = task.creatorName
            }
            tryOnFlatLabelTaskService.manualCreate(req)
        }
    }

    override fun handleException(task: ClipLabelTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task, ClipLabelTaskEvent(task, failStatus))
    }


    override fun handleTimeoutResult(task: ClipLabelTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, ClipLabelTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return ClipLabelTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                clipLabelTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    override fun doSync(task: ClipLabelTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = clipLabelTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步Clip任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }

    fun manualUpdateAiTaskInfo(task: ClipLabelTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (task.needAsync()) {
            if (!SourceEnum.needSync(task.source)) {
                task.synced()
            }
        }
        withTranExec {
            clipLabelTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
            callbackBusiness(task)
        }
    }

}
