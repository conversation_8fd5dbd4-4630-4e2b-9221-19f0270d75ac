package tech.tiangong.butted.openai.aiplatform.component


import cn.hutool.core.text.StrPool
import team.aikero.blade.core.toolkit.*
import com.zjkj.supplychain.commodity.client.beans.vo.response.TaoTianCommodityDetailRespVo
import com.zjkj.supplychain.commodity.client.beans.vo.response.TaoTianCommodityRespVo
import java.io.Serializable

/**
 * SPU（款式）识别家族DTO
 */
class TopFamilyNames(
    /**
     * 第几top家族[0-4]
     */
    var topIndex: Int, topFamilyNames: List<String?>? = mutableListOf()
) : Serializable {

    /**
     * 家族面料标签名称（逐渐处理往handledFamilyNames里面移）
     */
    var topFamilyNames: MutableList<String> = mutableListOf()

    /**
     * 当前处理的面料标签名称
     */
    var nextFamilyNames: MutableList<String> = mutableListOf()

    /**
     * 已处理过的面料标签名称
     */
    val handledFamilyNames: MutableList<String> = mutableListOf()

    /**
     * 当前家族选中的面料标签名称
     */
    var selectedFamilyName: String? = null

    /**
     * 当前家族选中的面料SKU
     */
    var selectedSku: TaoTianCommodityDetailRespVo? = null


    init {
        if (topFamilyNames.isNotEmpty()) {
            this.topFamilyNames = topFamilyNames!!.filter { it.isNotBlank() }.map { it!! }.toMutableList()
        }
    }

    fun checkNext(): Boolean {
        return this.selectedFamilyName.isBlank() && hasNext()
    }

    fun hasNext(): Boolean {
        return this.topFamilyNames.isNotEmpty()
    }

    /**
     * 随机从topFamilyNames中随机移除N个元素，并放入nextFamilyNames中
     *
     * @param count
     * @return
     * <AUTHOR>
     * @since 2024年07月24日10:25:33
     */
    fun next(count: Int): List<String> {
        this.nextFamilyNames = this.topFamilyNames.removeN(count)
        handledFamilyNames.addAll(this.nextFamilyNames)
        return this.nextFamilyNames
    }

    /**
     * 选中SKU
     */
    fun doSelected(fabricLabelMap: MutableMap<String?, TaoTianCommodityRespVo?>): Long? {
        //已经选中
        if (this.selectedFamilyName.isNotBlank() || this.selectedSku.isNotNull()) {
            return null
        }
        if (fabricLabelMap.isEmpty()) {
            return null
        }
        for (nextFamilyName in nextFamilyNames) {
            // 替换短横线为斜杠
            val fabricLabel = nextFamilyName.replace(StrPool.DASHED, StrPool.SLASH)
            fabricLabelMap[fabricLabel]?.also { cd ->
                fabricLabelMap.remove(fabricLabel)
                cd.taoTianCommodityVoList.firstOrNull()?.also { sku ->
                    this.selectedFamilyName = nextFamilyName
                    this.selectedSku = sku
                    return selectedSku!!.skuId
                }
            }
        }
        return null
    }

    val isSelected: Boolean
        /**
         * 是否选中SKU
         */
        get() = this.selectedFamilyName.isNotBlank() && this.selectedSku.isNotNull()


    private fun <T> MutableList<T>?.removeN(n: Int): MutableList<T> {
        if (this.isEmpty()) {
            return mutableListOf()
        }
        var removeCount = n
        if (removeCount > this!!.size || n < 0) {
            removeCount = this.size
        }
        val eleList: MutableList<T> = mutableListOf()
        for (i in 0 until removeCount) {
            eleList.add(this.removeAt(0))
        }
        return eleList
    }


    companion object {
        private const val serialVersionUID = 1L
    }

}
