package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.AlgorithmEnum
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.tryon.TryOnTaskBatchReq
import tech.tiangong.butted.common.req.tryon.TryOnTaskReq
import tech.tiangong.butted.common.vo.ResShiftImgVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.common.vo.tryon.TryOnTaskVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.*
import tech.tiangong.butted.event.ClipLabelTaskEvent
import tech.tiangong.butted.event.ShiftFaceStateEvent
import tech.tiangong.butted.event.TryOnFlatLabelTaskEvent
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.exception.RemoteException
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.ClipLabelProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.TryOnOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.TryOnConvert
import tech.tiangong.butted.openai.aiplatform.extend.ableTryOn
import tech.tiangong.butted.openai.aiplatform.extend.disableTryOn
import tech.tiangong.butted.openai.aiplatform.remote.FmClientApi
import tech.tiangong.butted.openai.aiplatform.remote.RestAiPlatformApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.ClipLabelTask
import tech.tiangong.butted.openai.entity.TryOnTask
import tech.tiangong.butted.openai.repository.TryOnTaskRepository
import tech.tiangong.butted.openai.service.ClipLabelTaskService
import tech.tiangong.butted.openai.service.TryOnTaskOutputService
import tech.tiangong.butted.openai.service.TryOnTaskService
import tech.tiangong.butted.openai.vo.query.TryOnTaskQuery
import tech.tiangong.butted.openai.vo.req.ClipLabelTaskManualCreateReq
import java.time.LocalDateTime

/**
 * 虚拟换装任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class TryOnTaskServiceImpl(
    config: ClipLabelProperties,
    val clipLabelTaskService: ClipLabelTaskService,
    val tryOnTaskOutputService: TryOnTaskOutputService,
    val tryOnTaskRepository: TryOnTaskRepository
) : TaskSyncSupportImpl<TryOnTask, TryOnOutputVo>(
    config,
    tryOnTaskRepository
), TryOnTaskService {
    init {
        modeEnum = AiTaskModeEnum.TRYON
    }


    @Deprecated("使用潮际虚拟换衣")
    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: TryOnTaskReq): Long {
        throw UnsupportedOperationException("接口不支持创建了")
        val task = TryOnConvert.build(checkCategory(req))
        if (req.clipTaskId != null) {
            val labelTask = clipLabelTaskService.obtainById(req.clipTaskId!!)
            if (TaskStatusEnum.failedOrCanceled(labelTask.taskStatus)) {
                task.cleanPushed()
                task.clipTaskId = clipLabelTaskService.retry(req.clipTaskId!!)
            }
            tryOnTaskRepository.save(task)
            if (!TaskStatusEnum.failedOrCanceled(labelTask.taskStatus)) {
                doPush(task)
            }
        } else {
            tryOnTaskRepository.save(task)
            afterCommitExec {
                doClipLabelTask(listOf(task))
            }
        }
        return task.taskId!!
    }


    @Deprecated("使用潮际虚拟换衣")
    @Transactional(rollbackFor = [Exception::class])
    override fun batchCreate(batchReq: TryOnTaskBatchReq) {
        throw UnsupportedOperationException("接口不支持创建了")
        val taskList = TryOnConvert.buildTryOnTaskList(batchReq)
        tryOnTaskRepository.saveBatch(taskList)
        afterCommitExec {
            doClipLabelTask(taskList)
        }
    }

    override fun getByBusId(busId: Long): TryOnTaskVo {
        val task = tryOnTaskRepository.getByBusId(busId)
        return packTryOnTaskVo(task)!!
    }

    private fun packTryOnTaskVo(task: TryOnTask?): TryOnTaskVo? {
        if (task == null) {
            return null
        }
        val outputList = if (TaskStatusEnum.completed(task.taskStatus)) {
            tryOnTaskOutputService.findByTaskId(task.taskId!!)
        } else {
            listOf()
        }
        return task.copy(TryOnTaskVo::class).apply {
            shiftFaceImgList = task.shiftFaceImgs?.split(Constant.COMMA)
            resImgList = outputList.map { ResShiftImgVo(resImg = it.resImg, shiftImg = it.shiftFaceImg) }
            if (this.clipTaskId != null) {
                clipLabelTaskService.detail(this.clipTaskId!!)?.also {
                    if (it.extendActions.ableTryOn()) {
                        this.predLabelList = it.predLabelList
                    }
                }
            }
            if (TaskStatusEnum.completed(task.taskStatus)) {
                if (task.needShiftFace() && !ShiftFaceEnum.finished(task.shiftFace)) {
                    this.taskStatus = TaskStatusEnum.GENERATING.code
                }
            }
        }
    }

    private fun doClipLabelTask(taskList: List<TryOnTask>) {
        taskList.forEach { task ->
            withTranExec {
                try {
                    val req = ClipLabelTaskManualCreateReq(
                        inputImage = task.garmImgUrl,
                        source = SourceEnum.A_TRY_ON,
                        parentTaskId = task.taskId,
                        taskAttribute = task.taskAttribute
                    ).apply {
                        tenantId = task.tenantId
                        creatorId = task.creatorId
                        creatorName = task.creatorName
                    }
                    task.clipTaskId = clipLabelTaskService.manualCreate(req)
                    manualUpdateAiTaskInfo(task)
                } catch (e: Exception) {
                    handleException(task, e.message ?: "Do Clip模型异常", TaskStatusEnum.FAILED)
                }
            }
        }
    }

    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查脸部修复是否完成
                if (TaskStatusEnum.completed(task.taskStatus)) {
                    checkShiftFaceProgress(task)
                    return@lockExec
                }
                //检查推送
                if (task.pushStatus == PushStatusEnum.UN_PUSH.code) {
                    if (task.clipTaskId != null) {
                        clipLabelTaskService.obtainById(task.clipTaskId!!).also {
                            checkToPushTryOnTask(task, it)
                        }
                    }
                    return@lockExec
                }
                //检查超时
                if (task.checkTime() < LocalDateTime.now().minusSeconds(config.timeout + config.pullInterval)) {
                    handleTimeout(task)
                    return@lockExec
                }
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }

    }


    fun checkShiftFaceProgress(task: TryOnTask) {
        if (!ShiftFaceEnum.checkTodoShift(task.shiftFace)) {
            return
        }
        val shiftEnum = tryOnTaskOutputService.checkShiftProgress(task.taskId!!) ?: return
        if (task.shiftFace == shiftEnum.code) {
            return
        }
        task.shiftFace = shiftEnum.code
        if (shiftEnum.finished()) {
            task.waitSync()
            task.aiEndTime = LocalDateTime.now()
        }
        manualUpdateAiTaskInfo(task)
    }


    override fun buildTaskModelParams(task: TryOnTask): Any {
        return TryOnConvert.packTryOnModelReq(task)
    }

    override fun handlePullResult(
        task: TryOnTask,
        changeStateEnum: TaskStatusEnum,
        output: TryOnOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        withTranExec {
            var checkStateEnum = changeStateEnum
            if (completed) {
                if (output == null || output.resImgs.isEmpty()) {
                    checkStateEnum = TaskStatusEnum.FAILED
                    task.message = "AI生图结果为空"
                } else {
                    val outputReq = TryOnConvert.packTryOnTaskOutputReq(task, output.resImgs!!)
                    tryOnTaskOutputService.manualCreate(outputReq)
                    task.taskProgress = 100
                    if (task.needShiftFace()) {
                        task.shiftFace = ShiftFaceEnum.SHIFTING.code
                    }
                    task.subtaskStatus = TaskStatusEnum.COMPLETED.code
                }
            }
            if (task.taskStatus != checkStateEnum.code) {
                if (!(checkStateEnum.completed() && task.needShiftFace())) {
                    task.waitSync()
                }
            }
            task.taskStatus = checkStateEnum.code
            if (checkStateEnum.failedOrCanceled()) {
                task.failTaskMode = AiTaskModeEnum.TRYON.fullMode()
            }
            manualUpdateAiTaskInfo(task)
        }
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenClipLabelTaskEvent(event: ClipLabelTaskEvent) {
        val clipTask = event.getTask()
        if (!SourceEnum.fromTryOn(clipTask.source) || clipTask.parentTaskId == null) {
            return
        }
        lockExec(clipTask.parentTaskId) {
            val task = tryOnTaskRepository.getById(clipTask.parentTaskId) ?: return@lockExec
            checkToPushTryOnTask(task, clipTask)
        }

    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenTryOnFlatLabelTaskEvent(event: TryOnFlatLabelTaskEvent) {
        val flatLabelTask = event.getTask()
        val flatLabelTaskStatus = event.taskStatus
        if (flatLabelTaskStatus.processing() || flatLabelTask.parentTaskId == null) {
            return
        }
        val clipTask = clipLabelTaskService.obtainById(flatLabelTask.parentTaskId!!)
        if (!SourceEnum.fromTryOn(clipTask.source) || clipTask.parentTaskId == null) {
            return
        }
        lockExec(clipTask.parentTaskId) {
            val task = obtainById(clipTask.parentTaskId!!)
            checkToPushTryOnTask(task, clipTask)
        }
    }


    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenShiftFaceStateEvent(event: ShiftFaceStateEvent) {
        try {
            lockExec(event.getTaskId()) {
                val task = obtainById(event.getTaskId())
                if (ShiftFaceEnum.finished(task.shiftFace)) {
                    return@lockExec
                }
                if (event.shiftEnum.finished()) {
                    task.waitSync()
                    task.aiEndTime = LocalDateTime.now()
                }
                task.shiftFace = event.shiftEnum.code
                manualUpdateAiTaskInfo(task)
            }
        } catch (e: Exception) {
            log.error { "监听脸部修复状态失败：\n ${e.stackTraceToString()}" }
        }
    }

    fun checkToPushTryOnTask(task: TryOnTask, clipTask: ClipLabelTask) {
        try {
            lockExec("CheckToPushTryOnTask:${task.taskId}") {
                if (!PushStatusEnum.unPushed(task.pushStatus)) {
                    return@lockExec
                }
                val clipStatus = TaskStatusEnum.of(clipTask.taskStatus!!)
                if (clipStatus.processing()) {
                    return@lockExec
                }
                //失败
                if (!clipStatus.completed()) {
                    handlePushException(
                        task,
                        clipTask.message,
                        clipStatus,
                        AiTaskModeEnum.CLIP_VIT_L_14
                    )
                    return@lockExec
                }
                var labelCategory = clipTask.category
                var extendActions = clipTask.extendActions
                val doTryOnFlatLabel = clipTask.needDoTryOnFlatLabelTask()
                if (doTryOnFlatLabel) {
                    val clipTaskVo = clipLabelTaskService.detail(clipTask.taskId!!)
                    if (TaskStatusEnum.processing(clipTaskVo.taskStatus)) {
                        return@lockExec
                    }
                    if (!TaskStatusEnum.completed(clipTaskVo.taskStatus)) {
                        handlePushException(
                            task,
                            clipTaskVo.message,
                            TaskStatusEnum.of(clipTaskVo.taskStatus!!),
                            AiTaskModeEnum.TRYON_FLAT_LABEL
                        )
                        return@lockExec
                    }
                    labelCategory = clipTaskVo.category
                    extendActions = clipTaskVo.extendActions
                }
                var errorMsg: String? = null
                var failMode: AiTaskModeEnum? = null
                if (labelCategory.isBlank()) {
                    errorMsg = if (doTryOnFlatLabel) {
                        failMode = AiTaskModeEnum.TRYON_FLAT_LABEL
                        "Flat无法识别出品类！"
                    } else {
                        failMode = AiTaskModeEnum.CLIP_VIT_L_14
                        "Clip无法识别出品类！"
                    }
                }
                if (errorMsg.isBlank() && extendActions.disableTryOn()) {
                    errorMsg = if (doTryOnFlatLabel) {
                        failMode = AiTaskModeEnum.TRYON_FLAT_LABEL
                        "Clip识别品类[${labelCategory}]不支持虚拟换装！"
                    } else {
                        failMode = AiTaskModeEnum.CLIP_VIT_L_14
                        "Flat识别品类[${labelCategory}]不支持虚拟换装！"
                    }
                }
                if (errorMsg.isNotBlank()) {
                    handlePushException(task, errorMsg, TaskStatusEnum.FAILED, failMode)
                    return@lockExec
                }
                if (task.category.isBlank()) {
                    task.category = labelCategory
                }
                try {
                    val taskPriority = jvTaskPriorityHandle.modifyTaskPriority(task, task.taskAttribute)
                    RestAiPlatformApi.create(
                        task.taskId!!,
                        TryOnConvert.packTryOnModelReq(task),
                        modeEnum, taskPriority
                    ).also { resp ->
                        if (resp.failed()) {
                            throw RemoteException(resp.message ?: "推送任务失败")
                        }
                    }
                    task.waitSync().firstPushed()
                    manualUpdateAiTaskInfo(task)
                } catch (e: Exception) {
                    log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                    handlePushException(task, e.message ?: "推送任务失败", TaskStatusEnum.FAILED, null)
                }
            }
        } catch (e: Exception) {
            log.error { "检查ClipTask并推送TryOn任务失败：\n【${clipTask.toJson()}】；\n ${e.stackTraceToString()}" }
        }
    }


    private fun handlePushException(
        task: TryOnTask,
        message: String?,
        statusEnum: TaskStatusEnum,
        failMode: AiTaskModeEnum?
    ) {
        task.pushFailed()
        task.failTaskMode = failMode?.fullMode()
        handleException(task, message, statusEnum)
    }

    override fun handleException(task: TryOnTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    override fun handleTimeoutResult(task: TryOnTask, canceled: Boolean) {
        task.waitSync()
        task.failTaskMode = AiTaskModeEnum.TRYON.fullMode()
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return TryOnTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.algorithms = AlgorithmEnum.JV.code
                it.pageNum = curPageNum
                tryOnTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    override fun doSync(task: TryOnTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        tryOnTaskRepository.getByBusId(busId).also {
            clipLabelTaskService.suspendByParentTaskId(it.taskId!!)
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    private fun checkCategory(req: TryOnTaskReq): TryOnTaskReq {
        val fmLabelVo = FmClientApi.obtainLabelInfoByName(req.category!!)
        if (fmLabelVo.disableTryOn()) {
            throw ButtedException("品类[${req.category}]不支持虚拟换装！")
        }
        return req
    }

    fun manualUpdateAiTaskInfo(task: TryOnTask) {
        task.refreshRevisedTime()
        withTranExec {
            tryOnTaskRepository.updateByIdManualFill(task)
            callbackAigcBusiness(task, "try_on")
        }
    }
}
