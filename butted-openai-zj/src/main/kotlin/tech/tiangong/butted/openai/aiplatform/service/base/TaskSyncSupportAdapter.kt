package tech.tiangong.butted.openai.aiplatform.service.base

import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.common.TaskModelOutputVo
import tech.tiangong.butted.openai.config.OpenAiTaskConfig
import tech.tiangong.butted.openai.entity.base.SyncTask
import tech.tiangong.butted.openai.repository.base.ManualBaseRepository

/**
 * TaskSyncSupportAdapter
 */
@Slf4j
abstract class TaskSyncSupportAdapter<T : SyncTask, O : TaskModelOutputVo>(
    config: OpenAiTaskConfig,
    manualBaseRepository: ManualBaseRepository<*, T>
) : TaskSyncSupportImpl<T, O>(
    config,
    manualBaseRepository
), ZjTaskSyncSupport<T> {
    override fun buildTaskModelParams(task: T): Any {
        return "do nothing"
    }

    override fun handlePullResult(
        task: T,
        changeStateEnum: TaskStatusEnum,
        output: O?,
        completed: Bo<PERSON>an,
        message: String?
    ) {
        println("do nothing")
    }

    override fun handleTimeoutResult(task: T, canceled: Boolean) {
        println("do nothing")
    }

    override fun doSync(task: T) {
        println("do nothing")
    }
}


