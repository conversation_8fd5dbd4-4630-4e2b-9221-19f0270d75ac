package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.PoseTaskReq
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.PoseTaskVo
import tech.tiangong.butted.common.vo.TaskSyncStatusVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.PoseProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.PoseModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.pkg.output.PoseOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.PoseConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.entity.PoseTask
import tech.tiangong.butted.openai.repository.PoseTaskRepository
import tech.tiangong.butted.openai.service.PoseTaskService
import tech.tiangong.butted.openai.vo.query.PoseTaskQuery
import tech.tiangong.butted.util.UserInvoke

/**
 * 姿势任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class PoseTaskServiceImpl(
    config: PoseProperties,
    val poseTaskRepository: PoseTaskRepository
) : TaskSyncSupportImpl<PoseTask, PoseOutputVo>(
    config,
    poseTaskRepository
), PoseTaskService {
    init {
        modeEnum = AiTaskModeEnum.POSE
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: PoseTaskReq): Long {
        return PoseConvert.build(req).let {
            poseTaskRepository.save(it)
            doPush(it)
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: PoseTaskReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }

    override fun buildTaskModelParams(task: PoseTask): PoseModelReq {
        return PoseConvert.packPoseModelReq(task)
    }


    override fun detail(taskId: Long): PoseTaskVo {
        val task = poseTaskRepository.obtainById(taskId, "任务不存在")
        return PoseConvert.packPoseTaskVo(task)
    }

    override fun getByBusId(busId: Long): PoseTaskVo {
        return poseTaskRepository.getByBusId(busId).let {
            PoseConvert.packPoseTaskVo(it)
        }
    }

    override fun handlePullResult(
        task: PoseTask,
        changeStateEnum: TaskStatusEnum,
        output: PoseOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.tiltAngle = it.tiltAngle
                task.poseRes = PoseConvert.packPoseInfoDto(it.poseRes)?.toJson()
            }
        }
        if (task.taskStatus != changeStateEnum.code) {
            task.waitSync()
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task)
    }

    override fun handleException(task: PoseTask, message: String?, statusEnum: TaskStatusEnum?) {
        val failStatus = statusEnum ?: TaskStatusEnum.FAILED
        super.handleException(task, message, failStatus)
        manualUpdateAiTaskInfo(task)
    }


    override fun handleTimeoutResult(task: PoseTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task)
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return PoseTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                poseTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    override fun doSync(task: PoseTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = poseTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步姿势任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }


    fun manualUpdateAiTaskInfo(task: PoseTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        if (task.notExistCallback()) {
            task.synced()
        }
        withTranExec {
            poseTaskRepository.updateByIdManualFill(task)
            event?.also {
                afterCommitExec {
                    publishEvent(it)
                }
            }
            checkCallbackBusiness(task)
        }
    }

}
