package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.MarketStyleTaskEvent
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.MarketStyleProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.MarketStyleModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.PredLabelModelVo
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.MarketStyleTaskOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.MarketStyleConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.MarketStyleTask
import tech.tiangong.butted.openai.repository.MarketStyleTaskRepository
import tech.tiangong.butted.openai.service.MarketStyleTaskService
import tech.tiangong.butted.openai.vo.query.MarketStyleTaskQuery
import tech.tiangong.butted.openai.vo.req.MarketStyleTaskBatchCreateReq
import tech.tiangong.butted.openai.vo.req.MarketStyleTaskQueryReq
import tech.tiangong.butted.openai.vo.req.MarketStyleTaskReq
import tech.tiangong.butted.openai.vo.resp.MarketPredLabelsVo
import tech.tiangong.butted.openai.vo.resp.MarketStyleTaskBatchCreateVo
import tech.tiangong.butted.openai.vo.resp.MarketStyleTaskVo

/**
 * 营销风格识别任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class MarketStyleTaskServiceImpl(
    config: MarketStyleProperties,
    val marketStyleTaskRepository: MarketStyleTaskRepository
) : TaskBaseSupportImpl<MarketStyleTask, MarketStyleTaskOutputVo>(
    config,
    marketStyleTaskRepository
), MarketStyleTaskService {
    init {
        modeEnum = AiTaskModeEnum.JV_STYLE_REC
    }
    /**
     * 批量查询任务信息
     */
    override fun qeryByIds(req: MarketStyleTaskQueryReq): List<MarketStyleTaskVo>? {
        val taskList = marketStyleTaskRepository.listByTaskIds(req.taskIds!!)
        val result = mutableListOf<MarketStyleTaskVo>()
        taskList.forEach { task ->
            result.add(
                task.copy(MarketStyleTaskVo::class).apply{
                    val predLabelsInfo = task.predLabels?.parseJsonList(PredLabelModelVo::class.java)
                    this.predLabelsInfo = MarketPredLabelsVo().apply {
                        this.name = predLabelsInfo?.firstOrNull()?.cn?.name
                        this.value = predLabelsInfo?.firstOrNull()?.cn?.values?.first()
                    }
                }
            )
        }
        return result
    }

    override fun detail(taskId: Long): MarketStyleTaskVo? {
        val task = marketStyleTaskRepository.getById(taskId)
        if(task == null){
            return null
        }
        return task.copy(MarketStyleTaskVo::class).apply{
            val predLabelsInfo = task.predLabels?.parseJsonList(PredLabelModelVo::class.java)
            this.predLabelsInfo = MarketPredLabelsVo().apply {
                this.name = predLabelsInfo?.firstOrNull()?.cn?.name
                this.value = predLabelsInfo?.firstOrNull()?.cn?.values?.first()
            }
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: MarketStyleTaskReq): Long {
        return MarketStyleConvert.buildMarketStyleTask(req).let {
            marketStyleTaskRepository.save(it)
            doPush(it)
        }
    }
    @Transactional(rollbackFor = [Exception::class])
    override fun batchCreate(req: MarketStyleTaskBatchCreateReq): List<MarketStyleTaskBatchCreateVo> {
        val result = mutableListOf<MarketStyleTaskBatchCreateVo>()
        req.inputImage?.forEach {

            MarketStyleConvert.buildMarketStyleTask( MarketStyleTaskReq(it)).let {
                marketStyleTaskRepository.save(it)
                doPush(it)
                result.add(MarketStyleTaskBatchCreateVo(it.taskId, it.inputImage))
            }
        }
        return  result
    }
    override fun buildTaskModelParams(task: MarketStyleTask): MarketStyleModelReq {
        return MarketStyleConvert.packMarketStyleModelReq(task)
    }


    override fun handlePullResult(
        task: MarketStyleTask,
        changeStateEnum: TaskStatusEnum,
        output: MarketStyleTaskOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.predLabels = it.predLabels?.toJson()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, MarketStyleTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: MarketStyleTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, MarketStyleTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return MarketStyleTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                marketStyleTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: MarketStyleTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            marketStyleTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
