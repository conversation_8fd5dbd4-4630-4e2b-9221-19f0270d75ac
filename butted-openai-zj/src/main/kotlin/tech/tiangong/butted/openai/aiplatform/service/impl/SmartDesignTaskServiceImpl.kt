package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.req.TaskSyncStatusBatchReq
import tech.tiangong.butted.common.req.TaskSyncStatusReq
import tech.tiangong.butted.common.vo.*
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.common.vo.base.RefImgVo
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.core.toolkit.blankDefault
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.core.toolkit.copyAndApply
import tech.tiangong.butted.core.toolkit.success
import tech.tiangong.butted.enums.*
import tech.tiangong.butted.event.RepairStateEvent
import tech.tiangong.butted.event.SmartDesignTaskEvent
import tech.tiangong.butted.event.SmartIdentifyTaskEvent
import tech.tiangong.butted.exception.ButtedException
import tech.tiangong.butted.exception.RemoteException
import tech.tiangong.butted.jdbc.afterCommitExec
import tech.tiangong.butted.jdbc.withNewTranExec
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.InspirationProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.SmartDesignProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.req.RefImgModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.req.SmartDesignModelReq
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.SmartDesignOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.SmartDesignConvert
import tech.tiangong.butted.openai.aiplatform.exception.TaskException
import tech.tiangong.butted.openai.aiplatform.extend.requirePromiseEnhanced
import tech.tiangong.butted.openai.aiplatform.remote.FmClientApi
import tech.tiangong.butted.openai.aiplatform.remote.RestAiPlatformApi
import tech.tiangong.butted.openai.aiplatform.remote.SmartDevelopApi
import tech.tiangong.butted.openai.aiplatform.service.base.TaskSyncSupportImpl
import tech.tiangong.butted.openai.common.IdentifyExtendAction
import tech.tiangong.butted.openai.common.SmartDesignRequire
import tech.tiangong.butted.openai.entity.FabricRecommendTask
import tech.tiangong.butted.openai.entity.SmartDesignTask
import tech.tiangong.butted.openai.entity.base.SubTask
import tech.tiangong.butted.openai.repository.SmartDesignTaskRepository
import tech.tiangong.butted.openai.service.*
import tech.tiangong.butted.openai.vo.query.SmartDesignTaskQuery
import tech.tiangong.butted.openai.vo.req.PictureCaptionApiReq
import tech.tiangong.butted.openai.vo.req.SmartDesignCreateReq
import tech.tiangong.butted.redis.RedisTemplateHelper
import tech.tiangong.butted.util.UserInvoke
import tech.tiangong.inspiration.common.enums.PromiseResultEnum
import java.time.LocalDateTime

/**
 * 智能设计开款任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class SmartDesignTaskServiceImpl(
    config: SmartDesignProperties,
    val smartDesignTaskRepository: SmartDesignTaskRepository,
    val smartIdentifyTaskService: SmartIdentifyTaskService,
    val smartDesignTaskOutputService: SmartDesignTaskOutputService,
    val pictureCaptionTaskService: PictureCaptionTaskService,
    val inspirationProperties: InspirationProperties,
    val redis: RedisTemplateHelper
) : TaskSyncSupportImpl<SmartDesignTask, SmartDesignOutputVo>(
    config,
    smartDesignTaskRepository
), SmartDesignTaskService {
    init {
        modeEnum = AiTaskModeEnum.SMART_DESIGN
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: SmartDesignCreateReq): Long {
        val smartIdentifyTask = smartIdentifyTaskService.obtainById(req.smartIdentifyId!!)
        val extendAction = try {
            IdentifyExtendAction(FmClientApi.getLabelInfoByName(req.category!!)?.extendLabel)
        } catch (e: Exception) {
            log.error { "获取品类信息失败：${e.stackTraceToString()}" }
            throw ButtedException(e.message ?: "获取品类信息失败")
        }
        val parentTask = if (req.parentBusId != null) {
            smartDesignTaskRepository.obtainById(req.parentBusId!!, "父任务不存在！")
        } else {
            null
        }
        val task = SmartDesignConvert.build(req, parentTask, smartIdentifyTask, extendAction)
        smartDesignTaskRepository.save(task)
        checkTodoExtendActions(task)
        return task.taskId!!
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun manualCreate(req: SmartDesignCreateReq): Long {
        return UserInvoke.doAction(req) {
            create(req)
        }
    }

    override fun detail(taskId: Long): SmartDesignTaskVo {
        val task = smartDesignTaskRepository.obtainById(taskId)
        return packSmartDesignTaskVo(task).apply {
            this.taskStatus = getFullCallbackStatus(task).code
            this.resImgList = findComposeTaskResGroupRepairImgList(task)
        }
    }

    override fun getByBusId(busId: Long): SmartDesignTaskVo {
        val task = smartDesignTaskRepository.getByBusId(busId)
        return packSmartDesignTaskVo(task).apply {
            //转换状态
            this.taskStatus = getFullCallbackStatus(task).code
            this.resImgList = findComposeTaskResGroupRepairImgList(task)
        }

    }

    override fun getByBusIdExcludePromise(busId: Long): SmartDesignTaskVo {
        val task = smartDesignTaskRepository.getByBusId(busId)
        return packSmartDesignTaskVo(task).apply {
            this.taskStatus = getFmCallbackStatus(task).code
            this.resImgList = findTaskResGroupRepairImgList(task)
        }
    }

    private fun packSmartDesignTaskVo(task: SmartDesignTask): SmartDesignTaskVo {
        val require = task.toRequire()
        val identifyInfoVo = smartIdentifyTaskService.obtainIdentifyInfoForDesign(require)
        return task.copy(SmartDesignTaskVo::class).apply {
            refImgType = identifyInfoVo.refImgType
            styleType = identifyInfoVo.styleType
            bgImgInfoList = task.bgImgInfo?.parseJsonList(RefImgVo::class.java)
            modelImgInfoList = task.modelImgInfo?.parseJsonList(RefImgVo::class.java)
            this.identifyCategory = identifyInfoVo.category
            if (task.useIdentifyClipLabel == Bool.YES.code) {
                if (this.identifyCategory == this.category) {
                    this.clipLabelList = identifyInfoVo.clipTask?.predLabelList
                }
            } else {
                this.clipLabelList =
                    task.submitClipLabels?.parseJsonList(PredLabelVo::class.java)
            }
            //不再做风格标签
            /*require.checkTodoStyleLabel {
                styleLabelList = identifyInfoVo.styleTask?.predLabelList
            }*/
            require.checkFlowerPatternLabel {
                flowerPatternLabelList = identifyInfoVo.flowerTask?.predLabelList
            }
            require.checkFabricRecommend {
                identifyInfoVo.fabricTask?.also {
                    fabricLabel = it.fabricLabel
                    recommendFabricList = it.recommendFabricList
                }
            }
            calculateDuration()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByBusId(busId: Long) {
        val task = smartDesignTaskRepository.getByBusId(busId)
        lockExec(task.taskId) {
            if (TaskStatusEnum.processing(task.taskStatus)) {
                invokeCancelAiTask(task.taskId!!)
            }
            //同步到业务端
            task.waitSync().suspend()
            task.message = "用户终止任务"
            manualUpdateAiTaskInfo(task)
        }

    }

    override fun handlePull(taskId: Long): Boolean {
        try {
            lockExec(taskId) {
                val task = obtainById(taskId)
                //检查脸部修复是否完成
                if (TaskStatusEnum.completed(task.taskStatus)) {
                    val faceRepair = handleRepairProgress(task).apply {
                        println("智能设计任务[${task.taskId}]脸部修复进度：${this.desc}")
                    }
                    if (faceRepair.notRequired() || faceRepair.finished()) {
                        handlePromiseProgress(task)
                    }
                    return@lockExec
                }
                //检查推送
                if (task.pushStatus == PushStatusEnum.UN_PUSH.code) {
                    smartIdentifyTaskService.obtainById(task.smartIdentifyId!!).also {
                        checkToPushSmartDesignTask(SmartIdentifyTaskEvent<SubTask>(it), taskId)
                    }
                    return@lockExec
                }
                //检查超时
                if (task.checkTime() < LocalDateTime.now().minusSeconds(config.timeout + config.pullInterval)) {
                    handleTimeout(task)
                    return@lockExec
                }
                doHandlePull(taskId)
            }
            return true
        } catch (e: Exception) {
            log.error { "【${modeEnum.name}】任务Handle失败：\n ${e.stackTraceToString()}" }
            return false
        }

    }


    fun handlePromiseProgress(task: SmartDesignTask): PromiseResultEnum {
        try {//需要做履约增强，查询履约增强状态
            val curPromiseResult = PromiseResultEnum.of(task.promiseState, PromiseResultEnum.NOT_REQUIRED)
            if (!curPromiseResult.processing()) {
                return curPromiseResult
            }
            val checkPromiseResult = SmartDevelopApi.checkPromiseAndRedoEnhanced(task.busId!!)
            task.promiseState = checkPromiseResult.code
            task.waitSync()
            manualUpdateAiTaskInfo(task)
            return checkPromiseResult
        } catch (e: Exception) {
            log.error { "【${task.taskId}】任务检查面料履约状态失败：\n ${e.stackTraceToString()}" }
            task.message = e.message
            manualUpdateAiTaskInfo(task)
            return PromiseResultEnum.PROCESSING
        }

    }

    fun handleRepairProgress(task: SmartDesignTask): FaceRepairEnum {
        val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
        if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
            return curFaceRepair
        }
        val checkRepairEnum: FaceRepairEnum =
            smartDesignTaskOutputService.checkRepairProgress(task.taskId!!) ?: FaceRepairEnum.NO
        task.faceRepair = checkRepairEnum.code
        if (checkRepairEnum.notRequired() || checkRepairEnum.finished()) {
            task.waitSync()
            task.aiEndTime = LocalDateTime.now()
        }
        manualUpdateAiTaskInfo(task)
        return checkRepairEnum
    }


    override fun handlePullResult(
        task: SmartDesignTask,
        changeStateEnum: TaskStatusEnum,
        output: SmartDesignOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        withTranExec {
            var checkStateEnum = changeStateEnum
            if (completed) {
                if (output == null || output.resImgs.isEmpty()) {
                    checkStateEnum = TaskStatusEnum.FAILED
                    task.message = "AI生图结果为空"
                } else {

                    redis.setValue("SmartDesignTaskServiceImpl-SmartDesignTask:" + task.taskId, task.toJson(), 1800)
                    val outputReq = SmartDesignConvert.packSmartDesignTaskOutputReq(task, output.resImgs!!)
                    smartDesignTaskOutputService.manualSave(outputReq)
                    task.prompts = output.prompts
                    task.modelVersion = output.modelVersion
                    task.taskProgress = 100
                    if (task.needFaceRepair()) {
                        task.faceRepair = FaceRepairEnum.REPAIRING.code
                    }
                }
            }
            if (task.taskStatus != checkStateEnum.code) {
                task.waitSync()
            }
            task.taskStatus = checkStateEnum.code
            if (checkStateEnum.failedOrCanceled()) {
                task.failTaskMode = AiTaskModeEnum.SMART_DESIGN.fullMode()
            }
            manualUpdateAiTaskInfo(task)
        }
    }

    override fun handleTimeoutResult(task: SmartDesignTask, canceled: Boolean) {
        task.waitSync()
        task.failTaskMode = AiTaskModeEnum.SMART_DESIGN.fullMode()
        manualUpdateAiTaskInfo(task)
    }

    @Async
    @EventListener
    override fun listenSmartIdentifyTask(event: SmartIdentifyTaskEvent<*>) {
        checkToPushSmartDesignTask(event)
    }

    @Async
    @EventListener
    @Transactional(rollbackFor = [Exception::class])
    override fun listenRepairStateEvent(event: RepairStateEvent) {
        try {
            if (event.sourceEnum != SourceEnum.A_SMART_DESIGN) {
                return
            }
            lockExec(event.getTaskId()) {
                val task = obtainById(event.getTaskId())
                val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
                if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
                    return@lockExec
                }
                if (event.repairEnum.finished()) {
                    task.waitSync()
                    task.aiEndTime = LocalDateTime.now()
                }
                task.faceRepair = event.repairEnum.code
                manualUpdateAiTaskInfo(task)
            }
        } catch (e: Exception) {
            log.error { "监听脸部修复状态失败：\n ${e.stackTraceToString()}" }
        }
    }

    fun checkToPushSmartDesignTask(event: SmartIdentifyTaskEvent<*>, pullTaskId: Long? = null) {
        val smartIdentifyTask = event.getTask()
        try {
            lockExec("CheckToPushSmartDesignTask:${smartIdentifyTask.taskId}") {
                val smartIdentifyStatus = event.taskStatus
                val extendTask = event.extendTask
                val extendTaskStatus = extendTask?.let { TaskStatusEnum.of(it.taskStatus!!) }
                if (smartIdentifyStatus.processing() || extendTaskStatus?.processing() == true) {
                    return@lockExec
                }
                val category = if (extendTask is FabricRecommendTask) {
                    extendTask.category
                } else {
                    null
                }
                val designTaskList = smartDesignTaskRepository.listEntityByQuery {
                    SmartDesignTaskQuery().apply {
                        this.smartIdentifyId = smartIdentifyTask.taskId!!
                        this.pushStatus = PushStatusEnum.UN_PUSH.code
                        this.category = category
                    }
                }
                if (designTaskList.isEmpty()) {
                    return@lockExec
                }
                if (!smartIdentifyStatus.completed() || (extendTaskStatus != null && !extendTaskStatus.completed())) {
                    designTaskList.forEach {
                        checkLockExec(it.taskId, it.taskId != pullTaskId) {
                            it.taskStatus = smartIdentifyStatus.code
                            it.message = smartIdentifyTask.message
                            it.failTaskMode = smartIdentifyTask.failTaskMode
                            if (extendTaskStatus != null) {
                                it.taskStatus = extendTaskStatus.code
                                it.message = extendTask.message
                                event.extendTaskMode?.also { mode ->
                                    it.failTaskMode = mode.fullMode()
                                }
                            }
                            it.pushStatus = PushStatusEnum.PUSH_FAIL.code
                            it.waitSync().stopAiEndTime()
                            manualUpdateAiTaskInfo(it)
                        }
                    }
                    return@lockExec
                }
                //推送任务到调度平台
                designTaskList.forEach { toT ->
                    checkLockExec(toT.taskId, toT.taskId != pullTaskId) {
                        val curTask = obtainById(toT.taskId!!)
                        try {
                            if (!PushStatusEnum.unPushed(curTask.pushStatus)) {
                                return@checkLockExec
                            }
                            withTranExec {
                                checkSceneCaptions(curTask)
                                checkModelMaterialCaptions(curTask)
                                if (checkToPush(curTask)) {
                                    curTask.firstPushed()
                                }
                                curTask.waitSync()
                                manualUpdateAiTaskInfo(curTask)
                            }
                        } catch (e: Exception) {
                            log.error { "推送任务失败：${e.stackTraceToString()}" }
                            curTask.waitSync().pushFailed()
                            curTask.taskStatus = TaskStatusEnum.FAILED.code
                            curTask.message = e.message ?: "推送任务失败"
                            if (e is TaskException) {
                                curTask.failTaskMode = e.modeEnum?.fullMode()
                            }
                            manualUpdateAiTaskInfo(curTask)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            log.error { "监听智能识别任务失败：\n【${smartIdentifyTask.toJson()}】；\n ${e.stackTraceToString()}" }
        }


    }


    @Throws(Exception::class)
    fun checkSceneCaptions(task: SmartDesignTask) {
        val sceneInfo = task.bgImgInfo?.parseJsonList(RefImgModelReq::class.java)?.firstOrNull { it.valid() }
        if (sceneInfo == null || sceneInfo.captions.isNotBlank()) {
            return
        }
        val apiReq = PictureCaptionApiReq(source = SourceEnum.A_SCENE_PICTURE, inputImg = sceneInfo.url!!)
        val captionText = pictureCaptionTaskService.callCaptionApi(apiReq)
        if (captionText.isBlank()) {
            throw TaskException(
                "场景图描述任务未生成结果",
                AiTaskModeEnum.CHAT_GPT_PICTURE_CAPTION
            )
        }
        sceneInfo.captions = captionText
        task.bgImgInfo = listOf(sceneInfo).toJson()
        /*//已经创建图片描述任务
        if (pictureCaptionTaskService.checkHasTaskByBusId(task.taskId!!)) {
            val pictureCaption = pictureCaptionTaskService.getByBusId(task.taskId!!)
            if (TaskStatusEnum.processing(pictureCaption.taskStatus)) {
                //等待图片描述任务完成
                return true
            }
            if (!TaskStatusEnum.completed(pictureCaption.taskStatus)) {
                //图片描述任务失败
                throw TaskException(
                    pictureCaption.message ?: "图片描述任务失败",
                    AiTaskModeEnum.PICTURE_CAPTION,
                    TaskStatusEnum.of(pictureCaption.taskStatus!!)
                )
            }
            if (pictureCaption.caption.isBlank()) {
                throw TaskException(
                    "图片描述任务未生成结果",
                    AiTaskModeEnum.PICTURE_CAPTION,
                    TaskStatusEnum.of(pictureCaption.taskStatus!!)
                )
            }
            sceneInfo.captions = pictureCaption.caption
            task.bgImgInfo = listOf(sceneInfo).toJson()
            return false
        }
        val captionReq = PictureCaptionTaskManualCreateReq().apply {
            this.busId = task.taskId
            this.inputImg = sceneInfo.url
            this.sourceEnum = SourceEnum.A_SCENE_PICTURE
            this.tenantId = task.tenantId
            this.creatorId = task.creatorId
            this.creatorName = task.creatorName
        }
        pictureCaptionTaskService.manualCreate(captionReq)*/
        //return true

    }


    @Throws(Exception::class)
    fun checkModelMaterialCaptions(task: SmartDesignTask) {
        if (task.modelMaterialUrl.isBlank() || task.modelMaterialCaption.isNotBlank()) {
            return
        }
        val apiReq = PictureCaptionApiReq(source = SourceEnum.A_MODEL_MATERIAL, inputImg = task.modelMaterialUrl!!)
        val captionText = pictureCaptionTaskService.callCaptionApi(apiReq)
        if (captionText.isBlank()) {
            throw TaskException(
                "模特素材图描述任务未生成结果",
                AiTaskModeEnum.CHAT_GPT_PICTURE_CAPTION
            )
        }
        task.modelMaterialCaption = captionText
    }


    @Throws(Exception::class)
    fun checkToPush(task: SmartDesignTask): Boolean {
        val require = task.toRequire()
        val identifyVo = smartIdentifyTaskService.obtainIdentifyInfoForDesign(require)
        var designExtendActionFinish = true
        //去掉风格标签处理，since 2025年4月14日18:42:43
        /*require.checkTodoStyleLabel {
            if (!checkSubTaskFinished(identifyVo.styleTask, AiTaskModeEnum.CLIP_STYLE_LABEL, "款式标签识别失败")) {
                designExtendActionFinish = false
            }
        }*/
        require.checkFlowerPatternLabel {
            if (!checkSubTaskFinished(
                    identifyVo.flowerTask,
                    AiTaskModeEnum.CLIP_FLOWER_PATTERN_LABEL,
                    "花型标签识别失败"
                )
            ) {
                designExtendActionFinish = false
            }
        }
        require.checkFabricRecommend {
            if (!checkSubTaskFinished(identifyVo.fabricTask, AiTaskModeEnum.FABRIC_RECOMMEND, "面料推荐识别失败")) {
               // designExtendActionFinish = false
            }
        }
        if (!designExtendActionFinish) {
            //等待识别完成
            return false
        }
        val smartDesignModelReq = SmartDesignConvert.packSmartDesignModelReq(task, identifyVo, require)
        val taskPriority = jvTaskPriorityHandle.modifyTaskPriority(task, task.taskAttribute)
        RestAiPlatformApi.create(
            task.taskId!!,
            smartDesignModelReq,
            modeEnum,
            taskPriority
        ).also { resp ->
            if (resp.failed()) {
                log.error { "${modeEnum.name}任务[${task.taskId}],推送失败：${task.toJson()}" }
                throw RemoteException(resp.message ?: "推送任务失败")
            }
        }
        return true
    }


    override fun doSync(task: SmartDesignTask) {
        if (task.needAsync()) {
            manualUpdateAiTaskInfo(task)
        }
    }


    override fun batchCheckSyncStatus(req: TaskSyncStatusBatchReq) {
        val taskList = smartDesignTaskRepository.listByBusId(req.busIdList!!)
        taskList.forEach { task ->
            try {
                TaskSyncStatusReq(
                    busId = task.busId!!,
                    taskMode = req.taskMode!!,
                    taskStatus = req.taskStatus!!,
                ).run {
                    checkSyncStatus(this)
                }
            } catch (e: Exception) {
                log.error { "批量检查智能设计任务同步状态失败：【${task.busId}】； ${e.stackTraceToString()}" }
            }
        }
    }

    override fun checkSyncStatus(req: TaskSyncStatusReq): TaskSyncStatusVo? {
        val task = smartDesignTaskRepository.getByBusId(req.busId!!)
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus == req.taskStatus) {
            return req.copy(TaskSyncStatusVo::class)
        }
        log.info { "同步智能设计任务[${req.busId}]状态：业务端状态[${req.taskStatus}] ——> AI当前状态[$curStatus]" }
        executor.delayExecute(1000L) {
            withNewTranExec {
                //更改同步状态为待同步
                task.waitSync()
                manualUpdateAiTaskInfo(task)
            }
        }
        return req.copyAndApply(TaskSyncStatusVo::class) {
            this.taskStatus = curStatus
        }
    }

    /**
     * 手动同步任务状态到业务端
     */
    override fun manualSyncTask(taskIdList: List<Long>?): Int {
        if (taskIdList.isEmpty()) {
            return 0
        }
        val taskList = smartDesignTaskRepository.listByIds(taskIdList!!)
        taskList.forEach { task ->
            withNewTranExec {
                try {
                    task.waitSync()
                    manualUpdateAiTaskInfo(task)
                } catch (e: Exception) {
                    log.error { "手动同步智能设计任务失败：\n【${task.taskId}】；\n ${e.stackTraceToString()}" }
                }
            }
        }
        return taskList.size
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return SmartDesignTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                smartDesignTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }

    fun manualUpdateAiTaskInfo(task: SmartDesignTask) {
        task.refreshRevisedTime()
        withTranExec {
            smartDesignTaskRepository.updateByIdManualFill(task)
            callbackBusiness(task)
            notifyTaskQueue(task)
        }
    }

    fun notifyTaskQueue(task: SmartDesignTask) {
        try {
            afterCommitExec {
                //履约增强AI设计任务，不通知
                if (task.source == SourceEnum.S_S_PROMISE_SMART_DESIGN.fullSource()) {
                    return@afterCommitExec
                }
                val cloneTask = task.copy(SmartDesignTask::class)
                cloneTask.taskStatus = getFullCallbackStatus(task).code
                publishEvent(SmartDesignTaskEvent(cloneTask, TaskStatusEnum.of(cloneTask.taskStatus!!)))
            }
        } catch (e: Exception) {
            log.error { "NotifyTaskQueue error: ${e.message}" }
        }
    }


    /*private fun getInspirationCallBackUrl(callback: String?): String {
        if (callback.isBlank()) {
            return inspirationProperties.defaultCallbackUrl()
        }
        return callback!!.split(Constant.COMMA).firstOrNull {
            !it.contains("ola-api")
        } ?: inspirationProperties.defaultCallbackUrl()

    }*/


    /*private fun getSdpCallBackUrl(callback: String?): String? {
        if (callback.isBlank()) {
            return null
        }
        val callbackList = callback!!.split(Constant.COMMA)
        var olaCallback = callbackList.firstOrNull {
            it.trim().isNotBlank() && it.contains("ola-api")
        }
        if (olaCallback.isBlank() && callbackList.size > 1) {
            olaCallback = callbackList[1]
        }
        return olaCallback
    }*/


    //https://qa2-fashion-api.tiangong.tech/inspiration/ai-task/callback,https://qa-ola-api.tiangong.tech/sdp-curation/inner/v1/inspiration/callback/ai/design
    //https://prod-nest-api.tiangong.tech/inspiration/ai-task/callback,https://prod-ola-api.tiangong.tech/sdp-curation/inner/v1/inspiration/callback/ai/design
    private fun getInspirationCallBackUrl(callback: String?): String {
        if (callback.isBlank()) {
            return inspirationProperties.defaultCallbackUrl()
        }
        return callback!!.split(Constant.COMMA).firstOrNull() ?: inspirationProperties.defaultCallbackUrl()
    }


    private fun getSdpCallBackUrl(callback: String?): String? {
        if (callback.isBlank()) {
            return null
        }
        val callbackList = callback!!.split(Constant.COMMA)
        if (callbackList.size > 1) {
            return callbackList[1]
        }
        return null
    }


    fun cloneCallbackTask(task: SmartDesignTask, statusEnum: TaskStatusEnum): SmartDesignTask {
        return SmartDesignTask().apply {
            this.taskId = task.taskId
            this.busId = task.busId
            this.busCode = task.busCode
            this.taskStatus = statusEnum.code
            this.taskProgress = task.taskProgress
            this.message = task.message
            this.creatorId = task.creatorId
            this.creatorName = task.creatorName
            this.tenantId = task.tenantId
        }
    }

    fun callbackToInspiration(task: SmartDesignTask) {
        if (task.syncStatus == Bool.YES.code) {
            return
        }
        afterCommitExec {
            try {
                lockExec("SmartDesign:Callback:${task.taskId}") {
                    val callbackUrl = getInspirationCallBackUrl(task.callback)
                    val cloneTask = cloneCallbackTask(task, getFmCallbackStatus(task))
                    RestAiPlatformApi.callback(cloneTask, modeEnum, callbackUrl.trim()).also {
                        super.fillSyncStatus(task, modeEnum, it)
                    }
                    smartDesignTaskRepository.updateByIdManualFill(task)
                }
            } catch (e: Exception) {
                log.error { "回调[callbackToInspiration]任务失败：${e.stackTraceToString()}" }
            }
        }

    }


    fun callbackToSdp(task: SmartDesignTask) {
        if (task.syncSdpStatus == Bool.YES.code) {
            return
        }
        afterCommitExec {
            try {
                lockExec("SmartDesign:Callback:${task.taskId}") {
                    val callbackUrl = getSdpCallBackUrl(task.callback)
                    //不需要回调SDP，更新SDP同步状态为已同步
                    if (callbackUrl.isBlank()) {
                        task.syncSdpStatus = Bool.YES.code
                        smartDesignTaskRepository.updateByIdManualFill(task)
                        return@lockExec
                    }
                    val cloneTask = cloneCallbackTask(task, getSdpCallbackStatus(task))
                    val response = RestAiPlatformApi.callback(cloneTask, modeEnum, callbackUrl!!.trim())
                    if (response.success()) {
                        log.info { "同步[${modeEnum.name}]任务成功,${task.taskId}" }
                        task.syncSdpSuccess()
                    } else {
                        log.info { "同步[${modeEnum.name}]任务失败,${task.taskId}" }
                        task.syncFail()
                        task.message =
                            response.message.blankDefault("同步[${modeEnum.name}]任务失败,${task.taskId}")
                    }
                    smartDesignTaskRepository.updateByIdManualFill(task)
                }
            } catch (e: Exception) {
                log.error { "回调[callbackToSdp]任务失败：${e.stackTraceToString()}" }
            }

        }
    }

    /**
     * 事务提交后回调业务
     */
    override fun callbackBusiness(task: SmartDesignTask) {
        try {
            if (task.busId == null) {
                return
            }
            callbackToInspiration(task)
            callbackToSdp(task)
        } catch (e: Exception) {
            log.error { "同步[${modeEnum.name}]任务失败：\n${e.stackTraceToString()}" }
        }
    }


    private fun checkTodoExtendActions(task: SmartDesignTask) {
        try {
            smartIdentifyTaskService.checkTodoExtendActions(task.toRequire())
        } catch (e: Exception) {
            log.error { "checkTodoExtendActions error: ${e.message}" }
            throw e
        }
    }


    override fun handleException(task: SmartDesignTask, message: String?, statusEnum: TaskStatusEnum?) {
        super.handleException(task, message, statusEnum)
        task.waitSync()
        manualUpdateAiTaskInfo(task)
    }

    private fun checkSubTaskFinished(subTask: BaseTaskVo?, modeEnum: AiTaskModeEnum, defaultMsg: String): Boolean {
        if (subTask == null) {
            return false
        }
        val taskStatus = subTask.taskStatus!!
        return when {
            TaskStatusEnum.processing(taskStatus) -> false
            TaskStatusEnum.completed(taskStatus) -> true
            else -> throw TaskException(
                subTask.message ?: defaultMsg, modeEnum,
                TaskStatusEnum.of(subTask.taskStatus!!)
            )
        }
    }

    fun SmartDesignTask.toRequire(): SmartDesignRequire {
        return SmartDesignRequire(
            smartIdentifyId = this.smartIdentifyId!!,
            category = this.category!!,
            extendActions = this.extendActions,
            styleType = this.styleType
        )
    }

    fun findComposeTaskResGroupRepairImgList(task: SmartDesignTask): List<ResGroupRepairImgVo> {
        val statusEnum = getFullCallbackStatus(task)
        if (!statusEnum.completed()) {
            return listOf()
        }
        val taskIdList = mutableListOf(task.taskId!!)
        if (task.requirePromiseEnhanced()) {
            taskIdList.addAll(
                smartDesignTaskRepository.findTaskIdByParentId(
                    listOf(task.taskId!!),
                    SourceEnum.S_S_PROMISE_SMART_DESIGN
                )
            )
        }
        return smartDesignTaskOutputService.findComposeTaskResGroupRepairImgList(taskIdList)
    }

    fun findTaskResGroupRepairImgList(task: SmartDesignTask): List<ResGroupRepairImgVo> {
        val statusEnum = getFmCallbackStatus(task)
        if (!statusEnum.completed()) {
            return listOf()
        }
        return smartDesignTaskOutputService.findTaskResGroupRepairImgList(task.taskId!!)
    }

    fun getSdpCallbackStatus(task: SmartDesignTask): TaskStatusEnum {
        return getFullCallbackStatus(task)
    }

    fun getFullCallbackStatus(task: SmartDesignTask): TaskStatusEnum {
        val curStatus = getFmCallbackStatus(task)
        if (curStatus != TaskStatusEnum.COMPLETED) {
            return curStatus
        }
        // 如果当前状态是已完成，则查询履约结果
        val curPromiseResult = PromiseResultEnum.of(task.promiseState, PromiseResultEnum.NOT_REQUIRED)
        if (!curPromiseResult.processing()) {
            return curStatus
        }
        return TaskStatusEnum.GENERATING
    }

    fun getFmCallbackStatus(task: SmartDesignTask): TaskStatusEnum {
        val curStatus = TaskStatusEnum.of(task.taskStatus!!)
        if (curStatus != TaskStatusEnum.COMPLETED) {
            return curStatus
        }
        //如果不需要修复脸部，或者修复完成，直接返回
        val curFaceRepair = FaceRepairEnum.of(task.faceRepair, FaceRepairEnum.NO)
        if (curFaceRepair.notRequired() || curFaceRepair.finished()) {
            return curStatus
        }
        return TaskStatusEnum.GENERATING
    }

    override fun buildTaskModelParams(task: SmartDesignTask): SmartDesignModelReq {
        throw UnsupportedOperationException("已经重写推送逻辑！")
    }

}
