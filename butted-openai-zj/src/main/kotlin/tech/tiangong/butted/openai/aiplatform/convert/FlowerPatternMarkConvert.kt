package tech.tiangong.butted.openai.aiplatform.convert

import tech.tiangong.butted.openai.aiplatform.aimodel.req.FlowerPatternMarkModelReq
import tech.tiangong.butted.core.constant.Constant
import tech.tiangong.butted.oss.toFullImgPath
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.openai.convert.BaseConvert
import tech.tiangong.butted.openai.entity.FlowerPatternMarkTask
import tech.tiangong.butted.openai.vo.req.FlowerPatternMarkTaskReq
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j

@Slf4j
object FlowerPatternMarkConvert : BaseConvert {

    fun buildFlowerPatternMarkTask(req: FlowerPatternMarkTaskReq): FlowerPatternMarkTask {
        return FlowerPatternMarkTask().apply {
            this.initBase()
            this.refImgUrl = req.refImgUrl.toFullImgPath()
            this.targetSize = "${req.refImgWidth}${Constant.X}${req.refImgHeight}"
            this.action = AiTaskModeEnum.FLOWER_PATTERN_MARK.action
            this.needImage = Bool.NO.code
        }
    }

    fun packFlowerPatternMarkModelReq(task: FlowerPatternMarkTask): FlowerPatternMarkModelReq {
        return FlowerPatternMarkModelReq(
            taskId = task.taskId,
            refImgUrl = task.refImgUrl,
            action = task.action,
            targetSize = task.targetSize,
            needImage = task.needImage,
        )
    }

}

