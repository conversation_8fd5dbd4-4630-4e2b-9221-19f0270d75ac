package tech.tiangong.butted.openai.aiplatform.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.enums.TaskStatusEnum
import tech.tiangong.butted.common.vo.PredLabelVo
import tech.tiangong.butted.common.vo.base.BaseTaskVo
import tech.tiangong.butted.core.toolkit.copy
import tech.tiangong.butted.enums.AiTaskModeEnum
import tech.tiangong.butted.enums.TaskActionEnum
import tech.tiangong.butted.event.OpenaiTaskEvent
import tech.tiangong.butted.event.StyleLabelTaskEvent
import tech.tiangong.butted.jdbc.withTranExec
import tech.tiangong.butted.openai.aiplatform.aimodel.properties.StyleLabelProperties
import tech.tiangong.butted.openai.aiplatform.aimodel.resp.output.StyleLabelOutputVo
import tech.tiangong.butted.openai.aiplatform.convert.StyleLabelConvert
import tech.tiangong.butted.openai.aiplatform.service.base.TaskBaseSupportImpl
import tech.tiangong.butted.openai.entity.StyleLabelTask
import tech.tiangong.butted.openai.repository.StyleLabelTaskRepository
import tech.tiangong.butted.openai.service.StyleLabelTaskService
import tech.tiangong.butted.openai.vo.query.StyleLabelTaskQuery
import tech.tiangong.butted.openai.vo.req.StyleLabelTaskReq
import tech.tiangong.butted.openai.vo.resp.StyleLabelTaskVo

/**
 * 风格标签任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
class StyleLabelTaskServiceImpl(
    config: StyleLabelProperties,
    val styleLabelTaskRepository: StyleLabelTaskRepository
) : TaskBaseSupportImpl<StyleLabelTask, StyleLabelOutputVo>(
    config,
    styleLabelTaskRepository
), StyleLabelTaskService {
    init {
        modeEnum = AiTaskModeEnum.CLIP_STYLE_LABEL
    }


    override fun detail(taskId: Long): StyleLabelTaskVo? {
        val task = styleLabelTaskRepository.getById(taskId)
        return task?.copy(StyleLabelTaskVo::class)?.apply {
            predLabelList = task.usableLabels?.parseJsonList(PredLabelVo::class.java)
        }
    }

    override fun getByParentTaskId(parentTaskId: Long): StyleLabelTaskVo? {
        val task = styleLabelTaskRepository.getByParentTaskId(parentTaskId)
        return task?.copy(StyleLabelTaskVo::class)?.apply {
            predLabelList = task.usableLabels?.parseJsonList(PredLabelVo::class.java)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun suspendByParentTaskId(parentTaskId: Long) {
        styleLabelTaskRepository.getByParentTaskId(parentTaskId)?.also {
            if (TaskStatusEnum.processing(it.taskStatus)) {
                userSuspendTask(it)
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun create(req: StyleLabelTaskReq): Long {
        return StyleLabelConvert.buildStyleLabelTask(req).let {
            styleLabelTaskRepository.save(it)
            doPush(it)
        }
    }

    override fun buildTaskModelParams(task: StyleLabelTask): Any {
        return StyleLabelConvert.packStyleLabelModelReq(task)
    }

    override fun handlePullResult(
        task: StyleLabelTask,
        changeStateEnum: TaskStatusEnum,
        output: StyleLabelOutputVo?,
        completed: Boolean,
        message: String?
    ) {
        if (completed) {
            output?.let {
                task.predLabels = it.predLabels?.toJson()
                task.usableLabels = it.toPredLabelDtoListJsonStr()
            }
        }
        task.taskStatus = changeStateEnum.code
        manualUpdateAiTaskInfo(task, StyleLabelTaskEvent(task, changeStateEnum))
    }

    override fun handleTimeoutResult(task: StyleLabelTask, canceled: Boolean) {
        manualUpdateAiTaskInfo(task, StyleLabelTaskEvent(task, TaskStatusEnum.TIMEOUT_FAILED))
    }

    override fun loopPage(taskAction: TaskActionEnum, curPageNum: Int): IPage<out BaseTaskVo> {
        return StyleLabelTaskQuery().let {
            if (it.adaptActionQuery(taskAction)) {
                it.pageNum = curPageNum
                styleLabelTaskRepository.findPage(it)
            } else {
                emptyPage()
            }
        }
    }


    fun manualUpdateAiTaskInfo(task: StyleLabelTask, event: OpenaiTaskEvent<*>? = null) {
        task.refreshRevisedTime()
        withTranExec {
            styleLabelTaskRepository.updateByIdManualFill(task)
            afterPublishEvent(event)
        }
    }
}
