plugins {
    alias(commonLibs.plugins.google.ksp)
//    alias(commonLibs.plugins.kapt)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
}

dependencies {
    implementation(projects.buttedCommon)
    implementation(projects.buttedOpenai)
    implementation(commonLibs.hutool.core)
    implementation(commonLibs.hutool.extra)
    implementation(commonLibs.okhttp)

}

